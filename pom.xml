<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.basiscotton.trade</groupId>
	<artifactId>basiscotton-trade-service-system</artifactId>
	<packaging>war</packaging>
	<version>3.0.0</version>

	<parent>
		<groupId>org.harry.dandelionframework</groupId>
		<artifactId>dandelion-framework-boot</artifactId>
		<version>2.3.0</version>
	</parent>

  <dependencies>
		<dependency>
			<groupId>org.harry</groupId>
			<artifactId>dandelion-system-client</artifactId>
		</dependency>

		<dependency>
			<groupId>org.harry.dandelionframework</groupId>
			<artifactId>dandelion-framework-lock-redis</artifactId>
		</dependency>

		<dependency>
		  <groupId>org.springframework.boot</groupId>
		  <artifactId>spring-boot-starter-test</artifactId>
		  <exclusions>
			  <exclusion>
				  <groupId>org.springframework.boot</groupId>
				  <artifactId>spring-boot-starter-logging</artifactId>
			  </exclusion>
		  </exclusions>
		  <scope>test</scope>
		</dependency>
	  <dependency>
		  <groupId>org.harry.dandelionframework</groupId>
		  <artifactId>dandelion-framework-document-actions</artifactId>
	  </dependency>

	  <dependency>
		  <groupId>org.harry.dandelionframework</groupId>
		  <artifactId>dandelion-framework-scheduler-starter</artifactId>
	  </dependency>

	  <dependency>
		  <groupId>org.harry.dandelionframework</groupId>
		  <artifactId>dandelion-framework-idgenerator</artifactId>
	  </dependency>

	  <dependency>
		  <groupId>org.harry.dandelionframework</groupId>
		  <artifactId>dandelion-framework-tomcat-starter</artifactId>
		  <type>pom</type>
		  <scope>provided</scope>
	  </dependency>

	  <dependency>
		  <groupId>org.harry.dandelionframework</groupId>
		  <artifactId>dandelion-fms-sdk</artifactId>
	  </dependency>

	  <dependency>
		  <groupId>org.harry.dandelionframework</groupId>
		  <artifactId>dandelion-signature-client</artifactId>
	  </dependency>
	  <!-- caffeine缓存 -->
	  <dependency>
		  <groupId>com.github.ben-manes.caffeine</groupId>
		  <artifactId>guava</artifactId>
		  <version>3.1.8</version>
	  </dependency>
	  <!-- netty通信组件 -->
	  <dependency>
		  <groupId>io.netty</groupId>
		  <artifactId>netty-all</artifactId>
		  <version>4.1.87.Final</version>
	  </dependency>


	  <dependency>
		  <groupId>com.cottoneasy.uc</groupId>
		  <artifactId>uc-sdk</artifactId>
		  <version>3.0.0</version>
		  <exclusions>
			  <exclusion>
				  <groupId>com.fasterxml.jackson.dataformat</groupId>
				  <artifactId>jackson-dataformat-cbor</artifactId>
			  </exclusion>
		  </exclusions>
	  </dependency>
	  <!--仓储中心-->
	  <dependency>
		  <groupId>com.cottoneasy</groupId>
		  <artifactId>storage-system-local-sdk</artifactId>
		  <version>3.0.0</version>
	  </dependency>
	  <!--金融中心-->
	  <dependency>
		  <groupId>com.cottoneasy</groupId>
		  <artifactId>finance-sdk</artifactId>
		  <version>3.0.1</version>
	  </dependency>
	  <!--结算中心-->
	  <dependency>
		  <groupId>com.sinosoft</groupId>
		  <artifactId>cnce-sc-api-sdk</artifactId>
		  <version>3.0.0</version>
          <exclusions>
              <exclusion>
                  <groupId>com.sinosoft</groupId>
                  <artifactId>cnce-sc-base-vo</artifactId>
              </exclusion>
          </exclusions>
	  </dependency>

      <dependency>
          <groupId>com.sinosoft</groupId>
          <artifactId>cnce-sc-base-vo</artifactId>
          <version>3.0.1</version>
      </dependency>

	  <!--监控-->
	  <dependency>
		  <groupId>com.cottoneasy.monitor</groupId>
		  <artifactId>monitor-sdk</artifactId>
		  <version>3.0.0</version>
		  <exclusions>
			  <exclusion>
				  <groupId>com.fasterxml.jackson.dataformat</groupId>
				  <artifactId>jackson-dataformat-cbor</artifactId>
			  </exclusion>
		  </exclusions>
	  </dependency>

  </dependencies>

   <!-- 发布服务器配置 -->
	<distributionManagement>
		<!-- 生产环境 -->
		<repository>
			<id>dandelion-releases</id>
			<name>Releases</name>
			<url>http://101.35.227.2:8081/repository/maven-releases/</url>
		</repository>

		<!-- 灾备环境 -->
		<!--<repository>
			<id>cnce-zb-releases</id>
			<name>Releases</name>
			<url>http://118.1.10.15:8081/repository/maven-releases/</url>
		</repository>-->

		<!-- 开发环境-jenkins部署使用 -->
		<!--<repository>
			<id>dandelion-releases</id>
			<name>Releases</name>
			<url>http://118.4.40.25:18081/repository/maven-releases/</url>
		</repository>-->

		<!-- 开发环境-本地打包使用 -->
		<!--<repository>
			<id>cnce-sit-releases</id>
			<name>Releases</name>
			<url>http://118.4.40.25:18081/repository/maven-releases/</url>
		</repository>-->
	</distributionManagement>

  	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<classifier>${profile.name}</classifier>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<configuration>
					<encoding>UTF-8</encoding>
					<nonFilteredFileExtensions>
						<nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
						<nonFilteredFileExtension>xls</nonFilteredFileExtension>
						<nonFilteredFileExtension>pdf</nonFilteredFileExtension>
						<nonFilteredFileExtension>ttf</nonFilteredFileExtension>
						<nonFilteredFileExtension>TTF</nonFilteredFileExtension>
						<nonFilteredFileExtension>html</nonFilteredFileExtension>
					</nonFilteredFileExtensions>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-war-plugin</artifactId>
				<configuration>
					<classifier>${profile.name}</classifier>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>
