/* 序列表*/
DROP TABLE IF EXISTS DE_SYS_SEQUENCE;

/* 序列生成日志记录*/
DROP TABLE IF EXISTS DE_SYS_SEQUENCE_LOG;

/* 序列生成规则表*/
DROP TABLE IF EXISTS DE_SYS_SEQUENCE_RULE;

/* 任务表*/
DROP TABLE IF EXISTS T_DE_TIME_TASK;

/* 任务日志表*/
DROP TABLE IF EXISTS T_DE_TIME_TASK_LOG;

/* 数据库锁表*/
DROP TABLE IF EXISTS DE_SYS_LOCK;

/*==============================================================*/
/* Table: T_DE_TIME_TASK                                        */
/*==============================================================*/
CREATE TABLE T_DE_TIME_TASK
(
   TASK_ID              VARCHAR(32) NOT NULL COMMENT '任务ID',
   SYSTEM_CODE              VARCHAR(32) NOT NULL COMMENT '任务ID',
   TASK_NAME            VARCHAR(64) COMMENT '任务名称',
   TASK_DESC            VARCHAR(512) COMMENT '任务描述',
   CLASS_NAME           VARCHAR(512) COMMENT '任务类名',
   RUN_SERVER_IP        VARCHAR(64) COMMENT '运行任务的服务器IP',
   RUN_SERVER           VARCHAR(64) COMMENT '域名/ip+项目路径',
   CRON_EXPRESSION      VARCHAR(64) COMMENT 'cron表达式',
   TASK_PARAM           VARCHAR(64) COMMENT '任务执行参数',
   IS_EFFECT            VARCHAR(64) COMMENT '0：未生效
            1：生效',
   IS_START             VARCHAR(2) COMMENT '0停止,1运行',
   CREATE_TIME          DATETIME COMMENT '创建时间',
   CREATE_BY            VARCHAR(32) COMMENT '创建人',
   UPDATE_TIME          DATETIME COMMENT '修改时间',
   UPDATE_BY            VARCHAR(32) COMMENT '修改人'
);

ALTER TABLE T_DE_TIME_TASK ADD PRIMARY KEY (TASK_ID);

/*==============================================================*/
/* Table: T_DE_TIME_TASK_LOG                                    */
/*==============================================================*/
CREATE TABLE T_DE_TIME_TASK_LOG
(
   TASK_LOG_ID          VARCHAR(36) NOT NULL COMMENT '任务日志ID',
   TASK_ID              VARCHAR(32) COMMENT '任务ID',
   RUNTIME_NUM          INT(11) COMMENT '任务运行时间',
   START_TIME           DATETIME COMMENT '开始时间',
   END_TIME             DATETIME COMMENT '结束时间',
   STATUS               VARCHAR(64) COMMENT 'OPERATION：运行中
            NORMAL:正常
            ERROR：异常',
   LOG_INFO             VARCHAR(512) COMMENT '日志信息',
   LOG_NUM              INT(11) COMMENT '日志行数',
   LOG_FILE_NAME        VARCHAR(128) COMMENT '日志文件名称'
);

ALTER TABLE T_DE_TIME_TASK_LOG ADD PRIMARY KEY (TASK_LOG_ID);

CREATE TABLE DE_SYS_LOCK
(
   LOCK_ID              VARCHAR(64) NOT NULL COMMENT '锁ID',
   REQUEST_ID           VARCHAR(32) COMMENT '请求ID',
   LOCK_COUNT           INTEGER COMMENT '锁次数',
   TIMEOUT              BIGINT COMMENT '超时时间',
   VERSION              INTEGER COMMENT '锁版本'
);

ALTER TABLE DE_SYS_LOCK  ADD PRIMARY KEY (LOCK_ID);

/*==============================================================*/
/* Table: DE_SYS_SEQUENCE                                       */
/*==============================================================*/
CREATE TABLE DE_SYS_SEQUENCE
(
   SEQUENCE_ID          BIGINT NOT NULL COMMENT '业务主键ID',
   BIZ_CODE             VARCHAR(32) NOT NULL COMMENT '业务编码',
   BIZ_NAME             VARCHAR(128) COMMENT '业务名称',
   SEQUENCE_RESET_RULE  VARCHAR(32) COMMENT '序号重置规则',
   SEQUENCE_DELIMITER  VARCHAR(32) COMMENT '分隔符',
   SEQUENCE_STEP        INT COMMENT '步长',
   CURRENT_CODE  VARCHAR(32) COMMENT '当前编号',
   START_NO           BIGINT COMMENT '起始序号',
   CURRENT_NO           BIGINT COMMENT '当前序号',
   SEQUENCE_RESET_VALUE VARCHAR(32) COMMENT '当前重置依赖',
   CREATE_BY            VARCHAR(32) COMMENT '创建人',
   CREATE_TIME          DATETIME COMMENT '创建时间',
   UPDATE_BY            VARCHAR(32) COMMENT '修改人',
   UPDATE_TIME          DATETIME COMMENT '修改时间',
   PRIMARY KEY (SEQUENCE_ID),
   UNIQUE KEY AK_UNIQUE_BIZ_CODE (SEQUENCE_ID, BIZ_CODE)
);

ALTER TABLE DE_SYS_SEQUENCE COMMENT '业务主键规则';

/*==============================================================*/
/* Table: DE_SYS_SEQUENCE_LOG                                   */
/*==============================================================*/
CREATE TABLE DE_SYS_SEQUENCE_LOG
(
   LOG_ID               BIGINT NOT NULL COMMENT '记录ID',
   BIZ_CODE             VARCHAR(32) COMMENT '业务编码',
   BIZ_NAME             VARCHAR(128) COMMENT '业务名称',
   SEQUENCE_VALUE       VARCHAR(64) COMMENT '主键',
   CREATE_TIME          DATETIME COMMENT '生成时间',
   PRIMARY KEY (LOG_ID)
);

ALTER TABLE DE_SYS_SEQUENCE_LOG COMMENT '业务主键生成记录';

/*==============================================================*/
/* Table: DE_SYS_SEQUENCE_RULE                                  */
/*==============================================================*/
CREATE TABLE DE_SYS_SEQUENCE_RULE
(
   RULE_ID              BIGINT NOT NULL COMMENT '业务ID',
   BIZ_CODE             VARCHAR(32) NOT NULL COMMENT '业务编码',
   RULE_NAME            VARCHAR(32) COMMENT '规则名称',
   RULE_SORT            INT COMMENT '规则排序',
   RULE_TYPE            VARCHAR(32) COMMENT '规则类别',
   RULE_VALUE           VARCHAR(512) COMMENT '规则值',
   PADDING_SIDE         VARCHAR(32) COMMENT '补齐方向',
   PADDING_WIDTH        INT COMMENT '补齐宽度',
   PADDING_CHAR         VARCHAR(32) COMMENT '填充字符',
   PRIMARY KEY (RULE_ID)
);

ALTER TABLE DE_SYS_SEQUENCE_RULE COMMENT '编码规则';

DROP TABLE IF EXISTS STG_SIGNATURE_LOCATION;
CREATE TABLE stg_signature_location  (
    SIGNATURE_ID 					bigint NOT NULL COMMENT '签章位置记录ID',
    SIGNATURE_FILE_TYPE 			varchar(2) COMMENT '背书文件类型',
    SIGNATURE_FILE_TYPE_NAME 		varchar(256) COMMENT '签章文件类型名称',
    SIGNATURE_BIZ_GROUP 			int NULL DEFAULT NULL COMMENT '签章业务组',
    SIGNATURE_BIZ_GROUP_NAME 		varchar(256) COMMENT '签章业务组名称',
    SIGNATURE_BIZ_TYPE 			int NULL DEFAULT NULL COMMENT '签章业务类型',
    SIGNATURE_BIZ_TYPE_NAME 		varchar(256) COMMENT '签章业务名称',
    SIGNATURE_TYPE 				int NULL DEFAULT NULL COMMENT '签章类型编码',
    SIGNATURE_TYPE_NAME 			varchar(256) COMMENT '签章类型名称',
    SIGNATURE_LOCATION_TYPE 		int NULL DEFAULT NULL COMMENT '签章类型 2=坐标签章,3=关键字签章',
    SIGNATURE_LOCATION_TYPE_NAME 	varchar(256) COMMENT '签章位置类型名称',
    KEYWORD 						varchar(256) COMMENT '签章类型为3时，必填，多个关键字用||隔开',
    LX 							decimal(10, 4) COMMENT '左侧的x坐标（单位：像素）按坐标签章时不能为空',
    LY 							decimal(10, 4) COMMENT '左侧的y坐标（单位：像素）按坐标签章时不能为空',
    OFFSETX 						decimal(10, 4) COMMENT 'X偏移,仅对关键字类型生效',
    OFFSETY 						decimal(10, 4) COMMENT 'Y偏移,仅对关键字类型生效',
    CREATE_TIME 					datetime COMMENT '创建时间',
    CREATE_USER_NAME 				varchar(128) COMMENT '创建人',
    CREATE_USER_ID 				varchar(64) COMMENT '创建人ID',
    UPDATE_USER_NAME 				varchar(128) COMMENT '更新人',
    UPDATE_USER_ID 				varchar(64) COMMENT '更新人ID',
    UPDATE_TIME 					datetime COMMENT '更新时间',
    NOTE 							text COMMENT '备注',
    primary key (SIGNATURE_ID)
);
alter table stg_signature_location comment '配置-签章位置表';
