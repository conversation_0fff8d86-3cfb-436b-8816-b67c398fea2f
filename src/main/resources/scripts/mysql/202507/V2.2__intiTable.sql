drop table if exists trd_basis_quote;
create table trd_basis_quote (
    id                          bigint comment '主键',
    stock_id                    bigint comment '商品id',
    stock_code                  varchar(32) comment '商品编码',
    stock_base_info_id          bigint  comment '商品基础信息id',
    user_name                   varchar(255) comment '委托账户名称',
    trader_code                 varchar(32) comment '委托交易商代码',
    trader_name                 varchar(255) comment '委托交易商名称',
    quote_status                varchar(32)  comment '委托状态：wait等待、succ成交、fail失败',
    quote_price                 decimal(18,2)  comment '委托价格',
    quote_price_range           varchar(255)  comment '委托价格范围',
    quote_quantity              decimal(18,4) comment '委托数量',
    buyer_margin_standard          decimal(18,2) comment '买方保证金标准',
    buyer_margin_amount         decimal(18,2) comment '买方保证金金额',
    buyer_trade_fee_amount            decimal(18,2) comment '买方交易手续费金额',
    buyer_trade_fee_standard             decimal(18,2) comment '买方交易手续费标准',
    buyer_delivery_fee_amount            decimal(18,2) comment '买方交割手续费金额',
    buyer_delivery_fee_standard             decimal(18,2) comment '买方交割手续费标准',
    basis_buyer_margin_type           tinyint comment '买方保证金收取方式: 1-集团账户 2-电商',
    basis_buyer_trade_fee_type              tinyint comment '买方交易手续费收取方式: 1-集团账户 2-电商',
    basis_buyer_delivery_fee_type              tinyint comment '买方交割手续费收取方式: 1-集团账户 2-电商',
    client_quote_time           datetime comment '客户端委托时间',
    server_quote_time           datetime comment '服务端委托时间',
    create_time                 datetime comment '创建时间',
    create_user_id              varchar(32) comment '创建人id',
    create_user_name            varchar(255) comment '创建人名称',
    update_time                 datetime comment '修改时间',
    update_user_id              varchar(32) comment '修改人id',
    update_user_name            varchar(255) comment '修改人名称',
    primary key (id)
);
alter table trd_basis_quote comment '基差交易委托表';

drop table if exists trd_basis_order;
create table trd_basis_order (
    id                          bigint comment '主键',
    contract_id                 bigint comment '合同id',
    stock_id                    bigint comment '商品id',
    stock_code                  varchar(32) comment '商品编码',
    stock_base_info_id          bigint  comment '商品基础信息id',
    quote_id                    bigint comment '委托表id',
    seller_custom_code          varchar(32) comment '卖方交易商代码',
    seller_custom_name          varchar(255) comment '卖方交易商名称',
    buyer_custom_code           varchar(32) comment '买方交易商代码',
    buyer_custom_name           varchar(255) comment '买方交易商名称',
    trade_price                  decimal(18,2)  comment '成交价格',
    trade_quantity               decimal(18,4)  comment '成交数量',
    trade_date                   datetime comment '成交时间',
    seller_margin_standard         decimal(18,2) comment '卖方保证金标准',
    seller_margin_amount        decimal(18,2) comment '卖方保证金金额',
    seller_trade_fee_amount           decimal(18,2) comment '卖方手续费金额',
    seller_trade_fee_standard            decimal(18,2) comment '卖方手续费标准',
    seller_delivery_fee_amount           decimal(18,2) comment '卖方手续费金额',
    seller_delivery_fee_standard            decimal(18,2) comment '卖方手续费标准',
    basis_seller_margin_type          tinyint comment '卖方保证金收取方式: 1-集团账户 2-电商',
    basis_seller_trade_fee_type             tinyint comment '卖方手续费收取方式: 1-集团账户 2-电商',
    basis_seller_delivery_fee_type             tinyint comment '卖方手续费收取方式: 1-集团账户 2-电商',
    buyer_margin_standard          decimal(18,2) comment '买方保证金标准',
    buyer_margin_amount         decimal(18,2) comment '买方保证金金额',
    buyer_trade_fee_amount            decimal(18,2) comment '买方手续费金额',
    buyer_trade_fee_standard             decimal(18,2) comment '买方手续费标准',
    buyer_delivery_fee_amount            decimal(18,2) comment '买方手续费金额',
    buyer_delivery_fee_standard             decimal(18,2) comment '买方手续费标准',
    basis_buyer_margin_type           tinyint comment '买方保证金收取方式: 1-集团账户 2-电商',
    basis_buyer_trade_fee_type              tinyint comment '买方手续费收取方式: 1-集团账户 2-电商',
    basis_buyer_delivery_fee_type              tinyint comment '买方手续费收取方式: 1-集团账户 2-电商',
    quote_type                  tinyint comment '报价方式: 1-一口价 2-锁基差 3-即时点价',
    pricing_party               tinyint comment '点价方: 1-买方 2-卖方',
    future_code            varchar(255) comment '期货合约',
    fixed_price                 decimal(18,2) comment '一口价',
    basis_price                 decimal(18,2) comment '基差价格',
    basis_price_range           varchar(255) comment '基差价格范围',
    pricing_price               decimal(18,2) comment '点价价格',
    pricing_valid_time          datetime comment '点价有效期',
    whs_supervision_status      int(11) comment '资金监管状态：1、正常 2、三方监管-籽棉贷款 3、三方监管-皮棉调销 4、三方监管-融资监管状态 5、质押监管 6、质押及三方监管',
    supervise_code              varchar(32) comment '监管机构代码',
    supervise_name              varchar(255) comment '监管机构名称',
    whs_pledge_channel          int(11) comment '金融业务渠道：1正常、2购销、3金益棉、4棉贸通、5e棉通、6棉e通、7全棉通、8邮棉贷、9农商银行',
    primary key (id)
);
alter table trd_basis_order comment '基差交易订单表';

