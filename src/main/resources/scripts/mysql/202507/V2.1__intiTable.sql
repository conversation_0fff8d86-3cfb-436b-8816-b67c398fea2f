drop table if exists trd_basis_futures;
create table trd_basis_futures (
    id                  bigint  comment '主键',
    future_code         varchar(255) not null  comment '期货合约',
    active_future       int(2) comment '是否为主力合约 0否 1是',
    remark              text comment '备注',
    create_user_name    varchar(255)  comment '创建人',
    create_user_id      varchar(32)  comment '创建人id',
    create_time         datetime  comment '创建时间',
    primary key (id)
);
alter table trd_basis_futures comment '基差期货合约表';

drop table if exists trd_basis_stock_base_info;
create table trd_basis_stock_base_info (
    id                          bigint comment '主键',
    stock_code                  varchar(32) comment '商品编码',
    warehouse_receipt_no        varchar(32) comment '仓单号',
    batch_no                    varchar(32) comment '批号',
    quantity                    int  comment '件数：186',
    marks_weight                 decimal(18,4)  comment '唛头重量',
    conditioned_weight          decimal(18,4)  comment '公定重量',
    cross_weight                decimal(18,4)  comment '毛重',
    product_year                int  comment '棉花年度',
    factory_code                varchar(32) comment '加工单位代码',
    factory_name                varchar(255) comment '加工单位',
    whs_pick_mode               int  comment '采摘方式：1、手摘棉 2机采棉',
    mark_desc                   varchar(32) comment '质量标识，如3128B',
    color_grade                 varchar(32)  comment '主体颜色级',
    white_cotton_l1             decimal(8,4) comment '颜色级比率：白棉1级',
    white_cotton_l2             decimal(8,4) comment '颜色级比率：白棉2级',
    white_cotton_l3             decimal(8,4) comment '颜色级比率：白棉3级',
    white_cotton_l4             decimal(8,4) comment '颜色级比率：白棉4级',
    white_cotton_l5             decimal(8,4) comment '颜色级比率：白棉5级',
    white_cotton_l6             decimal(8,4) comment '颜色级比率：白棉6级',
    spot_cotton_l1              decimal(8,4) comment '颜色级比率：淡点污棉1级',
    spot_cotton_l2              decimal(8,4) comment '颜色级比率：淡点污棉2级',
    spot_cotton_l3              decimal(8,4) comment '颜色级比率：淡点污棉3级',
    spot_cotton_l4              decimal(8,4) comment '颜色级比率：淡点污棉4级',
    yellow_ish_cotton_l1        decimal(8,4) comment '颜色级比率：淡黄染棉1级',
    yellow_ish_cotton_l2        decimal(8,4) comment '颜色级比率：淡黄染棉2级',
    yellow_ish_cotton_l3        decimal(8,4) comment '颜色级比率：淡黄染棉3级',
    yellow_cotton_l1            decimal(8,4) comment '颜色级比率：黄染棉1级',
    yellow_cotton_l2            decimal(8,4) comment '颜色级比率：黄染棉2级',
    main_mkl                    varchar(32) comment '主体马克隆值级',
    avg_mkl                     decimal(8,4) comment '马克隆值级平均值',
    avg_break_rate              decimal(8,4) comment '断裂比强度平均值',
    main_length                 varchar(32) comment '主体长度级：28',
    avg_length                  decimal(8,4) default null comment '长度级平均值',
    rolling_quality             varchar(32) comment '轧工质量',
    rolling_quality_avg         varchar(32) comment '轧工质量平均值：92.1',
    uniformity_average_value    varchar(32) comment '长度整齐度平均值：82.3',
    moisture_rate               varchar(32) comment '回潮率：6',
    impurity_rate               varchar(32) comment '含杂率：1.9',
    break_value                  varchar(32) comment '强力',
    AVG_RD decimal(8,4) COMMENT 'Rd平均值',
    MAX_RD decimal(8,4) COMMENT 'Rd最大值',
    MIN_RD decimal(8,4) COMMENT 'Rd最小值',
    AVG_PLUS_B decimal(8,4) COMMENT '+b平均值',
    MAX_PLUS_B decimal(8,4) COMMENT '+b最大值',
    MIN_PLUS_B decimal(8,4) COMMENT '+b最小值',
    stock_name                  varchar(32) comment '品名',
    place_type                  tinyint  comment '产地编码：兵团-第二师',
    place_detail                varchar(1024) default null comment '具体产地',
    intact_place                varchar(1024) default null comment '完整产地',
    inspect_date                datetime comment '检验日期',
    trader_code                 varchar(32) comment '货权人代码',
    trader_name                 varchar(255) comment '货权人名称',
    storage_whs_code            varchar(64) comment '仓库代码',
    storage_whs_name            varchar(64) comment '仓库名称',
    storage_status              int(11) comment '仓储业务状态：1、正常 2、注册中 3、注销中 4、移库中 5、过户中',
    whs_supervision_status      int(11) comment '资金监管状态：1、正常 2、三方监管-籽棉贷款 3、三方监管-皮棉调销 4、三方监管-融资监管状态 5、质押监管 6、质押及三方监管',
    supervise_code              varchar(32) comment '资金监管状态：1、正常 2、三方监管-籽棉贷款 3、三方监管-皮棉调销 4、三方监管-融资监管状态 5、质押监管 6、质押及三方监管',
    supervise_name              varchar(255) comment '监管机构名称',
    whs_pledge_channel          int(11) comment '金融业务渠道：1正常、2购销、3金益棉、4棉贸通、5e棉通、6棉e通、7全棉通、8邮棉贷、9农商银行',
    primary key (id)
  );
  alter table trd_basis_stock_base_info comment '商品基础信息';

  drop table if exists trd_basis_stock;
  create table trd_basis_stock (
      id                          bigint comment '主键',
      stock_base_info_id          bigint comment '商品基础信息id',
      stock_code                  varchar(32) comment '商品编码',
      warehouse_receipt_no        varchar(32) comment '仓单号',
      batch_no                    varchar(32) comment '批号',
      resource_trade_type         tinyint comment '资源交易方式: 1-打捆 2-单批',
      resource_display_type       tinyint comment '资源展示方式: 1-公开 2-指定',
      resource_audit_status       tinyint comment '资源审核状态: 1-未提交 2-待审核 3-已上市 4-已取消',
      quote_type                  tinyint comment '报价方式: 1-一口价 2-锁基差 3-即时点价',
      pricing_party               tinyint comment '点价方: 1-买方 2-卖方',
      future_code            varchar(255) comment '期货合约',
      fixed_price                 decimal(18,2) comment '一口价',
      basis_price                 decimal(18,2) comment '基差价格',
      pricing_price               decimal(18,2) comment '点价价格',
      pricing_valid_time          datetime comment '点价有效期',
      stock_source                tinyint comment '商品来源: 1-仓单 2-...',
      trade_status                tinyint comment '交易状态: 1-未成交 2-已成交 3-已流拍',
      trader_code                 varchar(32) comment '货权人代码',
      trader_name                 varchar(255) comment '货权人名称',
      basis_seller_margin_type    tinyint comment '卖方保证金收取方式: 1-集团账户 2-电商',
      seller_margin_standard         decimal(18,2) comment '卖方保证金标准',
      basis_seller_trade_fee_type       tinyint comment '卖方手续费收取方式: 1-集团账户 2-电商',
      seller_delivery_fee_standard            decimal(18,2) comment '卖方手续费标准',
      undertaker_type             tinyint comment '包干费承担方: 1-卖方 2-买方 3-其它企业',
      undertaker_other_name       varchar(255) comment '包干费其它企业名称',
      undertaker_time             int comment '包干费承担时间',
      subsidy_apply_type          tinyint comment '运补申领方: 1-卖方 2-买方 3-其它企业',
      subsidy_apply_other_name    varchar(255) comment '运补其它企业名称',
      remark                      text comment '备注',
      create_time                 datetime comment '创建时间',
      create_user_id              varchar(32) comment '创建人id',
      create_user_name            varchar(255) comment '创建人名称',
      update_time                 datetime comment '修改时间',
      update_user_id              varchar(32) comment '修改人id',
      update_user_name            varchar(255) comment '修改人名称',
      primary key (id)
  );
  alter table trd_basis_stock comment '现货商品表';


  drop table if exists trd_basis_stock_cart;
  create table trd_basis_stock_cart (
      id                      bigint  comment '主键',
      stock_base_info_id      bigint  comment '商品基础信息id',
      warehouse_receipt_no    varchar(32) comment '仓单号',
      batch_no                varchar(32) comment '批号',
      create_user_name        varchar(255)  comment '创建人',
      create_user_id          varchar(32)  comment '创建人id',
      create_time             datetime  comment '创建时间',
      primary key (id)
  );
  alter table trd_basis_stock_cart comment '基差商品购物车表';

