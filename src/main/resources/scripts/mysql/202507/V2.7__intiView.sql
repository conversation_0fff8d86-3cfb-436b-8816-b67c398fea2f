-- 交易商信息视图
DROP VIEW IF EXISTS v_uc_custom_info;
CREATE VIEW v_uc_custom_info AS
SELECT
    t_uc_custom_info.ID,
    t_uc_custom_info.ENTERPRISE_ID,
    t_uc_custom_info.PARENT_CUSTOM_ID,
    t_uc_custom_info.CUSTOM_NAME,
    t_uc_custom_info.CUSTOM_CODE,
    t_uc_custom_info.CUSTOM_TYPE,
    t_uc_custom_info.ATTACHMENT_TYPE,
    t_uc_custom_info.ATTACHMENT_CODE,
    t_uc_custom_info.ATTACHMENT_NAME,
    t_uc_custom_info.ATTACHMEN_REGION_CODE,
    t_uc_custom_info.ATTACHMEN_REGION_GROUP_CODE,
    t_uc_custom_info.REMARKS,
    t_uc_custom_info.DEL_FLAG,
    t_uc_custom_info.CREATE_TIME,
    t_uc_custom_info.CREATE_USER,
    t_uc_custom_info.CREATE_USER_ID,
    t_uc_custom_info.UPDATE_TIME,
    t_uc_custom_info.UPDATE_USER,
    t_uc_custom_info.UPDATE_USER_ID
FROM
    uc.t_uc_custom_info;

-- 企业信息视图
DROP VIEW IF EXISTS v_uc_enterprise_info;
CREATE VIEW v_uc_enterprise_info AS
SELECT
    *
FROM
    uc.t_uc_enterprise_basic_info;

-- 区域信息视图
DROP VIEW IF EXISTS v_uc_region_info;
CREATE VIEW v_uc_region_info AS
SELECT
    *
FROM
    uc.de_sys_region;

-- 仓单信息视图
DROP VIEW IF EXISTS v_xs_warehouse_info;
CREATE VIEW v_xs_warehouse_info AS
SELECT
    swr.WAREHOUSE_RECEIPT_NUMBER,
    sbi.QUANTITY,
    sbi.WEIGHT,
    sbi.MARK_WEIGHT,
    sbi.CONDITIONED_WEIGHT,
    swr.CURRENT_TRADER_CODE,
    swr.CURRENT_TRADER_NAME,
    swr.WAREHOUSE_RECEIPT_FILE_ID,
    sbi.BATCH_NUMBER,
    swr.CURRENT_WHS_ID AS WHS_ID,
    swr.CURRENT_WHS_CODE AS WHS_CODE,
    swr.CURRENT_WHS_NAME AS WHS_NAME,
    SSA.WARE_RECEIPT_CODE,
    STSA.LOAN_COMPANY_CODE,
    STSA.LOAN_COMPANY_NAME,
    STSA.ASSOCIATED_BANK_NUMBER,
    STSA.REPAY_COMPANY_OPEN_BANK,
    STSA.REPAY_ACCOUNT_NAME,
    STSA.REPAY_BANK_ACCOUNT,
    swr.CREATE_TIME,
    sbi.WHS_PLEDGE_STATUS,
    sbi.WHS_PLEDGE_CHANNEL,
    sbi.WHS_SUPERVISION_STATUS,
    sbi.STORAGE_STATUS,
    swr.WAREHOUSE_RECEIPT_ENABLE_STATUS,
    swr.WAREHOUSE_RECEIPT_STATUS,
    sbi.AGENT_SUPERVISION_STATUS,
    sbi.WHS_TRANSACTION_STATUS,
    sbi.WHS_BUSINESS_TYPE,
    sbi.PLACE_NAME,
    sbi.STOCK_NAME,
    sbi.PRINCIPAL_PART_RANK,
    sbi.COLOR_GRADE_CODE,
    sbi.QUANTITY  AS STOCK_INT_QUANTITY,
    sbi.TIDY_AVG,
    sbi.DIRTY,
    sbi.ROLLING_QUALITY_AVG,
    sbi.MACRON,
    sbi.MOISTURE_REGAIN,
    sbi.CONDITIONED_WEIGHT AS STOCK_IN_WEIGHT,
    sbi.FACTORY_CODE,
    sbi.FACTORY_NAME,
    sbi.PRODUCT_YEAR,
    sbi.WHS_PICK_MODE,
    sbi.STRENGTH_SCALE,
    sbi.MARK_DESC,
    sbi.QUANLITY_STATUS,
    ssa.SUPERVISE_STATUS,
    ( CASE WHEN SWRS.BATCH_ID IS NOT NULL THEN 1 ELSE 0 END ) AS whSControl,
    swi.WAREHOUSE_AREA_TYPE,
    sbi.BONDED_STATUS
FROM
    whs.stg_warehouse_receipt swr JOIN whs.stg_batch_info sbi ON swr.BATCH_ID = sbi.ID
    LEFT JOIN whs.stg_supervise_apply ssa ON ssa.WARE_RECEIPT_CODE = swr.WAREHOUSE_RECEIPT_NUMBER AND ssa.SUPERVISE_APPLY_STATUS != 4 AND ssa.IS_RELIEVE = 2
	LEFT JOIN whs.stg_three_supervise_agreement stsa ON stsa.ID = ssa.SUPERVISE_ID AND stsa.THREE_SUPERVISE_STATUS = 2
	LEFT JOIN whs.stg_warehouse_receipt_sc swrs ON swrs.BATCH_ID = ( SELECT swrs.BATCH_ID FROM whs.stg_warehouse_receipt_sc swrs WHERE swrs.BATCH_ID = sbi.ID AND swrs.SC_BUSINESS_TYPE = 1 LIMIT 1 )
	LEFT JOIN whs.stg_warehouse_info swi ON swi.ID = swr.CURRENT_WHS_ID;

-- 检验质量信息视图
DROP VIEW IF EXISTS v_factory_batch_weight;
CREATE VIEW v_factory_batch_weight AS
SELECT * FROM whs.STG_FACTORY_BATCH_WEIGHT;

-- 批检验质量信息视图
DROP VIEW IF EXISTS v_factory_batch_inspect;
CREATE VIEW v_factory_batch_inspect AS
SELECT * FROM whs.STG_FACTORY_BATCH_INSPECT;
