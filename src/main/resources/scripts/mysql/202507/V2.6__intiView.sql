DROP VIEW IF EXISTS v_whs_factory_forward_batch;
CREATE VIEW v_whs_factory_forward_batch AS
SELECT
    `whs`.`stg_factory_forward_batch`.`ID` AS `ID`,
    `whs`.`stg_factory_forward_batch`.`BATCH_CODE` AS `BATCH_CODE`,
    `whs`.`stg_factory_forward_batch`.`ENTERPRISE_CODE` AS `ENTERPRISE_CODE`,
    `whs`.`stg_factory_forward_batch`.`WORK_YEAR` AS `WORK_YEAR`,
    `whs`.`stg_factory_forward_batch`.`ZMX` AS `ZMX`,
    `whs`.`stg_factory_forward_batch`.`ZSS` AS `ZSS`,
    `whs`.`stg_factory_forward_batch`.`CAN_CANGDAN` AS `CAN_CANGDAN`,
    `whs`.`stg_factory_forward_batch`.`EXCLUDE_NUMBER` AS `EXCLUDE_NUMBER`,
    `whs`.`stg_factory_forward_batch`.`EXCLUDE_CODES` AS `EXCLUDE_CODES`,
    `whs`.`stg_factory_forward_batch`.`EXCLUDE_REASON` AS `EXCLUDE_REASON`,
    `whs`.`stg_factory_forward_batch`.`ZSS_NEW` AS `ZSS_NEW`,
    `whs`.`stg_factory_forward_batch`.`COLOR_GRADE` AS `COLOR_GRADE`,
    `whs`.`stg_factory_forward_batch`.`MAIN_LENGTH` AS `MAIN_LENGTH`,
    `whs`.`stg_factory_forward_batch`.`MAIN_MKL` AS `MAIN_MKL`,
    `whs`.`stg_factory_forward_batch`.`AVG_MKL` AS `AVG_MKL`,
    `whs`.`stg_factory_forward_batch`.`AVG_LEN_UNIFORMITY` AS `AVG_LEN_UNIFORMITY`,
    `whs`.`stg_factory_forward_batch`.`AVG_BREAK_RATE` AS `AVG_BREAK_RATE`,
    `whs`.`stg_factory_forward_batch`.`FORE_FIBER_RATE` AS `FORE_FIBER_RATE`,
    `whs`.`stg_factory_forward_batch`.`NET_WEIGHT` AS `NET_WEIGHT`,
    `whs`.`stg_factory_forward_batch`.`PUB_WEIGHT` AS `PUB_WEIGHT`,
    `whs`.`stg_factory_forward_batch`.`MOISTURE_RATE` AS `MOISTURE_RATE`,
    `whs`.`stg_factory_forward_batch`.`GINNING_QUALITY_RATE_P1` AS `GINNING_QUALITY_RATE_P1`,
    `whs`.`stg_factory_forward_batch`.`GINNING_QUALITY_RATE_P2` AS `GINNING_QUALITY_RATE_P2`,
    `whs`.`stg_factory_forward_batch`.`GINNING_QUALITY_RATE_P3` AS `GINNING_QUALITY_RATE_P3`,
    `whs`.`stg_factory_forward_batch`.`PACKET_NUM` AS `PACKET_NUM`,
    `whs`.`stg_factory_forward_batch`.`MKL_GRADE` AS `MKL_GRADE`,
    `whs`.`stg_factory_forward_batch`.`BREAK_RATE_GRADE` AS `BREAK_RATE_GRADE`,
    `whs`.`stg_factory_forward_batch`.`LEN_UNIFORMITY_INDEX` AS `LEN_UNIFORMITY_INDEX`,
    `whs`.`stg_factory_forward_batch`.`BATCH_EXCLUDE_REASON` AS `BATCH_EXCLUDE_REASON`,
    `whs`.`stg_factory_forward_batch`.`CERTIFICATE_DATE` AS `CERTIFICATE_DATE`,
    `whs`.`stg_factory_forward_batch`.`BATCH_ID` AS `BATCH_ID`,
    `whs`.`stg_factory_forward_batch`.`CURRENT_TRADER_NAME` AS `CURRENT_TRADER_NAME`,
    `whs`.`stg_factory_forward_batch`.`CURRENT_TRADER_CODE` AS `CURRENT_TRADER_CODE`,
    `whs`.`stg_factory_forward_batch`.`FIRST_STORAGE_WHS_CODE` AS `FIRST_STORAGE_WHS_CODE`,
    `whs`.`stg_factory_forward_batch`.`FIRST_STORAGE_WHS_NAME` AS `FIRST_STORAGE_WHS_NAME`,
    `whs`.`stg_factory_forward_batch`.`CURRENT_WHS_CODE` AS `CURRENT_WHS_CODE`,
    `whs`.`stg_factory_forward_batch`.`CURRENT_WHS_NAME` AS `CURRENT_WHS_NAME`,
    `whs`.`stg_factory_forward_batch`.`WAREHOUSE_AREA_TYPE` AS `WAREHOUSE_AREA_TYPE`,
    `whs`.`stg_factory_forward_batch`.`REL_BUSINESS_TYPE` AS `REL_BUSINESS_TYPE`,
    `whs`.`stg_factory_forward_batch`.`FORE_FIBER_RATE1` AS `FORE_FIBER_RATE1`,
    `whs`.`stg_factory_forward_batch`.`SYNC_TIME` AS `SYNC_TIME`,
    `whs`.`stg_factory_forward_batch`.`PLACE_NAME` AS `PLACE_NAME`,
    `whs`.`stg_factory_forward_batch`.`PROCESSING_METHOD` AS `PROCESSING_METHOD`,
    `whs`.`stg_factory_forward_batch`.`DIRTY` AS `DIRTY`,
    `whs`.`stg_factory_forward_batch`.`PRODUCT_YEAR` AS `PRODUCT_YEAR`,
    `whs`.`stg_factory_forward_batch`.`QUANTITY` AS `QUANTITY`,
    `whs`.`stg_factory_forward_batch`.`CONDITIONED_WEIGHT` AS `CONDITIONED_WEIGHT`,
    `whs`.`stg_factory_forward_batch`.`COLOR_GRADE_CODE` AS `COLOR_GRADE_CODE`,
    `whs`.`stg_factory_forward_batch`.`PRINCIPAL_PART_RANK` AS `PRINCIPAL_PART_RANK`,
    `whs`.`stg_factory_forward_batch`.`MACRON` AS `MACRON`,
    `whs`.`stg_factory_forward_batch`.`TIDY_AVG` AS `TIDY_AVG`,
    `whs`.`stg_factory_forward_batch`.`STRENGTH_SCALE` AS `STRENGTH_SCALE`,
    `whs`.`stg_factory_forward_batch`.`ROLLING_QUALITY_AVG` AS `ROLLING_QUALITY_AVG`,
    `whs`.`stg_factory_forward_batch`.`MOISTURE_REGAIN` AS `MOISTURE_REGAIN`,
    `whs`.`stg_factory_forward_batch`.`WAREHOUSE_RECEIPT_STATUS` AS `WAREHOUSE_RECEIPT_STATUS`
FROM
    `whs`.`stg_factory_forward_batch`