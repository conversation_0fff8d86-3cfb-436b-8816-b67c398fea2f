drop table if exists trd_basis_pre_stock;
create table trd_basis_pre_stock
(
    id                   bigint(20) not null  comment '主键',
    stock_base_info_id   bigint(20) default NULL  comment '商品基础信息id',
    stock_code           varchar(32) comment '商品码',
    factory_stock_id     varchar(255)  comment '加工厂商品id',
    -- resource_trade_type  tinyint(4) default NULL  comment '资源交易方式: 1-打捆 2-单批',
    resource_display_type tinyint(4) default NULL  comment '资源展示方式: 1-公开 2-指定',
    resource_audit_status tinyint(4) default NULL  comment '资源审核状态: 1-未提交 2-待审核 3-已上市 4-已取消',
    quote_type           tinyint(4) default NULL  comment '报价方式: 1-一口价 2-锁基差 3-即时点价',
    pricing_party        tinyint(4) default NULL  comment '点价方: 1-买方 2-卖方',
    pre_stock_weight decimal(18, 4)  comment '预售预购重量',
    future_code          varchar(255) comment '期货合约',
    min_basis_price          decimal(18, 2) default NULL  comment '最小基差价格',
    max_basis_price          decimal(18, 2) default NULL  comment '最大基差价格',
    -- pricing_price        decimal(18, 2) default NULL  comment '点价价格',
    pricing_valid_time   datetime  comment '点价有效期',
    delivery_time        datetime  comment '预计交货时间',
    pre_stock_source         tinyint(4) default NULL  comment '商品来源: 1-预售 2-预购',
    trade_status         tinyint(4) default NULL  comment '交易状态: 1-未成交 2-已成交 3-已流拍',
    trader_code      varchar(32) comment '企业代码',
    trader_name      varchar(255) comment '企业名称',
    basis_seller_margin_type tinyint(4) default NULL  comment '卖方保证金收取方式: 1-集团账户 2-电商',
    seller_margin_standard decimal(18, 2) default NULL  comment '卖方保证金标准',
    basis_seller_trade_fee_type tinyint(4) default NULL  comment '卖方手续费收取方式: 1-集团账户 2-电商',
    basis_seller_delivery_fee_type tinyint(4) default NULL  comment '卖方手续费收取方式: 1-集团账户 2-电商',
    seller_trade_fee_standard  decimal(18, 2) default NULL  comment '卖方手续费标准',
    seller_delivery_fee_standard  decimal(18, 2) default NULL  comment '卖方手续费标准',
    supplement_content   text  comment '补充说明',
    remark               text comment '备注',
    create_time          datetime  comment '创建时间',
    create_user_id       varchar(32) comment '创建人',
    create_user_name     varchar(255) comment '',
    update_time          datetime  comment '',
    update_user_id       varchar(32) comment '',
    update_user_name     varchar(255) comment '',
    primary key (id)
);
alter table trd_basis_pre_stock comment '预售预购商品';

drop table if exists trd_basis_pre_stock_base_info;
create table trd_basis_pre_stock_base_info
(
    id                   bigint(20) not null  comment '主键',
    stock_code           varchar(32) comment '商品码',
    quantity             int(11) default NULL  comment '件数：186',
    trader_code          varchar(32) comment '企业代码',
    trader_name          varchar(255) comment '企业名称',
    whs_pick_mode        int(11) default NULL  comment '采摘方式：1、手摘棉 2机采棉',
    min_color_grade      decimal(8, 4) default NULL  comment '最小颜色级',
    max_color_grade      decimal(8, 4) default NULL  comment '最大颜色级',
    min_mkl              decimal(8, 4)  comment '最小马值',
    max_mkl              decimal(8, 4)  comment '最大马值',
    min_length           decimal(8, 4)  comment '最小长度',
    max_length           decimal(8, 4)  comment '最大长度',
    min_break            decimal(8, 4)  comment '最小强力',
    max_break            decimal(8, 4)  comment '最大强力',
    min_uniformity_average decimal(8, 4)  comment '最小长整',
    max_uniformity_average decimal(8, 4)  comment '最大长整',
    min_impurity         decimal(8, 4)  comment '最小含杂',
    max_impurity         decimal(8, 4)  comment '最大含杂',
    min_moisture         decimal(8, 4)  comment '最小回潮',
    max_moisture         decimal(8, 4)  comment '最大回潮',
    place_type           varchar(255) default NULL  comment '产地编码：兵团-第二师',
    place_detail         varchar(1024) comment '具体地址',
    intact_place         varchar(1024) comment '完整地址',
    primary key (id)
);
alter table trd_basis_pre_stock_base_info comment '预售预购商品基础信息';



drop table if exists trd_basis_pre_stock_template;
create table trd_basis_pre_stock_template
(
    id                   bigint(20) not null  comment '主键',
    template_name          varchar(32) comment '模板名称',
    trader_code          varchar(32) comment '企业代码',
    trader_name          varchar(255) comment '企业名称',
    pre_stock_source         tinyint(4) default NULL  comment '商品来源: 1-预售 2-预购',
    pre_stock_weight decimal(18, 4)  comment '预售预购重量',
    quantity             int(11) default NULL  comment '件数：186',
    place_type           varchar(255) default NULL  comment '产地编码：兵团-第二师',
    place_detail         varchar(1024) comment '具体地址',
    intact_place         varchar(1024) comment '完整地址',
    whs_pick_mode        int(11) default NULL  comment '采摘方式：1、手摘棉 2机采棉',
    min_color_grade      decimal(8, 4) default NULL  comment '最小颜色级',
    max_color_grade      decimal(8, 4) default NULL  comment '最大颜色级',
    min_mkl              decimal(8, 4)  comment '最小马值',
    max_mkl              decimal(8, 4)  comment '最大马值',
    min_length           decimal(8, 4)  comment '最小长度',
    max_length           decimal(8, 4)  comment '最大长度',
    min_break            decimal(8, 4)  comment '最小强力',
    max_break            decimal(8, 4)  comment '最大强力',
    min_uniformity_average decimal(8, 4)  comment '最小长整',
    max_uniformity_average decimal(8, 4)  comment '最大长整',
    min_impurity         decimal(8, 4)  comment '最小含杂',
    max_impurity         decimal(8, 4)  comment '最大含杂',
    min_moisture         decimal(8, 4)  comment '最小回潮',
    max_moisture         decimal(8, 4)  comment '最大回潮',
    supplement_content   text  comment '补充说明',
    future_code          varchar(255) comment '期货合约',
    min_basis_price          decimal(18, 2) default NULL  comment '最小基差价格',
    max_basis_price          decimal(18, 2) default NULL  comment '最大基差价格',
    pricing_valid_time   datetime  comment '点价有效期',
    delivery_time        datetime  comment '预计交货时间',
    remark               text comment '备注',
    create_time          datetime  comment '创建时间',
    create_user_id       varchar(32) comment '创建人',
    create_user_name     varchar(255) comment '',
    update_time          datetime  comment '',
    update_user_id       varchar(32) comment '',
    update_user_name     varchar(255) comment '',
    primary key (id)
);
alter table trd_basis_pre_stock_template comment '预售预购商品模板表';

drop table if exists trd_basis_query_template;
create table trd_basis_query_template
(
    id                   bigint(20) not null  comment '主键',
    template_type          varchar(32) comment '模板类型1-全部、2-买方点价、3-卖方点价、4-预售、5-预购',
    template_name          varchar(32) comment '模板名称',
    template_name          text comment '模板内容',
    trader_code          varchar(32) comment '企业代码',
    trader_name          varchar(255) comment '企业名称',
    create_time          datetime  comment '创建时间',
    create_user_id       varchar(32) comment '创建人',
    create_user_name     varchar(255) comment '',
    update_time          datetime  comment '',
    update_user_id       varchar(32) comment '',
    update_user_name     varchar(255) comment '',
    primary key (id)
);
alter table trd_basis_query_template comment '查询模板表';

drop table if exists trd_basis_common_setting;
create table trd_basis_common_setting
(
    id                   bigint(20) not null  comment '主键',
    setting_name          varchar(32) comment '设置名称',
    basis_setting_type          varchar(32) comment '设置类型1、买方点价2、预售预购',
    seller_margin_standard decimal(18,2) comment '卖方保证金标准',
    seller_trade_fee_standard decimal(18,2) comment '卖方交易手续费标准',
    seller_delivery_fee_standard decimal(18,2) comment '卖方交割手续费标准',
    buyer_margin_standard decimal(18,2) comment '买方保证金标准',
    buyer_trade_fee_standard decimal(18,2) comment '买方交易手续费标准',
    buyer_delivery_fee_standard decimal(18,2) comment '买方交割手续费标准',
    setting_status  varchar(32) comment '是否启用-1停止1启用',
    create_time          datetime  comment '创建时间',
    create_user_id       varchar(32) comment '创建人',
    create_user_name     varchar(255) comment '',
    update_time          datetime  comment '',
    update_user_id       varchar(32) comment '',
    update_user_name     varchar(255) comment '',
    primary key (id)
);
alter table trd_basis_common_setting comment '基差交易公共设置';

drop table if exists trd_basis_trader_setting;
create table trd_basis_trader_setting
(
    id                   bigint(20) not null  comment '主键',
    basis_setting_type          varchar(32) comment '设置类型1、买方点价2、预售预购',
    trader_code          varchar(32) comment '企业代码',
    trader_name          varchar(255) comment '企业名称',
    margin_standard decimal(18,2) comment '保证金标准',
    trade_fee_standard decimal(18,2) comment '交易手续费标准',
    delivery_fee_standard decimal(18,2) comment '交割手续费标准',
    create_time          datetime  comment '创建时间',
    create_user_id       varchar(32) comment '创建人',
    create_user_name     varchar(255) comment '',
    update_time          datetime  comment '',
    update_user_id       varchar(32) comment '',
    update_user_name     varchar(255) comment '',
    primary key (id)
);
alter table trd_basis_trader_setting comment '基差交易交易商设置';

drop table if exists trd_basis_notice;
create table trd_basis_notice (
    id bigint(20) not null comment '主键',
    notice_type tinyint(1) unsigned default '1' comment '消息类型：1广播消息，2通知消息',
    title varchar(255) default null comment '标题',
    content text comment '内容',
    del_flag tinyint(1) unsigned default '1' comment '删除状态 1.已删除 0.未删除',
    publish_status tinyint(1) unsigned default '1' comment '发布状态：1未发布，2已发布',
    create_time          datetime  comment '创建时间',
    create_user_id       varchar(32) comment '创建人',
    create_user_name     varchar(255) comment '',
    update_time          datetime  comment '',
    update_user_id       varchar(32) comment '',
    update_user_name     varchar(255) comment '',
    primary key (id)
);
alter table trd_basis_notice comment '基差交易公告表';


INSERT INTO trd_basis_common_setting VALUES (332590166738853888, '2025年公共设置', 0.00, 0.00, 0.00, 10.00, 2.00, 8.00, '1', '2025-06-05 22:06:52', '6000000000000018', '900001_管理员', '2025-06-05 22:06:52', '6000000000000018', '900001_管理员', '1');
