-- 其它模块
drop table if exists trd_basis_orders_invoices;
create table trd_basis_orders_invoices
(
    id                   bigint NOT NULL COMMENT '主键',
    provider_trader_code varchar(32)  DEFAULT NULL COMMENT '开票交易商代码',
    provider_trader_name varchar(255) DEFAULT NULL COMMENT '开票交易商名称',
    receiver_trader_code varchar(32)  DEFAULT NULL COMMENT '对方交易商代码',
    receiver_trader_name varchar(255) DEFAULT NULL COMMENT '对方交易商名称',
    invoices_file_id     bigint       DEFAULT NULL COMMENT '发票文件id',
    create_time          datetime     DEFAULT NULL COMMENT '创建时间',
    create_user_id       varchar(32)  DEFAULT NULL COMMENT '创建人ID',
    create_user_name     varchar(255) DEFAULT NULL COMMENT '创建人',
    PRIMARY KEY (id)
);
alter table trd_basis_orders_invoices comment '订单发票表';

drop table if exists trd_basis_user_operate;
create table trd_basis_user_operate
(
    id                bigint NOT NULL COMMENT '主键',
    trader_code       varchar(32)  DEFAULT NULL COMMENT '交易商代码',
    trader_name       varchar(255) DEFAULT NULL COMMENT '交易商名称',
    operate_type      tinyint      DEFAULT NULL COMMENT '操作类型：1提交资源2查询资源3关注资源4报价5手动成交',
    business_id       bigint       DEFAULT NULL COMMENT '业务ID',
    operation_result  text COMMENT '结果说明',
    operation_content text COMMENT '操作内容',
    create_time       datetime     DEFAULT NULL COMMENT '创建时间',
    create_user_id    varchar(32)  DEFAULT NULL COMMENT '创建人id',
    create_user_name  varchar(255) DEFAULT NULL COMMENT '创建人',
    PRIMARY KEY (id)
);
alter table trd_basis_user_operate comment '用户操作记录表';

drop table if exists trd_basis_trader_code;
create table trd_basis_trader_code
(
    id               bigint NOT NULL COMMENT '主键',
    trader_code      varchar(32)  DEFAULT NULL COMMENT '交易商代码',
    trader_name      varchar(255) DEFAULT NULL COMMENT '交易商名称',
    custom_code      varchar(32)  DEFAULT NULL COMMENT '客户编码',
    create_time      datetime     DEFAULT NULL COMMENT '创建时间',
    create_user_id   varchar(32)  DEFAULT NULL COMMENT '创建人',
    create_user_name varchar(255) DEFAULT NULL COMMENT '创建人id',
    PRIMARY KEY (id)
);
alter table trd_basis_trader_code comment '基差交易商编码';

drop table if exists trd_basis_futures_price_records;
create table trd_basis_futures_price_records
(
    id               bigint NOT NULL COMMENT '主键',
    future_code      varchar(255)   DEFAULT NULL COMMENT '期货合约',
    last_price       decimal(18, 2) DEFAULT NULL COMMENT '最新价格',
    clear_price      decimal(18, 2) DEFAULT NULL COMMENT '结算价格',
    high_limit       decimal(18, 2) DEFAULT NULL COMMENT '涨停板',
    low_limit        decimal(18, 2) DEFAULT NULL COMMENT '跌停板',
    high_price       decimal(18, 2) DEFAULT NULL COMMENT '最高价',
    low_price        decimal(18, 2) DEFAULT NULL COMMENT '最低价格',
    future_time      datetime       DEFAULT NULL COMMENT '期货价格时间',
    create_time      datetime       DEFAULT NULL,
    create_user_id   varchar(32)    DEFAULT NULL,
    create_user_name varchar(255)   DEFAULT NULL,
    PRIMARY KEY (id)
);
alter table trd_basis_futures_price_records comment '期货价格记录表';

drop table if exists trd_basis_region;
create table trd_basis_region
(
    ID           bigint NOT NULL,
    PARENT_ID    bigint       DEFAULT NULL,
    REGION_NAME  varchar(255) DEFAULT NULL,
    REGION_LEVEL int          DEFAULT NULL,
    REGION_KEY   varchar(50)  DEFAULT NULL,
    SEQ          int          DEFAULT NULL,
    CREATE_TIME  datetime     DEFAULT NULL,
    UPDATE_TIME  datetime     DEFAULT NULL,
    PRIMARY KEY (ID)
);
alter table trd_basis_region comment '基差交易区域表';

-- 系统设置
drop table if exists trd_basis_query_template;
create table trd_basis_query_template
(
    id               bigint NOT NULL COMMENT '主键',
    template_type    varchar(32)  DEFAULT NULL COMMENT '模板类型1-全部、2-买方点价',
    template_name    varchar(32)  DEFAULT NULL COMMENT '模板名称',
    template_value   text COMMENT '模板内容-查询项json',
    trader_code      varchar(32)  DEFAULT NULL COMMENT '交易商代码',
    trader_name      varchar(255) DEFAULT NULL COMMENT '交易商名称',
    create_time      datetime     DEFAULT NULL,
    create_user_id   varchar(32)  DEFAULT NULL,
    create_user_name varchar(255) DEFAULT NULL,
    update_time      datetime     DEFAULT NULL,
    update_user_id   varchar(32)  DEFAULT NULL,
    update_user_name varchar(255) DEFAULT NULL,
    PRIMARY KEY (id)
);
alter table trd_basis_query_template comment '查询模板表';

drop table if exists trd_basis_futures;
create table trd_basis_futures
(
    id               bigint NOT NULL COMMENT '主键',
    future_code      varchar(255) DEFAULT NULL COMMENT '期货合约',
    active_future    int          DEFAULT NULL COMMENT '是否为主力合约 0否 1是',
    remark           text COMMENT '备注',
    create_user_name varchar(255) DEFAULT NULL,
    create_user_id   varchar(32)  DEFAULT NULL,
    create_time      datetime     DEFAULT NULL,
    PRIMARY KEY (id)
);
alter table trd_basis_futures comment '期货价格记录表';

drop table if exists trd_basis_market_setting;
create table trd_basis_market_setting
(
    id                                       bigint NOT NULL COMMENT '主键',
    setting_name                             varchar(32)    DEFAULT NULL COMMENT '板块名称',
    basis_setting_type                       varchar(32)    DEFAULT NULL COMMENT '设置类型:1、买方点价2、预售预购 3、现货挂牌',
    trade_start_time                         time           DEFAULT NULL COMMENT '交易开始时间',
    trade_end_time                           time           DEFAULT NULL COMMENT '交易结束时间',
    buyer_basis_margin_call_line             decimal(18, 2) DEFAULT NULL COMMENT '买方基差追保线',
    buyer_basis_margin_call_standard         decimal(18, 2) DEFAULT NULL COMMENT '买方基差追保标准',
    buyer_pricing_margin_call_line           decimal(18, 2) DEFAULT NULL COMMENT '买方点价追保线',
    buyer_pricing_margin_call_standard       decimal(18, 2) DEFAULT NULL COMMENT '买方点价保证金标准',
    buyer_pricing_forced_liquidation_line    decimal(18, 2) DEFAULT NULL COMMENT '买方点价强平线',
    seller_pricing_forced_liquidation_line   decimal(18, 2) DEFAULT NULL COMMENT '卖方点价强平线',
    warehouse_receipt_deposit_times          int            DEFAULT NULL COMMENT '仓单抵免次数',
    warehouse_receipt_deposit_onetime_amount decimal(18, 2) DEFAULT NULL COMMENT '仓单抵免-单次保证金金额',
    seller_basis_margin_type                 tinyint        DEFAULT NULL COMMENT '卖方基差保证金收取方式: 1-集团账户 2-电商',
    seller_basis_margin_standard             decimal(18, 2) DEFAULT NULL COMMENT '卖方基差保证金标准',
    seller_trade_fee_type                    tinyint        DEFAULT NULL COMMENT '卖方交易手续费收取方式: 1-集团账户 2-电商',
    seller_trade_fee_standard                decimal(18, 2) DEFAULT NULL COMMENT '卖方交易手续费标准',
    seller_delivery_fee_type                 tinyint        DEFAULT NULL COMMENT '卖方交割手续费收取方式: 1-集团账户 2-电商',
    seller_delivery_fee_standard             decimal(18, 2) DEFAULT NULL COMMENT '卖方交割手续费标准',
    buyer_basis_margin_type                  tinyint        DEFAULT NULL COMMENT '买方基差保证金收取方式: 1-集团账户 2-电商',
    buyer_basis_margin_standard              decimal(18, 2) DEFAULT NULL COMMENT '买方基差保证金标准',
    buyer_trade_fee_type                     tinyint        DEFAULT NULL COMMENT '买方交易手续费收取方式: 1-集团账户 2-电商',
    buyer_trade_fee_standard                 decimal(18, 2) DEFAULT NULL COMMENT '买方交易手续费标准',
    buyer_delivery_fee_type                  tinyint        DEFAULT NULL COMMENT '买方交割手续费收取方式: 1-集团账户 2-电商',
    buyer_delivery_fee_standard              decimal(18, 2) DEFAULT NULL COMMENT '买方交割手续费标准',
    payment_seller_amount_ratio              decimal(18, 2) DEFAULT NULL COMMENT '支付卖方货款比例',
    collection_buyer_amount_ratio            decimal(18, 2) DEFAULT NULL COMMENT '收买方货款比例',
    auto_pricing_time                        int            DEFAULT NULL COMMENT '自动点价确认时间',
    contract_generate_status                 tinyint        DEFAULT NULL COMMENT '合同是否自动生成1不生成2生成',
    audit_remark_status                      tinyint(1) DEFAULT NULL COMMENT '是否自动审核备注 1-否，2-是',
    spot_system_status                       tinyint(1) DEFAULT NULL COMMENT '挂牌系统状态（1-正常、2-暂停）',
    setting_status                           varchar(32)    DEFAULT NULL COMMENT '是否启用 1启用 -1未启用',
    create_time                              datetime       DEFAULT NULL COMMENT '创建时间',
    create_user_id                           varchar(32)    DEFAULT NULL COMMENT '创建人id',
    create_user_name                         varchar(255)   DEFAULT NULL COMMENT '创建人',
    update_time                              datetime       DEFAULT NULL COMMENT '更新时间',
    update_user_id                           varchar(32)    DEFAULT NULL COMMENT '更新人id',
    update_user_name                         varchar(255)   DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (id)
);
alter table trd_basis_market_setting comment '基差交易设置表';


drop table if exists trd_basis_credit;
create table trd_basis_credit
(
    id               bigint NOT NULL COMMENT '主键',
    credit_code      varchar(32)  DEFAULT NULL COMMENT '抵免编号',
    trader_code      varchar(32)  DEFAULT NULL COMMENT '交易商代码',
    trader_name      varchar(255) DEFAULT NULL COMMENT '交易商名称',
    credit_file_id   bigint       DEFAULT NULL COMMENT '抵免协议id',
    review_status    tinyint      DEFAULT NULL COMMENT '审核状态：1已申请2已通过3已驳回',
    create_time      datetime     DEFAULT NULL COMMENT '创建时间',
    create_user_id   varchar(32)  DEFAULT NULL COMMENT '创建人id',
    create_user_name varchar(255) DEFAULT NULL COMMENT '创建人',
    update_time      datetime     DEFAULT NULL COMMENT '更新时间',
    update_user_id   varchar(32)  DEFAULT NULL COMMENT '更新人id',
    update_user_name varchar(255) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (id)
);
alter table trd_basis_credit comment '仓单抵免协议表';

drop table if exists trd_basis_warehouse_receipt_credit;
create table trd_basis_warehouse_receipt_credit
(
    id                   bigint NOT NULL COMMENT '主键',
    credit_id            bigint       DEFAULT NULL COMMENT '抵免id',
    warehouse_receipt_no varchar(32)  DEFAULT NULL COMMENT '仓单号',
    batch_no             varchar(32)  DEFAULT NULL COMMENT '批号',
    credit_status        tinyint      DEFAULT NULL COMMENT '抵免状态：1未抵免2部分抵免3全部抵免4已换批5已作废',
    create_time          datetime     DEFAULT NULL COMMENT '创建时间',
    create_user_id       varchar(32)  DEFAULT NULL COMMENT '创建人id',
    create_user_name     varchar(255) DEFAULT NULL COMMENT '创建人',
    update_time          datetime     DEFAULT NULL COMMENT '更新时间',
    update_user_id       varchar(32)  DEFAULT NULL COMMENT '更新人id',
    update_user_name     varchar(255) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (id)
);
alter table trd_basis_warehouse_receipt_credit comment '仓单抵免批次表';

drop table if exists trd_basis_credit_business;
create table trd_basis_credit_business
(
    id                     bigint NOT NULL COMMENT '主键',
    credit_id              bigint         DEFAULT NULL COMMENT '抵免id',
    credit_batch_id        bigint         DEFAULT NULL COMMENT '抵免批次id',
    business_id            bigint         DEFAULT NULL COMMENT '业务id',
    credit_type            tinyint        DEFAULT NULL COMMENT '抵免类型：1报价2追保',
    funds_type             tinyint        DEFAULT NULL COMMENT '资金类型：1交易保证金2基差保证金',
    credit_standard        decimal(18, 2) DEFAULT NULL COMMENT '抵免标准',
    credit_amount          decimal(18, 2) DEFAULT NULL COMMENT '抵免金额',
    business_credit_status tinyint        DEFAULT NULL COMMENT '抵免状态：1已抵免2已取消',
    create_time            datetime       DEFAULT NULL COMMENT '创建时间',
    create_user_id         varchar(32)    DEFAULT NULL COMMENT '创建人id',
    create_user_name       varchar(255)   DEFAULT NULL COMMENT '创建人',
    update_time            datetime       DEFAULT NULL COMMENT '更新时间',
    update_user_id         varchar(32)    DEFAULT NULL COMMENT '更新人id',
    update_user_name       varchar(255)   DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (id)
);
alter table trd_basis_credit_business comment '抵免业务关系表';

-- 交易管理
drop table if exists trd_basis_stock_base_info;
create table trd_basis_stock_base_info
(
    id                       bigint NOT NULL COMMENT '主键',
    stock_code               varchar(32)    DEFAULT NULL COMMENT '商品编码',
    warehouse_receipt_no     varchar(32)    DEFAULT NULL COMMENT '仓单号',
    batch_no                 varchar(32)    DEFAULT NULL COMMENT '批号',
    quantity                 int            DEFAULT NULL COMMENT '件数：186',
    marks_weight             decimal(18, 4) DEFAULT NULL COMMENT '唛头重量',
    conditioned_weight       decimal(18, 4) DEFAULT NULL COMMENT '公定重量',
    cross_weight             decimal(18, 4) DEFAULT NULL COMMENT '毛重',
    product_year             varchar(32)    DEFAULT NULL COMMENT '棉花年度',
    factory_code             varchar(32)    DEFAULT NULL COMMENT '加工单位代码',
    factory_name             varchar(255)   DEFAULT NULL COMMENT '加工单位名称',
    whs_pick_mode            int            DEFAULT NULL COMMENT '采摘方式：1、手摘棉 2机采棉',
    mark_desc                varchar(32)    DEFAULT NULL COMMENT '质量标识',
    color_grade              varchar(32)    DEFAULT NULL COMMENT '主体颜色级',
    white_cotton_l1          decimal(8, 4)  DEFAULT NULL COMMENT '颜色级比率：白棉1级',
    white_cotton_l2          decimal(8, 4)  DEFAULT NULL COMMENT '颜色级比率：白棉2级',
    white_cotton_l3          decimal(8, 4)  DEFAULT NULL COMMENT '颜色级比率：白棉3级',
    white_cotton_l4          decimal(8, 4)  DEFAULT NULL COMMENT '颜色级比率：白棉4级',
    white_cotton_l5          decimal(8, 4)  DEFAULT NULL COMMENT '颜色级比率：白棉5级',
    white_cotton_l6          decimal(8, 4)  DEFAULT NULL COMMENT '颜色级比率：白棉6级',
    spot_cotton_l1           decimal(8, 4)  DEFAULT NULL COMMENT '颜色级比率：淡点污棉1级',
    spot_cotton_l2           decimal(8, 4)  DEFAULT NULL COMMENT '颜色级比率：淡点污棉2级',
    spot_cotton_l3           decimal(8, 4)  DEFAULT NULL COMMENT '颜色级比率：淡点污棉3级',
    spot_cotton_l4           decimal(8, 4)  DEFAULT NULL COMMENT '颜色级比率：淡点污棉4级',
    yellow_ish_cotton_l1     decimal(8, 4)  DEFAULT NULL COMMENT '颜色级比率：淡黄染棉1级',
    yellow_ish_cotton_l2     decimal(8, 4)  DEFAULT NULL COMMENT '颜色级比率：淡黄染棉2级',
    yellow_ish_cotton_l3     decimal(8, 4)  DEFAULT NULL COMMENT '颜色级比率：淡黄染棉3级',
    yellow_cotton_l1         decimal(8, 4)  DEFAULT NULL COMMENT '颜色级比率：黄染棉1级',
    yellow_cotton_l2         decimal(8, 4)  DEFAULT NULL COMMENT '颜色级比率：黄染棉2级',
    main_mkl                 varchar(32)    DEFAULT NULL COMMENT '马克隆值',
    avg_mkl                  decimal(8, 4)  DEFAULT NULL COMMENT '马克隆值级平均值',
    avg_break_rate           decimal(8, 4)  DEFAULT NULL COMMENT '断裂比强度平均值',
    main_length              varchar(32)    DEFAULT NULL COMMENT '主体长度',
    avg_length               decimal(8, 4)  DEFAULT NULL COMMENT '长度级平均值',
    rolling_quality          varchar(32)    DEFAULT NULL COMMENT '轧工质量',
    rolling_quality_avg      varchar(32)    DEFAULT NULL COMMENT '轧工质量平均值',
    uniformity_average_value varchar(32)    DEFAULT NULL COMMENT '长度整齐度平均值',
    moisture_rate            varchar(32)    DEFAULT NULL COMMENT '回潮率',
    impurity_rate            varchar(32)    DEFAULT NULL COMMENT '含杂率',
    break_value              decimal(8, 4)  DEFAULT NULL COMMENT '强力',
    stock_name               varchar(32)    DEFAULT NULL COMMENT '品名 1:长绒棉 2:细绒棉 3:彩棉 4:籽棉 5:棉浆粕 6:短绒 7:棉纱 8:涤纶短纤 9:粘胶短纤',
    place_type               tinyint        DEFAULT NULL COMMENT '产地编码：兵团-第二师',
    place_detail             varchar(1024)  DEFAULT NULL COMMENT '具体产地',
    intact_place             varchar(1024)  DEFAULT NULL COMMENT '完整产地',
    inspect_date             datetime       DEFAULT NULL COMMENT '公检日期',
    trader_code              varchar(32)    DEFAULT NULL COMMENT '货权人代码',
    trader_name              varchar(255)   DEFAULT NULL COMMENT '货权人名称',
    storage_whs_code         varchar(64)    DEFAULT NULL COMMENT '仓库代码',
    storage_whs_name         varchar(64)    DEFAULT NULL COMMENT '仓库名称',
    storage_status           int            DEFAULT NULL COMMENT '仓储业务状态：1、正常 2、注册中 3、注销中 4、移库中 5、过户中',
    whs_supervision_status   int            DEFAULT NULL COMMENT '资金监管状态：1、正常 2、三方监管-籽棉贷款 3、三方监管-皮棉调销 4、三方监管-融资监管状态 5、质押监管 6、质押及三方监管',
    supervise_code           varchar(32)    DEFAULT NULL COMMENT '监管机构代码',
    supervise_name           varchar(255)   DEFAULT NULL COMMENT '监管机构名称',
    whs_pledge_channel       int            DEFAULT NULL COMMENT '金融业务渠道：1正常、2购销、3金益棉、4棉贸通、5e棉通、6棉e通、7全棉通、8邮棉贷、9农商银行',
    max_mkl                  decimal(8, 4)  DEFAULT NULL COMMENT '马克隆值级最大值',
    min_mkl                  decimal(8, 4)  DEFAULT NULL COMMENT '马克隆值级最小值',
    AVG_RD                   decimal(8, 4)  DEFAULT NULL COMMENT 'Rd平均值',
    MAX_RD                   decimal(8, 4)  DEFAULT NULL COMMENT 'Rd最大值',
    MIN_RD                   decimal(8, 4)  DEFAULT NULL COMMENT 'Rd最小值',
    AVG_PLUS_B               decimal(8, 4)  DEFAULT NULL COMMENT '+b平均值',
    MAX_PLUS_B               decimal(8, 4)  DEFAULT NULL COMMENT '+b最大值',
    MIN_PLUS_B               decimal(8, 4)  DEFAULT NULL COMMENT '+b最小值',
    PRIMARY KEY (id)
);
alter table trd_basis_stock_base_info comment '商品基础信息';

drop table if exists trd_basis_stock;
create table trd_basis_stock
(
    id                           bigint NOT NULL COMMENT '主键',
    stock_base_info_id           bigint         DEFAULT NULL COMMENT '商品基础信息id',
    stock_code                   varchar(32)    DEFAULT NULL COMMENT '商品编码',
    warehouse_receipt_no         varchar(32)    DEFAULT NULL COMMENT '仓单号',
    batch_no                     varchar(32)    DEFAULT NULL COMMENT '批号',
    resource_trade_type          tinyint        DEFAULT NULL COMMENT '资源交易方式: 1单批',
    resource_display_type        tinyint        DEFAULT NULL COMMENT '资源展示方式: 1-公开',
    resource_audit_status        tinyint        DEFAULT NULL COMMENT '资源审核状态: 1-未提交 2-待审核 3-已上市 4-已取消',
    quote_type                   tinyint        DEFAULT NULL COMMENT '报价方式: 1-锁基差',
    pricing_party                tinyint        DEFAULT NULL COMMENT '点价方: 1-买方',
    future_code                  varchar(255)   DEFAULT NULL COMMENT '期货合约',
    basis_price                  decimal(18, 2) DEFAULT NULL COMMENT '基差价格',
    stock_weight                 decimal(12, 4) DEFAULT NULL COMMENT '商品重量',
    pricing_price                decimal(18, 2) DEFAULT NULL COMMENT '点价价格',
    pricing_valid_time           datetime       DEFAULT NULL COMMENT '点价有效期',
    interest_cost                decimal(18, 2) DEFAULT NULL COMMENT '利息成本',
    stock_source                 tinyint        DEFAULT NULL COMMENT '商品来源: 1-仓单 2-...',
    trade_status                 tinyint        DEFAULT NULL COMMENT '交易状态: 1-未成交 2-已成交 3-已下架',
    delisting_reason             text COMMENT '下架原因',
    trader_code                  varchar(32)    DEFAULT NULL COMMENT '货权人代码',
    trader_name                  varchar(255)   DEFAULT NULL COMMENT '货权人名称',
    seller_basis_margin_type     tinyint        DEFAULT NULL COMMENT '卖方基差保证金收取方式: 1-集团账户 2-电商',
    seller_basis_margin_standard decimal(18, 2) DEFAULT NULL COMMENT '卖方基差保证金标准',
    seller_trade_fee_type        tinyint        DEFAULT NULL COMMENT '卖方交易手续费收取方式: 1-集团账户 2-电商',
    seller_trade_fee_standard    decimal(18, 2) DEFAULT NULL COMMENT '卖方交易手续费标准',
    seller_delivery_fee_type     tinyint        DEFAULT NULL COMMENT '卖方交割手续费收取方式: 1-集团账户 2-电商',
    seller_delivery_fee_standard decimal(18, 2) DEFAULT NULL COMMENT '卖方交割手续费标准',
    remark                       text COMMENT '备注',
    create_time                  datetime       DEFAULT NULL COMMENT '创建时间',
    create_user_id               varchar(32)    DEFAULT NULL COMMENT '创建人id',
    create_user_name             varchar(255)   DEFAULT NULL COMMENT '创建人',
    update_time                  datetime       DEFAULT NULL COMMENT '更新时间',
    update_user_id               varchar(32)    DEFAULT NULL COMMENT '更新人id',
    update_user_name             varchar(255)   DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (id)
);
alter table trd_basis_stock comment '基差商品表';

drop table if exists trd_basis_stock_cart;
create table trd_basis_stock_cart
(
    id                   bigint NOT NULL COMMENT '主键',
    stock_id             bigint       DEFAULT NULL COMMENT '商品id',
    stock_base_info_id   bigint       DEFAULT NULL COMMENT '商品基础信息id',
    warehouse_receipt_no varchar(32)  DEFAULT NULL COMMENT '仓单号',
    batch_no             varchar(32)  DEFAULT NULL COMMENT '批号',
    stock_cart_status    tinyint      DEFAULT NULL COMMENT '自选商品状态1添加自选2取消自选',
    create_user_name     varchar(255) DEFAULT NULL COMMENT '创建人',
    create_user_id       varchar(32)  DEFAULT NULL COMMENT '创建人id',
    create_time          datetime     DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (id)
);
alter table trd_basis_stock_cart comment '基差商品购物车表';

drop table if exists trd_basis_quote;
create table trd_basis_quote
(
    id                          bigint NOT NULL COMMENT '主键',
    stock_id                    bigint         DEFAULT NULL COMMENT '商品id',
    stock_code                  varchar(32)    DEFAULT NULL COMMENT '商品编码',
    stock_base_info_id          bigint         DEFAULT NULL COMMENT '商品基础信息id',
    user_name                   varchar(255)   DEFAULT NULL COMMENT '委托账户名称',
    trader_code                 varchar(32)    DEFAULT NULL COMMENT '委托交易商代码',
    trader_name                 varchar(255)   DEFAULT NULL COMMENT '委托交易商名称',
    quote_status                tinyint(1) DEFAULT NULL COMMENT '报价状态 1: 未成交, 2: 已成交, 3: 已撤销',
    quote_cancel_type           tinyint(1) DEFAULT NULL COMMENT '报价取消方式1 买家自主撤销, 2 其它买家成交, 3 卖家撤销商品, 4 卖家确认',
    quote_price                 decimal(18, 2) DEFAULT NULL COMMENT '委托价格',
    quote_weight                decimal(18, 4) DEFAULT NULL COMMENT '委托重量',
    buyer_basis_margin_type     tinyint        DEFAULT NULL COMMENT '买方基差保证金收取方式: 1-集团账户 2-电商',
    buyer_basis_margin_standard decimal(18, 2) DEFAULT NULL COMMENT '买方基差保证金标准',
    buyer_basis_margin_amount   decimal(18, 2) DEFAULT NULL COMMENT '买方保证金金额',
    buyer_trade_fee_type        tinyint        DEFAULT NULL COMMENT '买方交易手续费收取方式: 1-集团账户 2-电商',
    buyer_trade_fee_standard    decimal(18, 2) DEFAULT NULL COMMENT '买方交易手续费标准',
    buyer_trade_fee_amount      decimal(18, 2) DEFAULT NULL COMMENT '买方交易手续费金额',
    buyer_delivery_fee_type     tinyint        DEFAULT NULL COMMENT '买方交割手续费收取方式: 1-集团账户 2-电商',
    buyer_delivery_fee_standard decimal(18, 2) DEFAULT NULL COMMENT '买方交割手续费标准',
    buyer_delivery_fee_amount   decimal(18, 2) DEFAULT NULL COMMENT '买方交割手续费金额',
    create_time                 datetime       DEFAULT NULL COMMENT '创建时间',
    create_user_id              varchar(32)    DEFAULT NULL COMMENT '创建人id',
    create_user_name            varchar(255)   DEFAULT NULL COMMENT '创建人',
    update_time                 datetime       DEFAULT NULL COMMENT '更新时间',
    update_user_id              varchar(32)    DEFAULT NULL COMMENT '更新人id',
    update_user_name            varchar(255)   DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (id)
);
alter table trd_basis_quote comment '基差交易报价表';

-- 交割管理
drop table if exists trd_basis_delivery_target;
create table trd_basis_delivery_target
(
    id                                     bigint NOT NULL COMMENT '主键',
    transaction_no                         varchar(32)    DEFAULT NULL COMMENT '交易号',
    stock_id                               bigint         DEFAULT NULL COMMENT '商品id',
    stock_code                             varchar(32)    DEFAULT NULL COMMENT '商品编码',
    contract_id                            bigint         DEFAULT NULL COMMENT '合同id',
    stock_base_info_id                     bigint         DEFAULT NULL COMMENT '商品基础信息id',
    quote_id                               bigint         DEFAULT NULL COMMENT '报价id',
    warehouse_receipt_no                   varchar(32)    DEFAULT NULL COMMENT '仓单号',
    batch_no                               varchar(32)    DEFAULT NULL COMMENT '批号',
    storage_whs_code                       varchar(32)    DEFAULT NULL COMMENT '仓库代码',
    storage_whs_name                       varchar(255)   DEFAULT NULL COMMENT '仓库名称',
    storage_status                         int            DEFAULT NULL COMMENT '仓储业务状态：1、正常 2、注册中 3、注销中 4、移库中 5、过户中',
    whs_supervision_status                 int            DEFAULT NULL COMMENT '资金监管状态：1、正常 2、三方监管-籽棉贷款 3、三方监管-皮棉调销 4、三方监管-融资监管状态 5、质押监管 6、质押及三方监管',
    supervise_code                         varchar(32)    DEFAULT NULL COMMENT '监管机构代码',
    supervise_name                         varchar(255)   DEFAULT NULL COMMENT '监管机构名称',
    whs_pledge_channel                     int            DEFAULT NULL COMMENT '金融业务渠道：1正常、2购销、3金益棉、4棉贸通、5e棉通、6棉e通、7全棉通、8邮棉贷、9农商银行',
    seller_trader_code                     varchar(32)    DEFAULT NULL COMMENT '卖方交易商代码',
    seller_trader_name                     varchar(255)   DEFAULT NULL COMMENT '卖方交易商名称',
    buyer_trader_code                      varchar(32)    DEFAULT NULL COMMENT '买方交易商代码',
    buyer_trader_name                      varchar(255)   DEFAULT NULL COMMENT '买方交易商名称',
    quote_type                             tinyint        DEFAULT NULL COMMENT '报价方式: 1-买方点价',
    future_code                            varchar(255)   DEFAULT NULL COMMENT '期货合约',
    trade_basis_price                      decimal(18, 2) DEFAULT NULL COMMENT '基差成交价格',
    trade_weight                           decimal(18, 4) DEFAULT NULL COMMENT '成交重量',
    pricing_valid_end_time                 datetime       DEFAULT NULL COMMENT '点价有效结束日期',
    trade_date                             datetime       DEFAULT NULL COMMENT '成交时间',
    delivery_type                          tinyint        DEFAULT NULL COMMENT '交割方式：1先点价后交割2先交割后点价',
    pricing_status                         tinyint        DEFAULT NULL COMMENT '点价状态：1未点价2点价中3已点价',
    pricing_price                          decimal(18, 2) DEFAULT NULL COMMENT '点价价格',
    temp_futurn_price                      decimal(18, 2) DEFAULT NULL COMMENT '暂定期货价格',
    pricing_from                           tinyint        DEFAULT NULL COMMENT '点价类型1:点价击穿-自动2:系统成交-到期结算3:系统成交-风控触线4手动确认-卖方',
    pricing_time                           datetime       DEFAULT NULL COMMENT '点价时间',
    temp_settlement_price                  decimal(18, 2) DEFAULT NULL COMMENT '暂定结算价格',
    final_settlement_price                 decimal(18, 2) DEFAULT NULL COMMENT '最终结算价格',
    target_risk_control_type               tinyint        DEFAULT NULL COMMENT '风险类型：1正常2基差风险3点价风险',
    basis_risk_control_status              tinyint        DEFAULT NULL COMMENT '基差风险状态：1正常2卖方风险3买方风险',
    pricing_risk_control_status            tinyint        DEFAULT NULL COMMENT '点价风险状态：1正常2卖方追保3卖方强平4买方追保5买方强平',
    risk_control_time                      datetime       DEFAULT NULL COMMENT '风控时间',
    delivery_time                          datetime       DEFAULT NULL COMMENT '交割时间',
    payment_seller_amount_ratio            decimal(18, 2) DEFAULT NULL COMMENT '支付卖方货款比例',
    collection_buyer_amount_ratio          decimal(18, 2) DEFAULT NULL COMMENT '收买方货款比例',
    seller_credit_num                      int            DEFAULT NULL COMMENT '仓单抵免次数-卖方',
    seller_credit_amount                   decimal(18, 2) DEFAULT NULL COMMENT '仓单抵免总金额-卖方',
    buyer_credit_num                       int            DEFAULT NULL COMMENT '仓单抵免次数-买方',
    buyer_credit_amount                    decimal(18, 2) DEFAULT NULL COMMENT '仓单抵免总金额-买方',

    seller_basis_margin_type               tinyint        DEFAULT NULL COMMENT '卖方基差保证金收取方式: 1-集团账户 2-电商',
    seller_basis_margin_standard           decimal(18, 2) DEFAULT NULL COMMENT '卖方基差保证金标准',
    seller_basis_margin_amount             decimal(18, 2) DEFAULT NULL COMMENT '卖方基差保证金金额',
    seller_trade_fee_type                  tinyint        DEFAULT NULL COMMENT '卖方交易手续费收取方式: 1-集团账户 2-电商',
    seller_trade_fee_standard              decimal(18, 2) DEFAULT NULL COMMENT '卖方交易手续费标准',
    seller_trade_fee_amount                decimal(18, 2) DEFAULT NULL COMMENT '卖方手续费金额',
    seller_delivery_fee_type               tinyint        DEFAULT NULL COMMENT '卖方交割手续费收取方式: 1-集团账户 2-电商',
    seller_delivery_fee_standard           decimal(18, 2) DEFAULT NULL COMMENT '卖方交割手续费标准',
    seller_delivery_fee_amount             decimal(18, 2) DEFAULT NULL COMMENT '卖方交割手续费金额',


    seller_pricing_forced_liquidation_line decimal(18, 2) DEFAULT NULL COMMENT '卖方点价强平线',


    buyer_basis_margin_type                tinyint        DEFAULT NULL COMMENT '买方基差保证金收取方式: 1-集团账户 2-电商',
    buyer_basis_margin_standard            decimal(18, 2) DEFAULT NULL COMMENT '买方基差保证金标准',
    buyer_basis_margin_amount              decimal(18, 2) DEFAULT NULL COMMENT '买方基差保证金金额',
    buyer_trade_fee_type                   tinyint        DEFAULT NULL COMMENT '买方交易手续费收取方式: 1-集团账户 2-电商',
    buyer_trade_fee_standard               decimal(18, 2) DEFAULT NULL COMMENT '买方交易手续费标准',
    buyer_trade_fee_amount                 decimal(18, 2) DEFAULT NULL COMMENT '买方交易手续费金额',
    buyer_delivery_fee_type                tinyint        DEFAULT NULL COMMENT '买方交割手续费收取方式: 1-集团账户 2-电商',
    buyer_delivery_fee_standard            decimal(18, 2) DEFAULT NULL COMMENT '买方交割手续费标准',
    buyer_delivery_fee_amount              decimal(18, 2) DEFAULT NULL COMMENT '买方交割手续费金额',

    buyer_basis_margin_call_line           decimal(18, 2) DEFAULT NULL COMMENT '买方基差追保线',
    buyer_basis_margin_call_standard       decimal(18, 2) DEFAULT NULL COMMENT '买方基差追保标准',
    buyer_basis_margin_call_amount         decimal(18, 2) DEFAULT NULL COMMENT '买方基差保证金金额',
    buyer_pricing_margin_call_line         decimal(18, 2) DEFAULT NULL COMMENT '买方点价追保线',
    buyer_pricing_margin_call_standard     decimal(18, 2) DEFAULT NULL COMMENT '买方点价保证金标准',
    buyer_pricing_forced_liquidation_line  decimal(18, 2) DEFAULT NULL COMMENT '买方点价强平线',
    buyer_pricing_margin_amount            decimal(18, 2) DEFAULT NULL COMMENT '买方点价保证金金额',


    collection_buyer_amount                decimal(18, 2) DEFAULT NULL COMMENT '已收买方金额',
    payment_seller_amount                  decimal(18, 2) DEFAULT NULL COMMENT '已付卖方金额',
    buyer_pricing_margin_status            tinyint        DEFAULT NULL COMMENT '买方点价保证金状态1已冻结2已释放3已扣除',
    batch_delivery_status                  tinyint        DEFAULT NULL COMMENT '批次交割状态：1未收买方2已收买方3已过户4已支付5已违约6已完结',
    invoices_status                        tinyint        DEFAULT NULL COMMENT '发票状态：1未开票2已开票3买方已确认',
    invoices_confirm_time                  datetime       DEFAULT NULL COMMENT '发票确认时间',
    buyer_delivery_confirm_status          tinyint        DEFAULT NULL COMMENT '买方货权确认状态1未确认2已确认',
    delivery_confirm_time                  datetime       DEFAULT NULL COMMENT '货权确认时间',
    create_time                            datetime       DEFAULT NULL COMMENT '创建时间',
    create_user_id                         varchar(32)    DEFAULT NULL COMMENT '创建人id',
    create_user_name                       varchar(255)   DEFAULT NULL COMMENT '创建人',
    update_time                            datetime       DEFAULT NULL COMMENT '更新时间',
    update_user_id                         varchar(32)    DEFAULT NULL COMMENT '更新人id',
    update_user_name                       varchar(255)   DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (id)
);
alter table trd_basis_delivery_target comment '基差交割标的表';

drop table if exists trd_basis_contract;
create table trd_basis_contract
(
    id                 bigint NOT NULL COMMENT '主键',
    transaction_no     varchar(32)  DEFAULT NULL COMMENT '交易号',
    seller_trader_code varchar(32)  DEFAULT NULL COMMENT '卖方交易商代码',
    seller_trader_name varchar(255) DEFAULT NULL COMMENT '卖方交易商名称',
    buyer_trader_code  varchar(32)  DEFAULT NULL COMMENT '买方交易商代码',
    buyer_trader_name  varchar(255) DEFAULT NULL COMMENT '买方交易商名称',
    contract_status    tinyint      DEFAULT NULL COMMENT '合同状态：1未完结2已违约3已取消4已完结',
    delivery_type      tinyint      DEFAULT NULL COMMENT '交割方式：1先点价后交割2先交割后点价',
    create_time        datetime     DEFAULT NULL COMMENT '创建时间',
    create_user_id     varchar(32)  DEFAULT NULL COMMENT '创建人id',
    create_user_name   varchar(255) DEFAULT NULL COMMENT '创建人',
    update_time        datetime     DEFAULT NULL COMMENT '更新时间',
    update_user_id     varchar(32)  DEFAULT NULL COMMENT '更新人id',
    update_user_name   varchar(255) DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (id)
);
alter table trd_basis_contract comment '基差合同表';


drop table if exists trd_basis_pricing_command;
create table trd_basis_pricing_command
(
    id                     bigint NOT NULL COMMENT '主键',
    target_id              bigint         DEFAULT NULL COMMENT '标的id',
    transaction_no         varchar(32)    DEFAULT NULL COMMENT '交易号',
    pricing_price          decimal(18, 2) DEFAULT NULL COMMENT '点价价格',
    pricing_valid_time     datetime       DEFAULT NULL COMMENT '点价有效期',
    batch_no               varchar(32)    DEFAULT NULL COMMENT '批号',
    warehouse_receipt_no   varchar(32)    DEFAULT NULL COMMENT '仓单号',
    future_code            varchar(32)    DEFAULT NULL COMMENT '期货合约代码',
    pricing_type           tinyint        DEFAULT NULL COMMENT '点价类型 1买方点价 2卖方点价',
    pricing_commander_code varchar(32)    DEFAULT NULL COMMENT '点价方代码',
    pricing_commander_name varchar(255)   DEFAULT NULL COMMENT '点价方名称',
    pricing_taker_code     varchar(32)    DEFAULT NULL COMMENT '接收方代码',
    pricing_taker_name     varchar(255)   DEFAULT NULL COMMENT '接收方名称',
    command_status         tinyint        DEFAULT NULL COMMENT '指令状态1预生效、2已生效、3自主撤销、4系统撤销-到期结算--有指令时5系统撤销-风控触线-有指令时6点价击穿-自动7对方确认-卖方',
    command_create_time    datetime       DEFAULT NULL COMMENT '指令创建时间',
    command_effect_time    datetime       DEFAULT NULL COMMENT '指令生效时间',
    command_effect_type    tinyint        DEFAULT NULL COMMENT '指令生效方式1卖方确认2系统确认-到时',
    pricing_complete_time  datetime       DEFAULT NULL COMMENT '点价完成时间',
    pricing_cancel_time    datetime       DEFAULT NULL COMMENT '点价撤销时间',
    create_time            datetime       DEFAULT NULL COMMENT '创建时间',
    create_user_id         varchar(32)    DEFAULT NULL COMMENT '创建人id',
    create_user_name       varchar(255)   DEFAULT NULL COMMENT '创建人',
    update_time            datetime       DEFAULT NULL COMMENT '更新时间',
    update_user_id         varchar(32)    DEFAULT NULL COMMENT '更新人id',
    update_user_name       varchar(255)   DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (id)
);
alter table trd_basis_pricing_command comment '基差点价指令表';

drop table if exists trd_basis_risk_control;
create table trd_basis_risk_control
(
    id                   bigint NOT NULL COMMENT '主键',
    transaction_no       varchar(32)    DEFAULT NULL COMMENT '交易号',
    contract_id          bigint         DEFAULT NULL COMMENT '合同id',
    target_id            bigint         DEFAULT NULL COMMENT '标的id',
    stock_id             bigint         DEFAULT NULL COMMENT '商品id',
    stock_base_info_id   bigint         DEFAULT NULL COMMENT '商品基础信息id',
    file_id              bigint         DEFAULT NULL COMMENT '追保文件id',
    future_code          varchar(32)    DEFAULT NULL COMMENT '期货合约',
    batch_no             varchar(32)    DEFAULT NULL COMMENT '批号',
    warehouse_receipt_no varchar(32)    DEFAULT NULL COMMENT '仓单号',
    delivery_type        tinyint        DEFAULT NULL COMMENT '交割方式：1先点价后交割2先交割后点价',
    funds_type           tinyint        DEFAULT NULL COMMENT '保证金类型：1基差保证金2交易保证金',
    margin_call_standard decimal(18, 2) DEFAULT NULL COMMENT '追保标准',
    margin_call_amount   decimal(18, 2) DEFAULT NULL COMMENT '追保金额',
    margin_call_time     datetime       DEFAULT NULL COMMENT '追保时间',
    liquidation_price    decimal(18, 2) DEFAULT NULL COMMENT '强平点价',
    liquidation_time     datetime       DEFAULT NULL COMMENT '强平点价时间',
    payment_trader_code  varchar(32)    DEFAULT NULL COMMENT '追保方代码',
    payment_trader_name  varchar(255)   DEFAULT NULL COMMENT '追保方名称',
    risk_control_status  tinyint        DEFAULT NULL COMMENT '追保强平状态1盯市中2追保中3已追保4已强平',
    create_time          datetime       DEFAULT NULL COMMENT '创建时间',
    create_user_id       varchar(32)    DEFAULT NULL COMMENT '创建人id',
    create_user_name     varchar(255)   DEFAULT NULL COMMENT '创建人',
    update_time          datetime       DEFAULT NULL COMMENT '更新时间',
    update_user_id       varchar(32)    DEFAULT NULL COMMENT '更新人id',
    update_user_name     varchar(255)   DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (id)
);
alter table trd_basis_risk_control comment '风险控制表';

CREATE VIEW v_monitor_holidy AS
SELECT monitor.t_risk_holiday.ID AS ID,
       monitor.t_risk_holiday.YEAR AS YEAR,
    monitor.t_risk_holiday.HOLIDAY_DATE AS HOLIDAY_DATE,
    monitor.t_risk_holiday.WORK_DAY AS WORK_DAY,
    monitor.t_risk_holiday.HOLIDAY_NAME AS HOLIDAY_NAME,
    monitor.t_risk_holiday.HOLIDAY_TYPE AS HOLIDAY_TYPE,
    monitor.t_risk_holiday.WEEK_DAY AS WEEK_DAY,
    monitor.t_risk_holiday.CREATE_TIME AS CREATE_TIME,
    monitor.t_risk_holiday.CREATE_USER AS CREATE_USER,
    monitor.t_risk_holiday.CREATE_USER_ID AS CREATE_USER_ID,
    monitor.t_risk_holiday.UPDATE_TIME AS UPDATE_TIME,
    monitor.t_risk_holiday.UPDATE_USER AS UPDATE_USER,
    monitor.t_risk_holiday.UPDATE_USER_ID AS UPDATE_USER_ID
    FROM
    monitor.t_risk_holiday;

CREATE VIEW v_risk_price_index_info AS
SELECT t1.COTTON_INDEX_NAME,
       t1.COTTON_INDEX_PRICE,
       t1.RELEASE_DATA
FROM monitor.t_risk_price_index_info t1
WHERE t1.COTTON_INDEX_NAME = 'CC Index 3128B'
  AND t1.COTTON_TYPE = 2
ORDER BY t1.RELEASE_DATA DESC LIMIT 1;