
RENAME TABLE trd_basis_pricing_quote TO trd_basis_pricing_command;
alter table trd_basis_pricing_command comment '基差点价指令表';

ALTER TABLE `tradecenter_basiscotton`.`trd_basis_pricing_command`
    CHANGE COLUMN `pricing_custom_code` `pricing_commander_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '点价方代码' AFTER `pricing_price`,
    CHANGE COLUMN `pricing_custom_name` `pricing_commander_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '点价方名称' AFTER `pricing_commander_code`,
    CHANGE COLUMN `hedging_custom_code` `pricing_taker_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接收方代码' AFTER `pricing_commander_name`,
    CHANGE COLUMN `hedging_custom_name` `pricing_taker_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接收方名称' AFTER `pricing_taker_code`,
    CHANGE COLUMN `pricing_status` `command_status` tinyint NULL DEFAULT NULL COMMENT '指令状态\r\n1预生效、\r\n2已生效、\r\n3自主撤销、\r\n4系统撤销-到期结算--有指令时\r\n5系统撤销-风控触线-有指令时\r\n6点价击穿-自动\r\n7对方确认-卖方' AFTER `pricing_taker_name`,
    CHANGE COLUMN `pricing_type` `command_effect_type` tinyint NULL DEFAULT NULL COMMENT '指令生效方式\r\n1卖方确认\r\n2系统确认-到时\r\n' AFTER `command_status`,
    MODIFY COLUMN `target_id` bigint NULL DEFAULT NULL COMMENT '标的id' AFTER `id`,
    ADD COLUMN `batch_no` varchar(32) NULL COMMENT '批号' AFTER `transaction_no`,
    ADD COLUMN `warehouse_receipt_no` varchar(32) NULL COMMENT '仓单号' AFTER `batch_no`,
    ADD COLUMN `future_code` varchar(32) NULL COMMENT '期货合约代码' AFTER `warehouse_receipt_no`,
    ADD COLUMN `pricing_type` tinyint NULL COMMENT '点价类型 1买方点价 2卖方点价' AFTER `future_code`,
    MODIFY COLUMN `pricing_price` decimal(18, 2) NULL DEFAULT NULL COMMENT '点价价格' AFTER `transaction_no`,
    ADD COLUMN `pricing_valid_time` datetime NULL COMMENT '点价有效期' AFTER `pricing_price`,
    ADD COLUMN `command_create_time` datetime NULL COMMENT '指令创建时间' AFTER `command_status`,
    ADD COLUMN `command_effect_time` datetime NULL COMMENT '指令生效时间' AFTER `command_create_time`,
    ADD COLUMN `pricing_complete_time` datetime NULL COMMENT '点价完成时间' AFTER `command_effect_type`,
    ADD COLUMN `pricing_cancel_time` datetime NULL COMMENT '点价撤销时间' AFTER `pricing_complete_time`;



CREATE  VIEW `v_monitor_holidy` AS SELECT
                                       `monitor`.`t_risk_holiday`.`ID` AS `ID`,
                                       `monitor`.`t_risk_holiday`.`YEAR` AS `YEAR`,
                                       `monitor`.`t_risk_holiday`.`HOLIDAY_DATE` AS `HOLIDAY_DATE`,
                                       `monitor`.`t_risk_holiday`.`WORK_DAY` AS `WORK_DAY`,
                                       `monitor`.`t_risk_holiday`.`HOLIDAY_NAME` AS `HOLIDAY_NAME`,
                                       `monitor`.`t_risk_holiday`.`HOLIDAY_TYPE` AS `HOLIDAY_TYPE`,
                                       `monitor`.`t_risk_holiday`.`WEEK_DAY` AS `WEEK_DAY`,
                                       `monitor`.`t_risk_holiday`.`CREATE_TIME` AS `CREATE_TIME`,
                                       `monitor`.`t_risk_holiday`.`CREATE_USER` AS `CREATE_USER`,
                                       `monitor`.`t_risk_holiday`.`CREATE_USER_ID` AS `CREATE_USER_ID`,
                                       `monitor`.`t_risk_holiday`.`UPDATE_TIME` AS `UPDATE_TIME`,
                                       `monitor`.`t_risk_holiday`.`UPDATE_USER` AS `UPDATE_USER`,
                                       `monitor`.`t_risk_holiday`.`UPDATE_USER_ID` AS `UPDATE_USER_ID`
                                   FROM
                                       `monitor`.`t_risk_holiday`;

drop table if exists trd_basis_margin_call_liquidation;
drop table if exists trd_basis_risk_control;
create table trd_basis_risk_control
(
    id                             bigint(20) not null comment '主键',
    transaction_no                 varchar(32) comment '交易号',
    contract_id                    bigint(20) comment '合同id',
    target_id                      bigint(20) comment '标的id',
    stock_id                       bigint(20) comment '商品id',
    stock_base_info_id             bigint(20) comment '商品基础信息id',
    file_id                        bigint(20) comment '追保文件id',
    future_code                    varchar(32) COMMENT '期货合约',
    batch_no                       varchar(32) COMMENT '批号',
    warehouse_receipt_no           varchar(32) COMMENT '仓单号',
    delivery_type                  tinyint(4) comment '交割方式：1先点价后交割2先交割后点价',
    funds_type                     tinyint(4) comment '保证金类型：1基差保证金2交易保证金',
    margin_call_standard           decimal(18, 2) comment '追保标准',
    margin_call_amount             decimal(18, 2) comment '追保金额',
    margin_call_time               datetime comment '追保时间',
    liquidation_price              decimal(18, 2) comment '强平点价',
    liquidation_time               datetime comment '强平点价时间',
    payment_trader_code            varchar(32) comment '追保方代码',
    payment_trader_name            varchar(255) comment '追保方名称',
    risk_control_status            tinyint(4) comment '追保强平状态1盯市中2追保中3已追保4已强平',
    create_time                    datetime comment '创建时间',
    create_user_id                 varchar(32) comment '创建人id',
    create_user_name               varchar(255) comment '创建人',
    update_time                    datetime comment '更新时间',
    update_user_id                 varchar(32) comment '更新人id',
    update_user_name               varchar(255) comment '更新人',
    primary key (id)
);
alter table trd_basis_risk_control comment '风险控制表';
# 商品表增加商品重量
alter table trd_basis_stock
    add stock_weight decimal(12, 4) null comment '商品重量' after basis_price;

# 报价表更新报价状态字段
alter table trd_basis_quote
    modify quote_status tinyint(1) null comment '报价状态 1: 未成交, 2: 已成交, 3: 已撤销';
# 报价表添加报价取消方式字段
alter table trd_basis_quote
    add quote_cancel_type tinyint(1) null comment '报价取消方式1 买家自主撤销, 2 其它买家成交, 3 卖家撤销商品, 4 卖家确认' after quote_status;

# 2025-08-23
CREATE VIEW v_risk_price_index_info AS
SELECT
    t1.COTTON_INDEX_NAME,
    t1.COTTON_INDEX_PRICE,
    t1.RELEASE_DATA
FROM
    monitor.t_risk_price_index_info t1
WHERE
    t1.COTTON_INDEX_NAME = 'CC Index 3128B'
  AND t1.COTTON_TYPE = 2
ORDER BY t1.RELEASE_DATA DESC
LIMIT 1

-- 以上修改点同步到2.1ubutTable.sql


-- 2025-08-28 适应现货挂牌数据库调整
-- 商品表
alter table trd_basis_stock
    modify resource_audit_status tinyint(1) not null comment '资源审核状态: 1-未审核 2-已审核 3-已取消 4-未提交';

alter table trd_basis_stock
    add trade_mode tinyint(1) not null comment '交易模式: 1 现货挂牌 2 基差点价' after resource_audit_status;

alter table trd_basis_stock
    add basis_pricing_type tinyint(1) null comment '点价方式: 1 买方点价 2卖方点价' after pricing_party;

alter table trd_basis_stock
    add stock_price decimal(18, 2) null comment '商品价格' after basis_price;

alter table trd_basis_stock
    add stock_validity_end date null comment '挂牌有效期(截止日期)' after stock_weight;

alter table trd_basis_stock
    add negotiable tinyint(1) null comment '能否议价 0 否 1 是' after stock_validity_end;

alter table trd_basis_stock
    modify stock_source tinyint not null comment '商品来源: 1-仓单 2-...';

alter table trd_basis_stock
    modify trade_status tinyint null comment '交易状态: 1-未上架 2-未成交 3-已成交 4-已下架';

alter table trd_basis_stock
    modify delisting_reason tinyint(1) null comment '下架原因 1 卖方自主撤销 2 其它业务撤销 3 到期撤销';

alter table trd_basis_stock
    drop column pricing_price;

alter table trd_basis_stock
    drop column basis_price;

alter table trd_basis_stock
    drop column quote_type;

-- 报价表
alter table trd_basis_quote
    modify quote_cancel_type tinyint(1) null comment '报价取消方式1 买家自主撤销, 2 其它买家成交, 3 卖家撤销商品, 4 到期撤销';

alter table trd_basis_quote
    modify quote_price decimal(18, 2) null comment '报价价格';

alter table trd_basis_quote
    modify quote_weight decimal(18, 4) null comment '报价重量';

-- 交割标的表
alter table trd_basis_delivery_target
    add generate_contract_status tinyint(1) default 0 null comment '生成合同状态 0 否 1 是' after delivery_confirm_time;

-- 基差交易设置表
alter table trd_basis_market_setting
    modify seller_basis_margin_type tinyint DEFAULT NULL COMMENT '卖方基差保证金收取方式: 1-集团账户 2-电商 3-交易中心',
    modify seller_trade_fee_type tinyint DEFAULT NULL COMMENT '卖方交易手续费收取方式: 1-集团账户 2-电商 3-交易中心',
    modify seller_delivery_fee_type tinyint DEFAULT NULL COMMENT '卖方交割手续费收取方式: 1-集团账户 2-电商 3-交易中心',
    modify buyer_basis_margin_type tinyint DEFAULT NULL COMMENT '买方基差保证金收取方式: 1-集团账户 2-电商 3-交易中心',
    modify buyer_trade_fee_type tinyint DEFAULT NULL COMMENT '买方交易手续费收取方式: 1-集团账户 2-电商 3-交易中心',
    modify buyer_delivery_fee_type tinyint DEFAULT NULL COMMENT '买方交割手续费收取方式: 1-集团账户 2-电商 3-交易中心';


-- 市场设置表
ALTER TABLE `tradecenter_basiscotton`.`trd_basis_market_setting`
    ADD COLUMN `spot_system_status` tinyint NULL COMMENT '挂牌系统状态 1-正常，2-暂停' AFTER `setting_status`,
    ADD COLUMN `max_validity_submit_days` tinyint NULL COMMENT '挂牌系统-资源提交的最大有效天数' AFTER `spot_system_status`;

-- 视图
CREATE VIEW v_spot_funds AS
SELECT
    t1.CLIENT_CODE,
    t1.BALANCE,
    t1.AVAILABLE_BALANCE,
    t1.FROZEN_FUNDS,
    (t1.RETAINAGE+t1.PAYABLE_FUNDS+t1.RISK_MARGIN) as OTHER_FROZEN_FUNDS,
    COALESCE(t2.FROZEN_FUNDS,0) SPOT_FROZEN_FUNDS
FROM
    settlement_centre.sc_client_sub_funds t1
        LEFT JOIN settlement_centre.sc_client_frozen_funds_details t2 ON t1.SETTLE_CODE = t2.SETTLE_CODE AND t2.BUSINESS_MODULE_ID = 19 AND t2.FE_ACCOUNT_CODE = 'A301'
WHERE
    t1.FE_ACCOUNT_CODE = 'A301'

ALTER TABLE `tradecenter_basiscotton`.`trd_basis_stock`
    ADD COLUMN `transport_subsidy_apply_party` tinyint NULL COMMENT '运输申领方' AFTER `seller_delivery_fee_standard`,
    ADD COLUMN  `seller_freeze_margin_amount` decimal(18, 2) DEFAULT NULL COMMENT '卖方冻结保证金金额' AFTER `seller_basis_margin_standard`,
    ADD COLUMN  `seller_freeze_trade_fee_amount` decimal(18, 2) DEFAULT NULL COMMENT '卖方冻结交易手续费金额' AFTER `seller_trade_fee_standard`,
    ADD COLUMN  `seller_freeze_delivery_fee_amount` decimal(18, 2) DEFAULT NULL COMMENT '卖方冻结交割手续费金额' AFTER `seller_delivery_fee_standard`;


ALTER TABLE trd_basis_delivery_target add transport_subsidy_apply_party tinyint null comment '运费补贴申领方 1买方 2卖方';
