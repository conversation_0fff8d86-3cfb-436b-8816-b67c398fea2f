INSERT INTO trd_basis_market_setting(id, setting_name, basis_setting_type, trade_start_time, trade_end_time, buyer_basis_margin_call_line, buyer_basis_margin_call_standard, buyer_pricing_margin_call_line, buyer_pricing_margin_call_standard, buyer_pricing_forced_liquidation_line, seller_pricing_forced_liquidation_line, warehouse_receipt_deposit_times, warehouse_receipt_deposit_onetime_amount, seller_basis_margin_type, seller_basis_margin_standard, seller_trade_fee_type, seller_trade_fee_standard, seller_delivery_fee_type, seller_delivery_fee_standard, buyer_basis_margin_type, buyer_basis_margin_standard, buyer_trade_fee_type, buyer_trade_fee_standard, buyer_delivery_fee_type, buyer_delivery_fee_standard, payment_seller_amount_ratio, collection_buyer_amount_ratio, auto_pricing_time, contract_generate_status, audit_remark_status, setting_status, create_time, create_user_id, create_user_name, update_time, update_user_id, update_user_name) VALUES (1, '买方点价', '1', '08:30:00', '23:30:00', 700.00, 700.00, 4.00, 4.00, 8.00, 10.00, 10, 0.00, 1, 0.00, 1, 0.00, 1, 0.00, 1, 1000.00, 1, 2.00, 1, 8.00, 90.00, 110.00, 5, 1, 1, '1', NULL, '', '', '2025-08-25 18:57:28', '6000000000000018', '900001_管理员');
INSERT INTO `tradecenter_basiscotton`.`de_sys_sequence`(`SEQUENCE_ID`, `BIZ_CODE`, `BIZ_NAME`, `SEQUENCE_RESET_RULE`, `SEQUENCE_DELIMITER`, `SEQUENCE_STEP`, `CURRENT_CODE`, `START_NO`, `CURRENT_NO`, `SEQUENCE_RESET_VALUE`, `CREATE_BY`, `CREATE_TIME`, `UPDATE_BY`, `UPDATE_TIME`) VALUES (39, 'seq_gpspm', '现货挂牌商品码', 'DateYmd', NULL, 1, '', NULL, 27, '20250908', NULL, NULL, NULL, NULL);
INSERT INTO `tradecenter_basiscotton`.`de_sys_sequence_rule`(`RULE_ID`, `BIZ_CODE`, `RULE_NAME`, `RULE_SORT`, `RULE_TYPE`, `RULE_VALUE`, `PADDING_SIDE`, `PADDING_WIDTH`, `PADDING_CHAR`) VALUES (294, 'seq_gpspm', '常量值', 1, 'Const', 'GP', NULL, NULL, NULL);
INSERT INTO `tradecenter_basiscotton`.`de_sys_sequence_rule`(`RULE_ID`, `BIZ_CODE`, `RULE_NAME`, `RULE_SORT`, `RULE_TYPE`, `RULE_VALUE`, `PADDING_SIDE`, `PADDING_WIDTH`, `PADDING_CHAR`) VALUES (295, 'seq_gpspm', '8位日期', 2, 'Date', 'YYYYMMdd', NULL, NULL, NULL);
INSERT INTO `tradecenter_basiscotton`.`de_sys_sequence_rule`(`RULE_ID`, `BIZ_CODE`, `RULE_NAME`, `RULE_SORT`, `RULE_TYPE`, `RULE_VALUE`, `PADDING_SIDE`, `PADDING_WIDTH`, `PADDING_CHAR`) VALUES (296, 'seq_gpspm', '序号', 3, 'Numbering', NULL, 'left', 4, '0');
