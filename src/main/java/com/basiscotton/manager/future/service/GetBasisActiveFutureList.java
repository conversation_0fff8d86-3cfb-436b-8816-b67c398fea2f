package com.basiscotton.manager.future.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.BasisFuturesEntity;
import com.basiscotton.base.mappers.BasisFutureMapper;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Title: GetBasisActiveFutureList.java
 * @Description: 主力期货合约
 * <AUTHOR>
 * @date 2025/5/25
 * @version V1.0
 */
@Service("basisTrader.getBasisActiveFutureList.1")
@ApiRequestObject(value = "期货合约集合", name = "getBasisActiveFutureList", groups = {"交易商端-基差交易-主力期货合约"}, params = {

})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "activeFutureList", desc = "期货合约集合", type = BasisFuturesEntity.class,  pagination = true),
})
public class GetBasisActiveFutureList implements IBusinessService {

    @Resource
    private BasisFutureMapper basisFutureMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数

        //查询仓库升贴水数据
        LambdaQueryWrapper<BasisFuturesEntity> queryWrapper = Wrappers.lambdaQuery(BasisFuturesEntity.class);
       	queryWrapper.eq(BasisFuturesEntity::getActiveFuture, BasisCottonConstants.activeFuture_1);

        List<BasisFuturesEntity> activeFutureList = basisFutureMapper.selectList(queryWrapper);
        //创建响应实体
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("activeFutureList", activeFutureList);
    }

}
