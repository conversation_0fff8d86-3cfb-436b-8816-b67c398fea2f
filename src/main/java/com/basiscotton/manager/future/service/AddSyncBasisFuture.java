package com.basiscotton.manager.future.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.BasisFuturesEntity;
import com.basiscotton.base.mappers.BasisFutureMapper;
import com.basiscotton.futurequotes.vo.RealTimeQuoteVo;
import com.github.benmanes.caffeine.cache.Cache;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.id.core.service.BizIdGenerator;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Title: AddSyncBasisFuture.java
 * @Description: 同步期货合约
 * <AUTHOR>
 * @date 2025/5/23
 * @version V1.0
 */
@Service("basisManage.addSyncBasisFuture.1")
@ApiRequestObject(value = "同步期货合约", name = "addSyncBasisFuture", groups = {"管理端-基差交易-期货合约"}, params = {

})
@ApiResponseObject(params = {
		@ApiParamMeta(key = "success", desc = "返回结果", type = boolean.class),
        @ApiParamMeta(key = "message", desc = "返回消息", type = String.class),
})
public class AddSyncBasisFuture implements IBusinessService {

    @Resource
    private Cache<String, RealTimeQuoteVo> quotesCache;

    @Resource
    private BasisFutureMapper basisFutureMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {

        List<RealTimeQuoteVo> realTimeQuoteVoList = new ArrayList<>();
        //获取所有缓存数据,转入realTimeQuoteVoList
        quotesCache.asMap().values().forEach(realTimeQuoteVo -> {
            realTimeQuoteVoList.add(realTimeQuoteVo);
        });

        if(realTimeQuoteVoList != null && realTimeQuoteVoList.size() > 0){
            List<String> codeList = realTimeQuoteVoList.stream().map(vo -> vo.getFutureCode()).collect(Collectors.toList());
            LambdaQueryWrapper<BasisFuturesEntity>  queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(BasisFuturesEntity::getFutureCode, codeList);
            List<BasisFuturesEntity> basisList = basisFutureMapper.selectList(queryWrapper);

            if(basisList != null && basisList.size() > 0){
                for (RealTimeQuoteVo realTimeQuoteVo : realTimeQuoteVoList) {
                    boolean addStatus = true;
                    out:for(BasisFuturesEntity entity : basisList){
                        if(realTimeQuoteVo.getFutureCode().equals(entity.getFutureCode())){
                            addStatus = false;
                            break out;
                        }
                    }
                    if(addStatus){
                        BasisFuturesEntity basisFutures  = new BasisFuturesEntity();
                        basisFutures.setId(BizIdGenerator.getInstance().generateBizId());
                        basisFutures.setFutureCode(realTimeQuoteVo.getFutureCode());
                        basisFutures.setActiveFuture(BasisCottonConstants.activeFuture_0);
                        basisFutureMapper.insert( basisFutures);
                    }
                }
            }else{
                for (RealTimeQuoteVo realTimeQuoteVo : realTimeQuoteVoList) {
                    BasisFuturesEntity basisFutures  = new BasisFuturesEntity();
                    basisFutures.setId(BizIdGenerator.getInstance().generateBizId());
                    basisFutures.setFutureCode(realTimeQuoteVo.getFutureCode());
                    basisFutures.setActiveFuture(BasisCottonConstants.activeFuture_0);
                    basisFutureMapper.insert( basisFutures);
                }
            }
        }
        //构造返回数据
        this.createSuccessResponse(context);
        context.getResponseBody().setData("success", true);
        context.getResponseBody().setData("message", "同步成功");
    }

}
