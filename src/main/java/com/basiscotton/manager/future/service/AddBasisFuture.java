package com.basiscotton.manager.future.service;

import com.basiscotton.base.entity.BasisFuturesEntity;
import com.basiscotton.base.mappers.BasisFutureMapper;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.id.core.service.BizIdGenerator;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
/**
 * @Title: AddBasisFuture.java
 * @Description: 添加期货合约
 * <AUTHOR>
 * @date 2025/5/23
 * @version V1.0
 */
@Service("basisManage.addBasisFuture.1")
@ApiRequestObject(value = "添加期货合约", name = "addBasisFuture", groups = {"管理端-基差交易-期货合约"}, params = {
        @ApiParamMeta(key = "future", desc = "期货合约信息", type = BasisFuturesEntity.class),
})
@ApiResponseObject(params = {
		@ApiParamMeta(key = "success", desc = "返回结果", type = boolean.class),
        @ApiParamMeta(key = "message", desc = "返回消息", type = String.class),
})
public class AddBasisFuture implements IBusinessService {

    @Resource
    private BasisFutureMapper basisFutureMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        BasisFuturesEntity basisFuture = context.getValueObject(BasisFuturesEntity.class, "future");
        basisFuture.setId(BizIdGenerator.getInstance().generateBizId());
        basisFutureMapper.insert(basisFuture);
        //构造返回数据
        this.createSuccessResponse(context);
        context.getResponseBody().setData("success", true);
        context.getResponseBody().setData("message", "添加成功");
    }

}
