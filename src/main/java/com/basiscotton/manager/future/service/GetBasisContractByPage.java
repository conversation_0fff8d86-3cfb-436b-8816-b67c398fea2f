package com.basiscotton.manager.future.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.basiscotton.base.entity.BasisFuturesEntity;
import com.basiscotton.base.mappers.BasisFutureMapper;
import org.harry.dandelion.framework.core.params.PageParameter;
import org.harry.dandelion.framework.core.params.Pagination;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.harry.dandelion.framework.rep.support.page.PipPagination;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
/**
 * @Title: GetBasisFutureByPage.java
 * @Description: 期货合约列表
 * <AUTHOR>
 * @date 2025/5/23
 * @version V1.0
 */
@Service("basisManage.getBasisFutureByPage.1")
@ApiRequestObject(value = "期货合约列表", name = "getBasisFutureByPage", groups = {"管理端-基差交易-期货合约"}, params = {
        @ApiParamMeta(key = "pageParameter", desc = "分页参数", type = PageParameter.class),
        @ApiParamMeta(key = "futureCode", desc = "期货合约编码", type = String.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "pagination", desc = "查询分页列表", type = BasisFuturesEntity.class,pagination = true),
})
public class GetBasisContractByPage implements IBusinessService {

    @Resource
    private BasisFutureMapper basisFutureMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        PageParameter pageParameter = context.getValueObject(PageParameter.class, "pageParameter");
        String futureCode = context.getValueObject(String.class, "futureCode");
        //查询仓库升贴水数据
        LambdaQueryWrapper<BasisFuturesEntity> queryWrapper = Wrappers.lambdaQuery(BasisFuturesEntity.class);
        if(StringUtil.isNotEmpty(futureCode)) {
        	queryWrapper.like(BasisFuturesEntity::getFutureCode, futureCode);
        }
        queryWrapper.orderByDesc(BasisFuturesEntity::getCreateTime);
        Pagination<BasisFuturesEntity> pagination=new PipPagination<BasisFuturesEntity>(pageParameter);
        PipPagination<BasisFuturesEntity> pipPagination = new PipPagination<BasisFuturesEntity>(pageParameter);
        pagination= basisFutureMapper.selectPage(pipPagination, queryWrapper);
        //创建响应实体
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("pagination", pagination);
    }

}
