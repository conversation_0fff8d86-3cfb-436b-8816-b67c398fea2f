package com.basiscotton.manager.future.service;

import com.basiscotton.base.mappers.BasisFutureMapper;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
/**
 * @Title: DelBasisContract.java
 * @Description: 删除期货合约
 * <AUTHOR>
 * @date 2025/5/23
 * @version V1.0
 */
@Service("basisManage.delBasisFuture.1")
@ApiRequestObject(value = "删除期货合约", name = "delBasisFuture", groups = {"管理端-基差交易-期货合约"}, params = {
        @ApiParamMeta(key = "id", desc = "期货合约ID", type = String.class),
})
@ApiResponseObject(params = {
		@ApiParamMeta(key = "success", desc = "返回结果", type = boolean.class),
        @ApiParamMeta(key = "message", desc = "返回消息", type = String.class),
})
public class DelBasisFuture implements IBusinessService {

    @Resource
    private BasisFutureMapper basisFutureMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
    	String id = context.getValueObject(String.class, "id");
        basisFutureMapper.deleteById(id);
        //构造返回数据
        this.createSuccessResponse(context);
        context.getResponseBody().setData("success", true);
        context.getResponseBody().setData("message", "删除成功");
    }

}
