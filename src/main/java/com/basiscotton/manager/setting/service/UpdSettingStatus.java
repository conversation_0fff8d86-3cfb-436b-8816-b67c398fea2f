package com.basiscotton.manager.setting.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.MarketSettingEntity;
import com.basiscotton.base.mappers.BasisMarketSettingMapper;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Title: UpdSettingStatus.java
 * @Description: 启用/关闭市场设置
 * <AUTHOR>
 * @date 2025/6/10
 * @version V1.0
 */
@Deprecated
@Service("basisManage.updSettingStatus.1")
@ApiRequestObject(value = "启用/关闭公共设置", name = "updSettingStatus", groups = {"管理端-基差交易-市场设置"}, params = {
        @ApiParamMeta(key = "id", desc = "主键", type = String.class),
        @ApiParamMeta(key = "type", desc = "类型", type = String.class),
        @ApiParamMeta(key = "status", desc = "状态", type = String.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "success", desc = "返回结果", type = boolean.class),
        @ApiParamMeta(key = "message", desc = "返回消息", type = String.class),
})
public class UpdSettingStatus implements IBusinessService {


    @Resource
    private BasisMarketSettingMapper basisMarketSettingMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {
        String id = context.getValueObject(String.class, "id");
        String type = context.getValueObject(String.class, "type");
        String status = context.getValueObject(String.class, "status");
        if(status.equals(BasisCottonConstants.COMMON_YES)){
            LambdaQueryWrapper<MarketSettingEntity> queryWrapper = new LambdaQueryWrapper<MarketSettingEntity>();
            queryWrapper.ne(MarketSettingEntity::getId,id);
            queryWrapper.eq(MarketSettingEntity::getSettingStatus, BasisCottonConstants.COMMON_YES);
            queryWrapper.eq(MarketSettingEntity::getBasisSettingType,type);
            if(basisMarketSettingMapper.selectCount(queryWrapper)>0){
                throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "同一个类型无法启用多个保证金、手续费设置");
            }
        }
    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        String id = context.getValueObject(String.class, "id");
        String status = context.getValueObject(String.class, "status");
        LambdaUpdateWrapper<MarketSettingEntity> updateWrapper = new LambdaUpdateWrapper<MarketSettingEntity>();
        updateWrapper.eq(MarketSettingEntity::getId,id);
        updateWrapper.set(MarketSettingEntity::getSettingStatus,status);
        basisMarketSettingMapper.update(null,updateWrapper);
        //构造返回数据
        this.createSuccessResponse(context);
        context.getResponseBody().setData("success", true);
        context.getResponseBody().setData("message", "状态变更成功");
    }
}
