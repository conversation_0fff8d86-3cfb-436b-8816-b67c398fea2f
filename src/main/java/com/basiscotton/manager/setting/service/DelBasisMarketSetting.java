package com.basiscotton.manager.setting.service;

import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.MarketSettingEntity;
import com.basiscotton.base.mappers.BasisMarketSettingMapper;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Title: DelBasisMarketSetting.java
 * @Description: 删除市场设置
 * @Author: gyz
 * @Date: 2025-06-05
 */
@Service("basisManage.delBasisMarketSetting.1")
@ApiRequestObject(value = "删除市场设置", name = "delBasisMarketSetting", groups = {"管理端-基差交易-市场设置"}, params = {
        @ApiParamMeta(key = "id", desc = "市场设置ID", type = String.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "success", desc = "返回结果", type = boolean.class),
        @ApiParamMeta(key = "message", desc = "返回消息", type = String.class),
})
public class DelBasisMarketSetting implements IBusinessService {

    @Resource
    private BasisMarketSettingMapper basisMarketSettingMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {
        //获取参数
        String id = context.getValueObject(String.class, "id");
        MarketSettingEntity marketSettingEntity = basisMarketSettingMapper.selectById(id);
        String status = marketSettingEntity.getSettingStatus();
        if(status.equals(BasisCottonConstants.COMMON_YES)){
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "已启用的设置不允许删除，请停用后重试");
        }
    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        String id = context.getValueObject(String.class, "id");
        basisMarketSettingMapper.deleteById(id);

        //构造返回数据
        this.createSuccessResponse(context);
        context.getResponseBody().setData("success", true);
        context.getResponseBody().setData("message", "删除成功");
}
}
