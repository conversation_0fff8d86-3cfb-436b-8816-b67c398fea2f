package com.basiscotton.manager.setting.service;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.basiscotton.base.entity.MarketSettingEntity;
import com.basiscotton.base.mappers.BasisMarketSettingMapper;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * @Title: UpdBasisMarketSetting.java
 * @Description: 编辑基差交易市场设置
 * @Author: gyz
 * @Date: 2025-06-05
 */

@Service("basisManage.updBasisMarketSetting.1")
@ApiRequestObject(value = "编辑市场设置", name = "updBasisMarketSetting", groups = {"管理端-基差交易-市场设置"}, params = {
        @ApiParamMeta(key = "marketSetting", desc = "市场设置", type = MarketSettingEntity.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "success", desc = "返回结果", type = boolean.class),
        @ApiParamMeta(key = "message", desc = "返回消息", type = String.class),
})
public class UpdBasisMarketSetting implements IBusinessService {


    @Resource
    private BasisMarketSettingMapper basisMarketSettingMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        MarketSettingEntity marketSettingEntity = context.getValueObject(MarketSettingEntity.class, "marketSetting");
        LambdaUpdateWrapper<MarketSettingEntity> updateWrapper = new LambdaUpdateWrapper<MarketSettingEntity>();
        updateWrapper.eq(MarketSettingEntity::getId, marketSettingEntity.getId());
        updateWrapper.set(MarketSettingEntity::getSettingName, marketSettingEntity.getSettingName());
        updateWrapper.set(MarketSettingEntity::getTradeStartTime, marketSettingEntity.getTradeStartTime());
        updateWrapper.set(MarketSettingEntity::getTradeEndTime, marketSettingEntity.getTradeEndTime());
        updateWrapper.set(MarketSettingEntity::getAuditRemarkStatus, marketSettingEntity.getAuditRemarkStatus());
        updateWrapper.set(MarketSettingEntity::getBasisSettingType, marketSettingEntity.getBasisSettingType());
        updateWrapper.set(MarketSettingEntity::getBuyerBasisMarginCallLine, marketSettingEntity.getBuyerBasisMarginCallLine());
        updateWrapper.set(MarketSettingEntity::getBuyerBasisMarginCallStandard, marketSettingEntity.getBuyerBasisMarginCallStandard());
        updateWrapper.set(MarketSettingEntity::getBuyerPricingMarginCallLine, marketSettingEntity.getBuyerPricingMarginCallLine());
        updateWrapper.set(MarketSettingEntity::getBuyerPricingMarginCallStandard, marketSettingEntity.getBuyerPricingMarginCallStandard());
        updateWrapper.set(MarketSettingEntity::getBuyerPricingForcedLiquidationLine, marketSettingEntity.getBuyerPricingForcedLiquidationLine());
        updateWrapper.set(MarketSettingEntity::getSellerPricingForcedLiquidationLine, marketSettingEntity.getSellerPricingForcedLiquidationLine());
        updateWrapper.set(MarketSettingEntity::getSellerBasisMarginType, marketSettingEntity.getSellerBasisMarginType());
        updateWrapper.set(MarketSettingEntity::getSellerBasisMarginStandard, marketSettingEntity.getSellerBasisMarginStandard());
        updateWrapper.set(MarketSettingEntity::getSellerTradeFeeType, marketSettingEntity.getSellerTradeFeeType());
        updateWrapper.set(MarketSettingEntity::getSellerTradeFeeStandard, marketSettingEntity.getSellerTradeFeeStandard());
        updateWrapper.set(MarketSettingEntity::getSellerDeliveryFeeType, marketSettingEntity.getSellerDeliveryFeeType());
        updateWrapper.set(MarketSettingEntity::getSellerDeliveryFeeStandard, marketSettingEntity.getSellerDeliveryFeeStandard());
        updateWrapper.set(MarketSettingEntity::getBuyerBasisMarginType, marketSettingEntity.getBuyerBasisMarginType());
        updateWrapper.set(MarketSettingEntity::getBuyerBasisMarginStandard, marketSettingEntity.getBuyerBasisMarginStandard());
        updateWrapper.set(MarketSettingEntity::getBuyerTradeFeeType, marketSettingEntity.getBuyerTradeFeeType());
        updateWrapper.set(MarketSettingEntity::getBuyerTradeFeeStandard, marketSettingEntity.getBuyerTradeFeeStandard());
        updateWrapper.set(MarketSettingEntity::getBuyerDeliveryFeeType, marketSettingEntity.getBuyerDeliveryFeeType());
        updateWrapper.set(MarketSettingEntity::getBuyerDeliveryFeeStandard, marketSettingEntity.getBuyerDeliveryFeeStandard());
        updateWrapper.set(MarketSettingEntity::getPaymentSellerAmountRatio, marketSettingEntity.getPaymentSellerAmountRatio());
        updateWrapper.set(MarketSettingEntity::getCollectionBuyerAmountRatio, marketSettingEntity.getCollectionBuyerAmountRatio());
        updateWrapper.set(MarketSettingEntity::getAutoPricingTime, marketSettingEntity.getAutoPricingTime());
        updateWrapper.set(MarketSettingEntity::getContractGenerateStatus, marketSettingEntity.getContractGenerateStatus());
        updateWrapper.set(MarketSettingEntity::getSpotSystemStatus, marketSettingEntity.getSpotSystemStatus());
        updateWrapper.set(MarketSettingEntity::getUpdateTime, LocalDateTime.now());
        basisMarketSettingMapper.update(marketSettingEntity, updateWrapper);
        //构造返回数据
        this.createSuccessResponse(context);
        context.getResponseBody().setData("success", true);
        context.getResponseBody().setData("message", "修改成功");
    }
}
