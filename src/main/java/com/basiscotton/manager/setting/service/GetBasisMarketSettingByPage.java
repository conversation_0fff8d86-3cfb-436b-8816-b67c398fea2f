package com.basiscotton.manager.setting.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.basiscotton.base.ExceptionEnums;
import com.basiscotton.base.entity.MarketSettingEntity;
import com.basiscotton.base.mappers.BasisMarketSettingMapper;
import com.basiscotton.common.idempotence.IdempotentService;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.params.PageParameter;
import org.harry.dandelion.framework.core.params.Pagination;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.harry.dandelion.framework.rep.support.page.PipPagination;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Title: GetBasisMarketSettingByPage.java
 * @Description: 查询基差交易市场设置
 * @Author: gyz
 * @Date: 2025-06-05
 */

@Service("basisManage.getBasisMarketSettingByPage.1")
@ApiRequestObject(value = "市场设置列表", name = "getBasisMarketSettingByPage", groups = {"基差交易-市场设置"}, params = {
        @ApiParamMeta(key = "pageParameter", desc = "分页参数", type = PageParameter.class),
        @ApiParamMeta(key = "settingName", desc = "名称查询", type = String.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "pagination", desc = "查询分页列表", type = MarketSettingEntity.class,pagination = true),
})
public class GetBasisMarketSettingByPage implements IBusinessService {

    @Resource
    private BasisMarketSettingMapper basisMarketSettingMapper;
    @Resource
    private IdempotentService idempotentService;
    @Override
    public void doVerify(ServiceHandlerContext context) {

        boolean  absent =  idempotentService.idempotentVerify( context, 30);
        if (Boolean.TRUE.equals(absent)) {
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, ExceptionEnums.S0005.getEMsg());
        }
        //验证分页参数是否为空
        PageParameter pageParameter = context.getValueObject(PageParameter.class, "pageParameter");
        if (pageParameter == null) {
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "参数不能为空");
        }
    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        PageParameter pageParameter = context.getValueObject(PageParameter.class, "pageParameter");
        String settingName = context.getValueObject(String.class, "settingName");
        //查询公共设置列表数据
        LambdaQueryWrapper<MarketSettingEntity> queryWrapper = Wrappers.lambdaQuery(MarketSettingEntity.class);
        if(StringUtil.isNotEmpty(settingName)) {
            queryWrapper.like(MarketSettingEntity::getSettingName,settingName);
        }
        queryWrapper.orderByDesc(MarketSettingEntity::getCreateTime);
        Pagination<MarketSettingEntity> pagination=new PipPagination<MarketSettingEntity>(pageParameter);
        PipPagination<MarketSettingEntity> pipPagination = new PipPagination<MarketSettingEntity>(pageParameter);
        pagination=basisMarketSettingMapper.selectPage(pipPagination, queryWrapper);
        //创建响应实体
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("pagination", pagination);
    }
}
