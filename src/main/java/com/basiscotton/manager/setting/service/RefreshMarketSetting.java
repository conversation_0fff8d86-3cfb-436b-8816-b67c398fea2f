package com.basiscotton.manager.setting.service;

import com.basiscotton.common.marketSeting.BasisMarketSettingService;
import com.basiscotton.common.marketSeting.SpotMarketSettingService;
import com.basiscotton.common.tradetime.service.BasisTradeTimeWindowService;
import com.basiscotton.common.tradetime.service.SpotTradeTimeWindowService;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Title: RefreshMarketSetting.java
 * @Description: 刷新市场设置
 * <AUTHOR>
 * @date 2025/6/10
 * @version V1.0
 */

@Service("basisManage.refreshMarketSetting.1")
@ApiRequestObject(value = "刷新市场设置", name = "refreshMarketSetting", groups = {"管理端-基差交易-市场设置"}, params = {
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "success", desc = "返回结果", type = boolean.class),
        @ApiParamMeta(key = "message", desc = "返回消息", type = String.class),
})
public class RefreshMarketSetting implements IBusinessService {


    @Resource
    private BasisMarketSettingService basisMarketSettingService;
    @Resource
    private BasisTradeTimeWindowService basisTradeTimeWindowService;
    @Resource
    private SpotMarketSettingService spotMarketSettingService;
    @Resource
    private SpotTradeTimeWindowService spotTradeTimeWindowService;


    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        basisMarketSettingService.refreshMarketSeting();
        basisTradeTimeWindowService.refreshTradeTimeWindow();
        spotMarketSettingService.refreshMarketSeting();
        spotTradeTimeWindowService.refreshTradeTimeWindow();
        //构造返回数据
        this.createSuccessResponse(context);
        context.getResponseBody().setData("success", true);
        context.getResponseBody().setData("message", "刷新成功");
    }
}
