package com.basiscotton.manager.setting.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.MarketSettingEntity;
import com.basiscotton.base.mappers.BasisMarketSettingMapper;
import org.apache.kafka.common.protocol.types.Field;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.id.core.service.BizIdGenerator;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Title: AddBasisMarketSetting.java
 * @Description: 添加基差交易市场设置
 * @Author: gyz
 * @Date: 2025-06-05
 * @version V1.0
 */
@Service("basisManage.addBasisMarketSetting.1")
@ApiRequestObject(value = "添加市场设置", name = "addBasisMarketSetting", groups = {"管理端-基差交易-市场设置"}, params = {
        @ApiParamMeta(key = "marketSetting", desc = "市场设置", type = MarketSettingEntity.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "success", desc = "返回结果", type = boolean.class),
        @ApiParamMeta(key = "message", desc = "返回消息", type = String.class),
})
public class AddBasisMarketSetting implements IBusinessService {


    @Resource
    private BasisMarketSettingMapper basisMarketSettingMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {
        MarketSettingEntity marketSettingEntity = context.getValueObject(MarketSettingEntity.class, "marketSetting");
        String type = marketSettingEntity.getBasisSettingType().getCode();
        LambdaQueryWrapper<MarketSettingEntity> queryWrapper = new LambdaQueryWrapper<MarketSettingEntity>();
        queryWrapper.eq(MarketSettingEntity::getSettingStatus, BasisCottonConstants.COMMON_YES);
        queryWrapper.eq(MarketSettingEntity::getBasisSettingType,type);
        if(basisMarketSettingMapper.selectCount(queryWrapper)>0){
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "已存在同类型市场设置");
        }
    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        MarketSettingEntity marketSettingEntity = context.getValueObject(MarketSettingEntity.class, "marketSetting");
        marketSettingEntity.setId(BizIdGenerator.getInstance().generateBizId());
        marketSettingEntity.setSettingStatus(BasisCottonConstants.COMMON_YES);
        basisMarketSettingMapper.insert(marketSettingEntity);
        //构造返回数据
        this.createSuccessResponse(context);
        context.getResponseBody().setData("success", true);
        context.getResponseBody().setData("message", "添加成功");
    }
}
