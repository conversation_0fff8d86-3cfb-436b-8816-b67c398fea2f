package com.basiscotton.manager.contract.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: BasisContractParamVo.java
 * @Description: 基差合同查询类
 * @date 2025/8/23
 */
@Data
public class BasisContractParamVo implements Serializable {

      /**
       * 交易号
       */
      @ApiModelProperty("交易号")
      private String transactionNo;
      /**
       * 卖方交易商代码
       */
      @ApiModelProperty("卖方交易商代码")
      private String sellerTraderCode;
      /**
       * 卖方交易商名称
       */
      @ApiModelProperty("卖方交易商名称")
      private String sellerTraderName;
      /**
       * 买方交易商代码
       */
      @ApiModelProperty("买方交易商代码")
      private String buyerTraderCode;
      /**
       * 买方交易商名称
       */
      @ApiModelProperty("买方交易商名称")
      private String buyerTraderName;
      /**
       * 合同状态：1未完结2已违约3已取消4已完结
       */
      @ApiModelProperty("合同状态：1未完结2已违约3已取消4已完结")
      private String contractStatus;
      /**
       * 交割方式：1先点价后交割2先交割后点价
       */
      @ApiModelProperty("交割方式：1先点价后交割2先交割后点价")
      private String deliveryType;
}
