package com.basiscotton.manager.contract.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.basiscotton.base.entity.BasisDeliveryTargetEntity;
import com.sinosoft.dandelion.system.client.params.DataItem;
import com.sinosoft.dandelion.system.client.serializer.DataItemCodeDeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Transient;
import java.io.Serializable;
import java.util.List;

@Data
public class BasisContractResVo implements Serializable {

	@ApiModelProperty(value="主键")
	@JSONField(serializeUsing = ToStringSerializer.class)
	private Long id;

	@ApiModelProperty("交易号")
	private String transactionNo;

	@ApiModelProperty("卖方交易商代码")
	private String sellerTraderCode;

	@ApiModelProperty("卖方交易商名称")
	private String sellerTraderName;

	@ApiModelProperty("买方交易商代码")
	private String buyerTraderCode;

	@ApiModelProperty("买方交易商名称")
	private String buyerTraderName;

	@ApiModelProperty("合同状态：1未完结2已违约3已取消4已完结")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem contractStatus;

	@ApiModelProperty("交割方式：1先点价后交割2先交割后点价")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem deliveryType;

	@Transient
	@ApiModelProperty(value = "交割标的信息")
	List<BasisDeliveryTargetEntity> basisDeliveryTargetEntities;

}