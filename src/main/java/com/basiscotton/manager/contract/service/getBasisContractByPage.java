package com.basiscotton.manager.contract.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.basiscotton.base.entity.BasisDeliveryTargetEntity;
import com.basiscotton.base.mappers.BasisContractMapper;
import com.basiscotton.base.mappers.BasisDeliveryTargetMapper;
import com.basiscotton.manager.contract.vo.BasisContractParamVo;
import com.basiscotton.manager.contract.vo.BasisContractResVo;
import lombok.extern.slf4j.Slf4j;
import org.harry.dandelion.framework.core.params.PageParameter;
import org.harry.dandelion.framework.core.params.Pagination;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.rep.support.page.PipPagination;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: getBasisContractByPage.java
 * @Description: 获取基差合同列表
 * @date 2025/8/23 11:24
 */
@Slf4j
@Service("basisContract.getBasisContractByPage.1")
@ApiRequestObject(value = "获取基差合同列表", name = "getBasisContractByPage",  groups= {"管理端-合同管理-成交订单"},params= {
        @ApiParamMeta(key = "pageParameter",  desc = "分页参数", type = PageParameter.class),
        @ApiParamMeta(key = "basisContractParamVo", desc = "查询参数", type = BasisContractParamVo.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "pagination", desc = "查询分页列表", type = BasisContractResVo.class),
})
public class getBasisContractByPage implements IBusinessService {

    @Resource
     private BasisContractMapper basisContractMapper;

    @Resource
     private BasisDeliveryTargetMapper basisDeliveryTargetMapper;
    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        PageParameter pageParameter = context.getValueObject(PageParameter.class, "pageParameter");
        BasisContractParamVo paramVo = context.getValueObject(BasisContractParamVo.class, "basisContractParamVo");
        // 组装查询参数
        PipPagination<BasisContractResVo> pagination = new PipPagination<BasisContractResVo>(pageParameter);
        pagination = basisContractMapper.selectBasisContractByPage(pagination,paramVo);
        // 查询基差标的信息
        this.createTarget(pagination);
        //创建响应实体
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("pagination", pagination);
    }

    /**
     * 查询基差标的信息
     * @param result
     */
    private void createTarget(Pagination<BasisContractResVo> result) {
        //没合同直接返回
        if(result.getResult() == null){
            return;
        }
        // 全部交易号
        List<String> transactionNos = new ArrayList<>();
        for (BasisContractResVo r : result.getResult()) {
            if (r.getTransactionNo() != null) {
                transactionNos.add(r.getTransactionNo());
            }
        }
        try {
            // 批量查询所有基差标的信息
            LambdaQueryWrapper<BasisDeliveryTargetEntity> queryWrapper = Wrappers.lambdaQuery(BasisDeliveryTargetEntity.class);
            queryWrapper.in(BasisDeliveryTargetEntity::getTransactionNo, transactionNos);
            List<BasisDeliveryTargetEntity> allTargetList = basisDeliveryTargetMapper.selectList(queryWrapper);

            // 按交易号分组
            Map<String, List<BasisDeliveryTargetEntity>> targetMap = allTargetList.stream()
                    .collect(Collectors.groupingBy(BasisDeliveryTargetEntity::getTransactionNo));

            // 设置到对应的结果对象中
            for (BasisContractResVo r : result.getResult()) {
                if (r.getTransactionNo() != null) {
                    List<BasisDeliveryTargetEntity> targetList = targetMap.getOrDefault(r.getTransactionNo(), new ArrayList<>());
                    r.setBasisDeliveryTargetEntities(targetList);
                }
            }
        } catch (Exception e) {
            log.error("查询基差标的信息异常", e);
        }
    }
}
