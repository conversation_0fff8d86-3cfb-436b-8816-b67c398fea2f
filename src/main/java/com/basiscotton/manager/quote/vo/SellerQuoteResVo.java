package com.basiscotton.manager.quote.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.sinosoft.dandelion.system.client.params.DataItem;
import com.sinosoft.dandelion.system.client.serializer.DataItemCodeDeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Title: SellerQuoteResVo.java
 * @Description: 卖方委托列表返回结果
 * <AUTHOR>
 * @date 2025/5/25
 * @version V1.0
 */
@Data
public class SellerQuoteResVo implements Serializable {

	@ApiModelProperty(value="主键")
	@JSONField(serializeUsing = ToStringSerializer.class)
	private Long id;

	@ApiModelProperty(value="商品ID")
	@JSONField(serializeUsing = ToStringSerializer.class)
	private Long stockId;

	@ApiModelProperty(value = "商品码")
	private String stockCode;

	@ApiModelProperty(value = "批号")
	private String batchNo;

	@ApiModelProperty(value = "交易状态: 1-未成交 2-已成交 3-已流拍")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem tradeStatus;

	@ApiModelProperty(value = "重量")
	private BigDecimal stockWeight;

	@ApiModelProperty(value = "卖方价格")
	private BigDecimal stockPrice;

	@ApiModelProperty(value = "报价方式: 1-一口价 2-锁基差 3-即时点价")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem quoteType;

	@ApiModelProperty(value="期货合约")
	private String futureCode;

	@ApiModelProperty(value = "报价价格")
	private String quotePrice;

	@ApiModelProperty(value = "卖方保证金金额")
	private BigDecimal sellerMarginAmount;

	@ApiModelProperty(value = "卖方手续费金额")
	private BigDecimal sellerFeeAmount;

	@ApiModelProperty(value = "创建时间")
	private Date createTime;

	@ApiModelProperty(value = "运输申领方")
	private DataItem transportSubsidyApplyParty;

}

