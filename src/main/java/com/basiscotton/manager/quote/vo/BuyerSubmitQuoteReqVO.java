package com.basiscotton.manager.quote.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class BuyerSubmitQuoteReqVO {

    @NotBlank(message = "商品 ID 不能为空")
    @ApiModelProperty(value = "商品 ID")
    private String stockId;

    @NotNull(message = "报价价格不能为空")
    @Min(value = 0, message = "报价价格不能小于0")
    @ApiModelProperty(value = "报价价格")
    private BigDecimal quotePrice;

}
