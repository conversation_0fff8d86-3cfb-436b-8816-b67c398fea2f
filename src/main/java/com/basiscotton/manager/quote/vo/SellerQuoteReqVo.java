package com.basiscotton.manager.quote.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Title: SellerQuoteReqVo.java
 * @Description: 卖方委托查询vo
 * <AUTHOR>
 * @date 2025/5/25
 * @version V1.0
 */
@Data
public class SellerQuoteReqVo implements Serializable {

	//默认查询项
	private List<String> tradeStatus;
	private String traderCode;

	//页面查询项
	@ApiModelProperty(value = "商品码")
	private String stockCode;

	@ApiModelProperty(value = "批号")
	private String batchNo;

}

