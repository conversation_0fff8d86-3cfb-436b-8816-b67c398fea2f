package com.basiscotton.manager.quote.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class CancelBuyerQuoteReqVO {

    @NotBlank(message = "商品 ID 不能为空")
    @ApiModelProperty(value = "商品 ID")
    private String stockId;

    @NotBlank(message = "报价 ID 不能为空")
    @ApiModelProperty(value = "报价 ID")
    private String quoteId;

}
