package com.basiscotton.manager.quote.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Title: BuyerQuoteReqVo.java
 * @Description: 买方委托查询vo
 * <AUTHOR>
 * @date 2025/5/25
 * @version V1.0
 */
@Data
public class BuyerQuoteReqVo implements Serializable {

	//默认查询项
	private List<String> tradeStatus;
	private String traderCode;

	//页面查询项
	@ApiModelProperty(value = "商品码")
	private String stockCode;

}

