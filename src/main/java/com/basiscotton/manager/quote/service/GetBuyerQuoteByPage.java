package com.basiscotton.manager.quote.service;

import com.basiscotton.base.mappers.BasisQuoteMapper;
import com.basiscotton.manager.quote.vo.BuyerQuoteReqVo;
import com.basiscotton.manager.quote.vo.BuyerQuoteResVo;
import org.harry.dandelion.framework.core.params.PageParameter;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.rep.support.page.PipPagination;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Title: GetBuyerQuoteByPage.java
 * @Description: 买方委托信息列表
 * <AUTHOR>
 * @date 2025/5/25
 * @version V1.0
 */
@Service("basisQuote.getBuyerQuoteByPage.1")
@ApiRequestObject(value = "买方委托信息列表-交易商", name = "getBuyerQuoteByPage", groups = {"交易商端-基差交易-委托管理"}, params = {
        @ApiParamMeta(key = "pageParameter", desc = "分页参数", type = PageParameter.class),
        @ApiParamMeta(key = "reqVo", desc = "查询vo", type = BuyerQuoteReqVo.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "pagination", desc = "查询分页列表", type = BuyerQuoteResVo.class,pagination = true),
})
public class GetBuyerQuoteByPage implements IBusinessService {

    @Resource
    private BasisQuoteMapper basisQuoteMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {

        String traderCode = context.getCurrentUserCustomCode();

        BuyerQuoteReqVo reqVo = context.getValueObject(BuyerQuoteReqVo.class, "reqVo");
        reqVo.setTraderCode(traderCode);

        //获取参数
        PageParameter pageParameter = context.getValueObject(PageParameter.class, "pageParameter");
        PipPagination<BuyerQuoteResVo> pipPagination = new PipPagination<>(pageParameter);
        pipPagination = basisQuoteMapper.getBuyerQuoteByPage(pipPagination, reqVo);

        //创建响应实体
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("pagination", pipPagination);
    }

}
