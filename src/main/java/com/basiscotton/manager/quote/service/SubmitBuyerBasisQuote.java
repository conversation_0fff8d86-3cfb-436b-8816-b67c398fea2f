package com.basiscotton.manager.quote.service;

import cn.hutool.extra.validation.BeanValidationResult;
import cn.hutool.extra.validation.ValidationUtil;
import com.basiscotton.base.enums.TradeMode;
import com.basiscotton.manager.quote.vo.BuyerSubmitQuoteReqVO;
import com.basiscotton.tradingcore.business.QuoteService;
import com.basiscotton.tradingcore.dto.SubmitQuoteDTO;
import com.basiscotton.tradingcore.dto.TradingResult;
import lombok.RequiredArgsConstructor;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.springframework.stereotype.Service;

import java.util.stream.Collectors;

@Service("basisQuote.submitBuyerBasisQuote.1")
@ApiRequestObject(value = "提交买方基差报价", name = "submitBuyerBasisQuote", groups = {"报价管理"}, params = {
    @ApiParamMeta(key = "reqVo", desc = "买方报价参数", type = BuyerSubmitQuoteReqVO.class)
})
@RequiredArgsConstructor
public class SubmitBuyerBasisQuote implements IBusinessService {

    /**
     * 报价服务
     */
    private final QuoteService quoteService;

    @Override
    public void doVerify(ServiceHandlerContext context) {
        BuyerSubmitQuoteReqVO reqVo = context.getValueObject(BuyerSubmitQuoteReqVO.class, "reqVo");
        if (reqVo == null) {
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "买方报价参数不能为空");
        }

        BeanValidationResult validationResult = ValidationUtil.warpValidate(reqVo);
        if (!validationResult.isSuccess()) {
            String errorMessage = validationResult.getErrorMessages()
                    .stream()
                    .map(BeanValidationResult.ErrorMessage::getMessage)
                    .collect(Collectors.joining(";"));
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, errorMessage);
        }
    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        BuyerSubmitQuoteReqVO reqVo = context.getValueObject(BuyerSubmitQuoteReqVO.class, "reqVo");

        SubmitQuoteDTO submitQuoteDTO = SubmitQuoteDTO.builder()
                .stockId(reqVo.getStockId())
                .buyerCustomerCode(context.getCurrentUserCustomCode())
                .buyerCustomerName(context.getCurrentUserCustomName())
                .quotePrice(reqVo.getQuotePrice())
                .tradeMode(TradeMode.BASIS_PRICING)
                .build();

        TradingResult tradingResult = quoteService.submitBuyerQuote(submitQuoteDTO);
        createSuccessResponse(context);
        context.getResponseDataSet().put("tradingResult", tradingResult);
    }
}
