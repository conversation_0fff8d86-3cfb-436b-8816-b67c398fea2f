package com.basiscotton.manager.quote.service;

import com.basiscotton.base.mappers.BasisQuoteMapper;
import com.basiscotton.manager.quote.vo.BuyerQuoteResVo;
import com.basiscotton.manager.quote.vo.SellerQuoteReqVo;
import com.basiscotton.manager.quote.vo.SellerQuoteResVo;
import org.harry.dandelion.framework.core.params.PageParameter;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.rep.support.page.PipPagination;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Title: GetSellerQuoteByPage.java
 * @Description: 卖方委托信息列表
 * <AUTHOR>
 * @date 2025/5/25
 * @version V1.0
 */
@Service("basisQuote.getSellerQuoteByPage.1")
@ApiRequestObject(value = "卖方委托信息列表-交易商", name = "getSellerQuoteByPage", groups = {"交易商端-基差交易-委托管理"}, params = {
        @ApiParamMeta(key = "pageParameter", desc = "分页参数", type = PageParameter.class),
        @ApiParamMeta(key = "reqVo", desc = "查询vo", type = SellerQuoteReqVo.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "pagination", desc = "查询分页列表", type = BuyerQuoteResVo.class,pagination = true),
})
public class GetSellerQuoteByPage implements IBusinessService {

    @Resource
    private BasisQuoteMapper basisQuoteMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        PageParameter pageParameter = context.getValueObject(PageParameter.class, "pageParameter");
        SellerQuoteReqVo reqVo = context.getValueObject(SellerQuoteReqVo.class, "reqVo");
        reqVo.setTraderCode(context.getCurrentUserCustomCode());
        PipPagination<SellerQuoteResVo> pipPagination = new PipPagination<>(pageParameter);
        pipPagination = basisQuoteMapper.getSellerQuoteByPage(pipPagination, reqVo);

        //创建响应实体
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("pagination", pipPagination);
    }

}
