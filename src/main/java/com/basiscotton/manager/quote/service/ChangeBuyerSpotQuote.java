package com.basiscotton.manager.quote.service;

import cn.hutool.extra.validation.BeanValidationResult;
import cn.hutool.extra.validation.ValidationUtil;
import com.basiscotton.base.enums.TradeMode;
import com.basiscotton.manager.quote.vo.ChangeBuyerQuoteReqVO;
import com.basiscotton.tradingcore.business.QuoteService;
import com.basiscotton.tradingcore.dto.BuyerChangeQuoteDTO;
import com.basiscotton.tradingcore.dto.TradingResult;
import lombok.RequiredArgsConstructor;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.springframework.stereotype.Service;

import java.util.stream.Collectors;

@Service("spot.changeBuyerSpotQuote.1")
@ApiRequestObject(value = "修改买方现货挂牌报价", name = "changeBuyerSpotQuote", groups = {"报价管理"}, params = {
    @ApiParamMeta(key = "reqVo", desc = "买方报价参数", type = ChangeBuyerQuoteReqVO.class)
})
@RequiredArgsConstructor
public class ChangeBuyerSpotQuote implements IBusinessService {

    /**
     * 报价服务
     */
    private final QuoteService quoteService;

    @Override
    public void doVerify(ServiceHandlerContext context) {
        ChangeBuyerQuoteReqVO reqVo = context.getValueObject(ChangeBuyerQuoteReqVO.class, "reqVo");
        if (reqVo == null) {
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "买方报价参数不能为空");
        }

        BeanValidationResult validationResult = ValidationUtil.warpValidate(reqVo);
        if (!validationResult.isSuccess()) {
            String errorMessage = validationResult.getErrorMessages()
                    .stream()
                    .map(BeanValidationResult.ErrorMessage::getMessage)
                    .collect(Collectors.joining(";"));
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, errorMessage);
        }
    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        ChangeBuyerQuoteReqVO reqVo = context.getValueObject(ChangeBuyerQuoteReqVO.class, "reqVo");

        BuyerChangeQuoteDTO buyerChangeQuoteDTO = BuyerChangeQuoteDTO.builder()
                .quoteId(reqVo.getQuoteId())
                .stockId(reqVo.getStockId())
                .buyerCustomerCode(context.getCurrentUserCustomCode())
                .buyerCustomerName(context.getCurrentUserCustomName())
                .quotePrice(reqVo.getQuotePrice())
                .tradeMode(TradeMode.SPOT_LISTING)
                .build();

        TradingResult tradingResult = quoteService.buyerChangeQuote(buyerChangeQuoteDTO);
        createSuccessResponse(context);
        context.getResponseDataSet().put("tradingResult", tradingResult);
    }
}
