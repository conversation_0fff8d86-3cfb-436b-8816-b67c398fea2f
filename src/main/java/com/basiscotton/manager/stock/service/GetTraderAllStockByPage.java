package com.basiscotton.manager.stock.service;

import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.mappers.BasisStockMapper;
import com.basiscotton.manager.stock.vo.TraderAllStockReqVo;
import com.basiscotton.manager.stock.vo.TraderAllStockResVo;
import org.harry.dandelion.framework.core.params.PageParameter;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.rep.support.page.PipPagination;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Title: GetTraderAllStockByPage.java
 * @Description: 全部资源列表-交易商
 * <AUTHOR>
 * @date 2025/5/24
 * @version V1.0
 */
@Service("basisStock.getTraderAllStockByPage.1")
@ApiRequestObject(value = "全部资源列表-交易商", name = "getTraderAllStockByPage", groups = {"交易商端-基差交易-资源管理"}, params = {
        @ApiParamMeta(key = "pageParameter", desc = "分页参数", type = PageParameter.class),
        @ApiParamMeta(key = "reqVo", desc = "查询vo", type = TraderAllStockReqVo.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "pagination", desc = "查询分页列表", type = TraderAllStockResVo.class,pagination = true),
})
public class GetTraderAllStockByPage implements IBusinessService {

    @Resource
    private BasisStockMapper basisStockMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        PageParameter pageParameter = context.getValueObject(PageParameter.class, "pageParameter");
        TraderAllStockReqVo reqVo = context.getValueObject(TraderAllStockReqVo.class, "reqVo");
        reqVo.setTraderCode(context.getCurrentUserCustomCode());
        PipPagination<TraderAllStockResVo> pipPagination = new PipPagination<>(pageParameter);
        pipPagination = basisStockMapper.getTraderAllStockByPage(pipPagination, reqVo);

        //创建响应实体
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("pagination", pipPagination);
    }

}
