package com.basiscotton.manager.stock.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.BasisStockEntity;
import com.basiscotton.base.enums.TradeMode;
import com.basiscotton.base.mappers.BasisStockMapper;
import com.basiscotton.common.apiservice.IBasisTradeSettlementService;
import com.basiscotton.common.apiservice.dto.HandleMarginDTO;
import com.sinosoft.dandelion.system.client.params.DataItem;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: DelNonWarehouseSpotStock.java
 * @Description: 删除非仓单资源商品-交易商-现货挂牌
 * @date 2025/9/8
 */
@Service("spotStock.delNonWarehouseSpotStock.1")
@ApiRequestObject(value = "删除非仓单现货商品-交易商-现货挂牌", name = "delNonWarehouseSpotStock", groups = {"交易商端-挂牌交易-资源管理"}, params = {
        @ApiParamMeta(key = "ids", desc = "商品ID", type = String.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "success", desc = "返回结果", type = boolean.class),
        @ApiParamMeta(key = "message", desc = "返回消息", type = String.class),
})
public class DelNonWarehouseSpotStock implements IBusinessService {

    @Resource
    private BasisStockMapper basisStockMapper;

    @Resource
    private IBasisTradeSettlementService basisTradeSettlementService;

    @Override
    public void doVerify(ServiceHandlerContext context) {
        //获取参数
        String ids = context.getValueObject(String.class, "ids");
        if (ids == null) {
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "商品ID不能为空");
        }
    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        String ids = context.getValueObject(String.class, "ids");
        List<String> idList = Arrays.asList(ids.split(","));
        LambdaQueryWrapper<BasisStockEntity> deleteWrapper = Wrappers.lambdaQuery(BasisStockEntity.class);
        deleteWrapper.in(BasisStockEntity::getId, idList);
        basisStockMapper.delete(deleteWrapper);
        //构造返回数据
        this.createSuccessResponse(context);
        context.getResponseBody().setData("success", true);
        context.getResponseBody().setData("message", "删除成功");
    }
}
