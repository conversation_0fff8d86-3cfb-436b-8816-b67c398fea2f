package com.basiscotton.manager.stock.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.MarketSettingEntity;
import com.basiscotton.base.entity.BasisStockEntity;
import com.basiscotton.base.mappers.BasisMarketSettingMapper;
import com.basiscotton.base.mappers.BasisStockMapper;
import com.basiscotton.manager.stock.vo.UpdBasisStockVo;
import com.sinosoft.dandelion.system.client.params.DataItem;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * @Title: UpdStockBasis.java
 * @Description: 修改商品信息
 * <AUTHOR>
 * @date 2025/6/3
 * @version V1.0
 */
@Service("basisStock.updStockBasis.1")
@ApiRequestObject(value = "修改商品信息-交易商", name = "updStockBasis", groups = {"交易商端-基差交易-资源管理"}, params = {
        @ApiParamMeta(key = "stockInfo", desc = "资源信息", type = UpdBasisStockVo.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "success", desc = "返回结果", type = boolean.class),
        @ApiParamMeta(key = "message", desc = "返回消息", type = String.class),
})
public class UpdStockBasis implements IBusinessService {

    @Resource
    private BasisStockMapper basisStockMapper;

    @Resource
    private BasisMarketSettingMapper basisMarketSettingMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        UpdBasisStockVo stockInfo = context.getValueObject(UpdBasisStockVo.class, "stockInfo");

        LambdaQueryWrapper<BasisStockEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(BasisStockEntity::getId, Arrays.asList(stockInfo.getIdStr().split(",")));
        List<BasisStockEntity> stockList = basisStockMapper.selectList(queryWrapper);

        if(stockList != null && stockList.size() > 0) {
            for (BasisStockEntity stock : stockList) {
                DataItem tradeStatus = stock.getTradeStatus();
                if(tradeStatus != null){
                    if(!BasisCottonConstants.trade_status_1.equals(tradeStatus.getCode()) && !BasisCottonConstants.trade_status_3.equals(tradeStatus.getCode())){
                        throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "商品存在报价信息，无法资源维护");
                    }
                }
                DataItem quoteType = stock.getQuoteType();
                if(quoteType != null && BasisCottonConstants.quote_type_1.equals(quoteType.getCode())){
                    this.updBasisStock(stockInfo, stock.getId());
                }else{
                    this.updFixedStock(stockInfo, stock.getId());
                }
            }
        }
        //构造返回数据
        this.createSuccessResponse(context);
        context.getResponseBody().setData("success", true);
        context.getResponseBody().setData("message", "修改成功");
    }

    private void updFixedStock(UpdBasisStockVo stockInfo,Long stockId){
        LambdaUpdateWrapper<BasisStockEntity> updateWrapper = new LambdaUpdateWrapper<BasisStockEntity>();
        updateWrapper.eq(BasisStockEntity::getId, stockId);
        updateWrapper.set(BasisStockEntity::getFutureCode, stockInfo.getFutureCode());
        updateWrapper.set(BasisStockEntity::getRemark, stockInfo.getRemark());
        basisStockMapper.update(null, updateWrapper);
    }

    private void updBasisStock(UpdBasisStockVo stockInfo,Long stockId){
        LambdaUpdateWrapper<BasisStockEntity> updateWrapper = new LambdaUpdateWrapper<BasisStockEntity>();
        updateWrapper.eq(BasisStockEntity::getId, stockId);
        updateWrapper.set(BasisStockEntity::getFutureCode, stockInfo.getFutureCode());
        updateWrapper.set(BasisStockEntity::getStockPrice, stockInfo.getBasisPrice());
        updateWrapper.set(BasisStockEntity::getPricingValidTime, stockInfo.getPricingValidTime());
        updateWrapper.set(BasisStockEntity::getInterestCost, stockInfo.getInterestCost());
        //没备注且自动审核已开启不需要审核
        if(StringUtil.isNotEmpty(stockInfo.getRemark()) && BasisCottonConstants.audit_status_1.equals(this.getAuditRemarkStatus())) {
            updateWrapper.set(BasisStockEntity::getResourceAuditStatus, new DataItem(BasisCottonConstants.resource_audit_status_1, ""));
            updateWrapper.set(BasisStockEntity::getTradeStatus, new DataItem(BasisCottonConstants.trade_status_1, ""));
        }else{
            updateWrapper.set(BasisStockEntity::getResourceAuditStatus, new DataItem(BasisCottonConstants.resource_audit_status_2, ""));
            updateWrapper.set(BasisStockEntity::getTradeStatus, new DataItem(BasisCottonConstants.trade_status_2, ""));
        }
        updateWrapper.set(BasisStockEntity::getRemark, stockInfo.getRemark());
        basisStockMapper.update(null, updateWrapper);
    }

    /**
     * 获取是否自动审核备注状态 1-否，2-是
     * @return
     */
    private Integer getAuditRemarkStatus(){
        LambdaQueryWrapper<MarketSettingEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MarketSettingEntity::getBasisSettingType, BasisCottonConstants.basis_setting_type_1);
        queryWrapper.eq(MarketSettingEntity::getSettingStatus, BasisCottonConstants.setting_status_1);
        MarketSettingEntity marketSettingEntity = basisMarketSettingMapper.selectOne(queryWrapper);
        if (marketSettingEntity != null) {
            return marketSettingEntity.getAuditRemarkStatus();
        } else {
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "当前不存在已启用的市场设置，不允许提交资源商品");
        }
    }
}
