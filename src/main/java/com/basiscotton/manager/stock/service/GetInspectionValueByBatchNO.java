package com.basiscotton.manager.stock.service;

import com.basiscotton.base.mappers.InspectionMapper;
import com.basiscotton.manager.stock.vo.InspectionValueVO;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: GetInspectionValueByBatchNO.java
 * @Description: 通过批号获取批次检验信息
 * @date 2025/9/4
 */
@Service("spotStock.getInspectionValueByBatchNo.1")
@ApiRequestObject(value = "获取检验信息", name = "getInspectionValueByBatchNo",  groups= {"交易商端-现货挂牌-非仓单资源"},params= {
        @ApiParamMeta(key = "batchNo", desc = "批号", type = String.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "inspectionValue", desc = "检验信息", type = InspectionValueVO.class),
})
public class GetInspectionValueByBatchNO implements IBusinessService {

    @Resource
    private InspectionMapper inspectionMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {
        String batchNo = context.getStringValue("batchNo");
        if (batchNo == null) {
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "参数不能为空");
        }
    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        String batchNo = context.getStringValue("batchNo");
        String currentCustomCode = context.getCurrentUserCustomCode();
        InspectionValueVO inspectionValue = inspectionMapper.getInspectionValueByBatchNO(batchNo, currentCustomCode);
        if (inspectionValue == null) {
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "未找到对应的批次信息");
        }
        //创建响应实体
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("inspectionValue",inspectionValue);
    }
}
