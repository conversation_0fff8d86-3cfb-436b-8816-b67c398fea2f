package com.basiscotton.manager.stock.service;

import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.mappers.BasisStockBaseInfoMapper;
import com.basiscotton.manager.stock.vo.BasisWhsReceiptParamVo;
import com.basiscotton.manager.stock.vo.BasisWhsReceiptVo;
import org.harry.dandelion.framework.core.params.PageParameter;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.harry.dandelion.framework.rep.support.page.PipPagination;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: GetBasisWhsPrceiptByPage.java
 * @Description: 交易商获取可用仓单资源
 * @date 2025/5/25 17:46
 */
@Service("basisStock.getTraderBasisWhsReceiptByPage.1")
@ApiRequestObject(value = "仓单资源列表-交易商-管理端", name = "getTraderBasisWhsReceiptByPage", groups = {"交易商端-基差交易-资源管理"}, params = {
        @ApiParamMeta(key = "pageParameter", desc = "分页参数", type = PageParameter.class),
        @ApiParamMeta(key = "reqVo", desc = "查询vo", type = BasisWhsReceiptParamVo.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "pagination", desc = "查询分页列表", type = BasisWhsReceiptVo.class,pagination = true),
})
public class GetTraderBasisWhsReceiptByPage  implements IBusinessService {

    @Resource
    private BasisStockBaseInfoMapper basisStockBaseInfoMapper;
    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        String currentUserCustomType = context.getCurrentUserCustomType();
        String currentCustomCode = context.getCurrentUserCustomCode();
        PageParameter pageParameter = context.getValueObject(PageParameter.class, "pageParameter");
        BasisWhsReceiptParamVo paramVo = context.getValueObject(BasisWhsReceiptParamVo.class, "reqVo");
        if(StringUtil.isNotEmpty(paramVo.getBatchNumber())){
            paramVo.setBatchNumberList(Arrays.asList(paramVo.getBatchNumber().split(",")));
        }
        if(StringUtil.isNotEmpty(paramVo.getWhsNoStr())){
            paramVo.setWhsNoList(Arrays.asList(paramVo.getWhsNoStr().split(",")));
        }
        if(currentUserCustomType.equals(BasisCottonConstants.custom_type_1)) {
            paramVo.setTraderCode(currentCustomCode);
        }
        PipPagination<BasisWhsReceiptVo> pipPagination = new PipPagination<BasisWhsReceiptVo>(pageParameter);
        pipPagination = basisStockBaseInfoMapper.selectBasisWarehouseReceiptByTraderCode(pipPagination, paramVo);

        //创建响应实体
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("pagination", pipPagination);

    }
}
