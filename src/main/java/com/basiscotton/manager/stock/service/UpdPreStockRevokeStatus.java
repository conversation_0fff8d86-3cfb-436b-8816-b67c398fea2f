package com.basiscotton.manager.stock.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.BasisPreStockEntity;
import com.basiscotton.base.mappers.BasisPreStockMapper;
import com.sinosoft.dandelion.system.client.params.DataItem;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * @Title: UpdPreStockRevokeStatus.java
 * @Description: 撤销商品
 * <AUTHOR>
 * @date 2025/5/28
 * @version V1.0
 */
@Service("basisStock.updPreStockRevokeStatus.1")
@ApiRequestObject(value = "撤销商品", name = "updStockRevokeStatus", groups = {"交易商端-基差交易-资源管理"}, params = {
        @ApiParamMeta(key = "id", desc = "商品ID", type = String.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "success", desc = "返回结果", type = boolean.class),
        @ApiParamMeta(key = "message", desc = "返回消息", type = String.class),
})
public class UpdPreStockRevokeStatus implements IBusinessService {

    @Resource
    private BasisPreStockMapper basisPreStockMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        String id = context.getValueObject(String.class, "id");
        List<String> idList = Arrays.asList(id.split(","));
        LambdaQueryWrapper<BasisPreStockEntity> queryWrapper = Wrappers.lambdaQuery(BasisPreStockEntity.class);
        queryWrapper.in(BasisPreStockEntity::getId, idList);
        List<BasisPreStockEntity> stockList = basisPreStockMapper.selectList(queryWrapper);
        if(stockList != null && stockList.size() > 0){
            for(BasisPreStockEntity stock : stockList){
                DataItem tradeStatus = stock.getTradeStatus();
                DataItem resourceAuditStatus = stock.getResourceAuditStatus();
                if(!BasisCottonConstants.resource_audit_status_3.equals(resourceAuditStatus.getCode())){
                    throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "商品未上架，无法下架商品");
                }
                if(tradeStatus != null){
                    if(BasisCottonConstants.trade_status_1.equals(tradeStatus.getCode()) || BasisCottonConstants.trade_status_3.equals(tradeStatus.getCode())){
                        LambdaUpdateWrapper<BasisPreStockEntity> updateWrapper = new LambdaUpdateWrapper<BasisPreStockEntity>();
                        updateWrapper.eq(BasisPreStockEntity::getId, id);
                        updateWrapper.set(BasisPreStockEntity::getResourceAuditStatus, BasisCottonConstants.resource_audit_status_4);
                        updateWrapper.set(BasisPreStockEntity::getTradeStatus, BasisCottonConstants.trade_status_3);
                        basisPreStockMapper.update(null, updateWrapper);
                    }else{
                        throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "商品存在报价信息，无法下架商品");
                    }
                }
            }
        }
        //构造返回数据
        this.createSuccessResponse(context);
        context.getResponseBody().setData("success", true);
        context.getResponseBody().setData("message", "下架成功");
    }

}
