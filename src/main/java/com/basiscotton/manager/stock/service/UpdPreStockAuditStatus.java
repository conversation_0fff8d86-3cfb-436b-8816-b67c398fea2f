package com.basiscotton.manager.stock.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.BasisPreStockEntity;
import com.basiscotton.base.mappers.BasisPreStockMapper;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * @Title: updPreStockAuditStatus.java
 * @Description: 审核商品
 * <AUTHOR>
 * @date 2025/5/28
 * @version V1.0
 */
@Service("basisStock.updPreStockAuditStatus.1")
@ApiRequestObject(value = "审核商品", name = "updPreStockAuditStatus", groups = {"交易商端-基差交易-资源管理"}, params = {
        @ApiParamMeta(key = "id", desc = "商品ID", type = String.class),
        @ApiParamMeta(key = "auditStatus", desc = "审核状态", type = String.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "success", desc = "返回结果", type = boolean.class),
        @ApiParamMeta(key = "message", desc = "返回消息", type = String.class),
})
public class UpdPreStockAuditStatus implements IBusinessService {

    @Resource
    private BasisPreStockMapper basisPreStockMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        String id = context.getValueObject(String.class, "id");
        String auditStatus = context.getValueObject(String.class, "auditStatus");
        List<String> idList = Arrays.asList(id.split(","));
        LambdaQueryWrapper<BasisPreStockEntity> queryWrapper = Wrappers.lambdaQuery(BasisPreStockEntity.class);
        queryWrapper.in(BasisPreStockEntity::getId, idList);
        queryWrapper.eq(BasisPreStockEntity::getResourceAuditStatus, BasisCottonConstants.resource_audit_status_2);
        List<BasisPreStockEntity> stockList = basisPreStockMapper.selectList(queryWrapper);

        if(stockList != null && stockList.size() > 0) {
            for (BasisPreStockEntity entity : stockList) {
                if(auditStatus != null && BasisCottonConstants.AUDIT_STATUS_1.equals(auditStatus)){
                    LambdaUpdateWrapper<BasisPreStockEntity> updateWrapper = new LambdaUpdateWrapper<BasisPreStockEntity>();
                    updateWrapper.eq(BasisPreStockEntity::getId, entity.getId());
                    updateWrapper.set(BasisPreStockEntity::getResourceAuditStatus, BasisCottonConstants.resource_audit_status_3);
                    basisPreStockMapper.update(null, updateWrapper);
                }
                if(auditStatus != null && BasisCottonConstants.AUDIT_STATUS_2.equals(auditStatus)){
                    LambdaUpdateWrapper<BasisPreStockEntity> updateWrapper = new LambdaUpdateWrapper<BasisPreStockEntity>();
                    updateWrapper.eq(BasisPreStockEntity::getId, entity.getId());
                    updateWrapper.set(BasisPreStockEntity::getResourceAuditStatus, BasisCottonConstants.resource_audit_status_4);
                    basisPreStockMapper.update(null, updateWrapper);
                }
            }
        }
        //构造返回数据
        this.createSuccessResponse(context);
        context.getResponseBody().setData("success", true);
        context.getResponseBody().setData("message", "审核成功");
    }

}
