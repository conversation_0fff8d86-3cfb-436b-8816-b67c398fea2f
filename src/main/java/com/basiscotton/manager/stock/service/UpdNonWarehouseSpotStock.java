package com.basiscotton.manager.stock.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.BasisStockEntity;
import com.basiscotton.base.enums.TradeMode;
import com.basiscotton.base.mappers.BasisStockMapper;
import com.basiscotton.common.apiservice.IBasisTradeSettlementService;
import com.basiscotton.common.apiservice.dto.HandleMarginDTO;
import com.basiscotton.tradingcore.business.StockService;
import com.basiscotton.tradingcore.cache.bo.StockLocker;
import com.basiscotton.tradingcore.dto.ActiveStockDTO;
import com.github.benmanes.caffeine.cache.Cache;
import com.sinosoft.dandelion.system.client.params.DataItem;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: UpdNonWarehouseSpotStock.java
 * @Description: 上架非仓单资源商品-交易商-现货挂牌
 * @date 2025/9/1
 */
@Service("spotStock.updNonWarehouseSpotStock.1")
@ApiRequestObject(value = "上架非仓单现货商品-交易商-现货挂牌", name = "updNonWarehouseSpotStock", groups = {"交易商端-挂牌交易-资源管理"}, params = {
        @ApiParamMeta(key = "ids", desc = "商品ID", type = String.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "success", desc = "返回结果", type = boolean.class),
        @ApiParamMeta(key = "message", desc = "返回消息", type = String.class),
})
public class UpdNonWarehouseSpotStock implements IBusinessService {

    @Resource
    private BasisStockMapper basisStockMapper;

    @Resource
    private IBasisTradeSettlementService basisTradeSettlementService;

    @Resource
    Cache<String, StockLocker> basisStockCache;

    @Override
    public void doVerify(ServiceHandlerContext context) {
        //获取参数
        String ids = context.getValueObject(String.class, "ids");
        if (ids == null) {
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "商品ID不能为空");
        }
    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        String ids = context.getValueObject(String.class, "ids");
        List<String> idList = Arrays.asList(ids.split(","));
        LambdaQueryWrapper<BasisStockEntity> queryWrapper = Wrappers.lambdaQuery(BasisStockEntity.class);
        queryWrapper.in(BasisStockEntity::getId, idList);
        List<BasisStockEntity> stockList = basisStockMapper.selectList(queryWrapper);
        if(stockList != null && !stockList.isEmpty()){
            for(BasisStockEntity basisStockEntity : stockList){
                StockLocker stockLocker1 = new StockLocker(String.valueOf(basisStockEntity.getId()));
                basisStockCache.put(String.valueOf(basisStockEntity.getId()), stockLocker1);
                //卖方保证金标准
                BigDecimal sellerBasisMarginStandard = basisStockEntity.getSellerBasisMarginStandard();
                //交易手续费标准
                BigDecimal sellerTradeFeeStandard = basisStockEntity.getSellerTradeFeeStandard();
                //商品重量
                BigDecimal stockWeight = basisStockEntity.getStockWeight();
                //需冻结的保证金金额
                BigDecimal sellerFreezeMarginAmount = sellerBasisMarginStandard.multiply(stockWeight).setScale(2, BigDecimal.ROUND_HALF_UP);
                //需冻结的交易手续费金额
                BigDecimal sellerFreezeTradeFeeAmount = sellerTradeFeeStandard.multiply(stockWeight).setScale(2, BigDecimal.ROUND_HALF_UP);
                //冻结保证金和交易手续费
                basisStockEntity.setSellerFreezeMarginAmount(sellerFreezeMarginAmount);
                basisStockEntity.setSellerFreezeTradeFeeAmount(sellerFreezeTradeFeeAmount);
                basisStockEntity.setSellerFreezeDeliveryFeeAmount(BigDecimal.ZERO);
                HandleMarginDTO tradFeeMarginDTO = HandleMarginDTO.quoteToHandleMarginDTOFromStockTradFee(TradeMode.SPOT_LISTING).apply(basisStockEntity);
                HandleMarginDTO marginDTO = HandleMarginDTO.quoteToHandleMarginDTOFromStock(TradeMode.SPOT_LISTING).apply(basisStockEntity);
                basisTradeSettlementService.freezeMargin(Arrays.asList(tradFeeMarginDTO, marginDTO));
                basisStockEntity.setResourceAuditStatus(new DataItem(BasisCottonConstants.resource_audit_status_2, null));
                basisStockEntity.setTradeStatus(new DataItem(BasisCottonConstants.trade_status_2, null));
                basisStockMapper.updateById(basisStockEntity);
            }
        }else {
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "未查询到商品信息");
        }
        //构造返回数据
        this.createSuccessResponse(context);
        context.getResponseBody().setData("success", true);
        context.getResponseBody().setData("message", "上架成功");
    }
}
