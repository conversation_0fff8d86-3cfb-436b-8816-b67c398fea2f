package com.basiscotton.manager.stock.service;

import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.mappers.BasisStockMapper;
import com.basiscotton.manager.stock.vo.TraderAllSpotStockResVo;
import com.basiscotton.manager.stock.vo.TraderAllStockReqVo;
import org.harry.dandelion.framework.core.params.PageParameter;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.rep.support.page.PipPagination;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Title: GetTraderAllFCDSpotStockByPage.java
 * @Description: 货挂牌非仓单已添加商品列表-交易商（未提交的商品）
 * <AUTHOR>
 * @date 2025/9/2
 * @version V1.0
 */
@Service("spotStock.getTraderAllFCDSpotStockByPage.1")
@ApiRequestObject(value = "现货挂牌非仓单已添加商品列表-交易商", name = "getTraderAllSpotStockByPage", groups = {"交易商端-现货挂牌-资源管理"}, params = {
        @ApiParamMeta(key = "pageParameter", desc = "分页参数", type = PageParameter.class),
        @ApiParamMeta(key = "reqVo", desc = "查询vo", type = TraderAllStockReqVo.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "pagination", desc = "查询分页列表", type = TraderAllSpotStockResVo.class,pagination = true),
})
public class GetTraderAllFCDSpotStockByPage implements IBusinessService {

    @Resource
    private BasisStockMapper basisStockMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        PageParameter pageParameter = context.getValueObject(PageParameter.class, "pageParameter");
        TraderAllStockReqVo reqVo = context.getValueObject(TraderAllStockReqVo.class, "reqVo");
        reqVo.setCreateUserName(context.getCurrentUserRealName());
        reqVo.setStockSource(BasisCottonConstants.stock_source_2);
        reqVo.setResourceAuditStatus(BasisCottonConstants.resource_audit_status_4);
        PipPagination<TraderAllSpotStockResVo> pipPagination = new PipPagination<>(pageParameter);
        pipPagination = basisStockMapper.getTraderAllSpotStockByPage(pipPagination, reqVo);

        //创建响应实体
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("pagination", pipPagination);
    }

}
