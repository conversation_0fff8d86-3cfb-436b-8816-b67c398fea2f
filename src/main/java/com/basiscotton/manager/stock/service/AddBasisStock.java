package com.basiscotton.manager.stock.service;

import com.applet.trade.base.mappers.CommonMappers;
import com.applet.trade.basisStock.vo.CustomInfoVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.BasisSequenceContants;
import com.basiscotton.base.entity.MarketSettingEntity;
import com.basiscotton.base.entity.BasisStockBaseInfoEntity;
import com.basiscotton.base.entity.BasisStockEntity;
import com.basiscotton.base.mappers.BasisMarketSettingMapper;
import com.basiscotton.base.mappers.BasisStockBaseInfoMapper;
import com.basiscotton.base.mappers.BasisStockMapper;
import com.basiscotton.manager.stock.vo.BasisBatchInfoVo;
import com.basiscotton.manager.stock.vo.BasisStockVo;
import com.sinosoft.dandelion.system.client.params.DataItem;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.harry.dandelion.framework.id.core.service.BizIdGenerator;
import org.harry.dandelion.framework.sequence.ISequenceFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: AddBasisStock.java
 * @Description: 添加仓单资源商品
 * @date 2025/5/25 19:15
 */
@Service("basisStock.addBasisStock.1")
@ApiRequestObject(value = "添加仓单资源商品-交易商", name = "addBasisStock", groups = {"交易商端-基差交易-资源管理"}, params = {
        @ApiParamMeta(key = "warehouseReceiptNos", desc = "仓单号", type = String.class),
        @ApiParamMeta(key = "basisStockVo", desc = "仓单资源提交信息", type = BasisStockVo.class)
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "success", desc = "返回结果", type = boolean.class),
        @ApiParamMeta(key = "message", desc = "返回消息", type = String.class),
})
public class AddBasisStock implements IBusinessService {



    @Resource
    private CommonMappers commonMappers;

    @Resource
    private BasisStockMapper basisStockMapper;

    @Resource
    private BasisStockBaseInfoMapper basisStockBaseInfoMapper;

    @Resource
    private BasisMarketSettingMapper basisMarketSettingMapper;

    @Resource
    private ISequenceFactory sequenceFactory;
    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        String currentCustomCode = context.getCurrentUserCustomCode();
        String currentRealName = context.getCurrentUserRealName();
        String warehouseReceiptNos = context.getValueObject(String.class, "warehouseReceiptNos");
        List<String> warehouseReceiptNoList = Arrays.asList(warehouseReceiptNos.split(","));
        BasisStockVo basisStockVo = context.getValueObject(BasisStockVo.class, "basisStockVo");

        Map<String, BasisBatchInfoVo> batchInfoVoMap = getWhsBatchInfo(warehouseReceiptNoList);
        //Map<String, InspectionValueVO> inspectionValueMap = getInspectionData(batchNoList);
        List<String> customerCodeList = new ArrayList<>();
        customerCodeList.add(currentCustomCode);
        Map<String, CustomInfoVO> customInfoMap = getCustomInfo(customerCodeList);

        for (String warehouseReceiptNo : warehouseReceiptNoList) {
            long basisStockId = BizIdGenerator.getInstance().generateBizId();
            long basisStockBaseInfoId = BizIdGenerator.getInstance().generateBizId();
            String stockCode = sequenceFactory.create(BasisSequenceContants.SEQ_GPSPM).next();

            BasisStockEntity basisStockEntity = new BasisStockEntity();
            basisStockEntity.setId(basisStockId);
            basisStockEntity.setStockBaseInfoId(basisStockBaseInfoId);
            basisStockEntity.setStockCode(stockCode);
            basisStockEntity.setWarehouseReceiptNo(warehouseReceiptNo);
            basisStockEntity.setBatchNo(batchInfoVoMap.get(warehouseReceiptNo).getBatchNo());
            basisStockEntity.setResourceTradeType(new DataItem(BasisCottonConstants.resource_trade_type_1,""));
            basisStockEntity.setResourceDisplayType(new DataItem(BasisCottonConstants.resource_display_type_1,""));
            //是- 全都自动审核，否-有备注的需手动审核
            if(StringUtil.isNotEmpty(basisStockVo.getRemark()) && BasisCottonConstants.audit_status_1.equals(this.getAuditRemarkStatus())) {
                basisStockEntity.setResourceAuditStatus(new DataItem(BasisCottonConstants.resource_audit_status_1, ""));
                basisStockEntity.setTradeStatus(new DataItem(BasisCottonConstants.trade_status_1,""));
            }else{
                basisStockEntity.setResourceAuditStatus(new DataItem(BasisCottonConstants.resource_audit_status_2, ""));
                basisStockEntity.setTradeStatus(new DataItem(BasisCottonConstants.trade_status_2,""));
            }
            basisStockEntity.setQuoteType(new DataItem(BasisCottonConstants.quote_type_1,""));
            basisStockEntity.setBasisPricingType(new DataItem(basisStockVo.getPricingParty(),""));
            basisStockEntity.setFutureCode(basisStockVo.getFutureCode());
            basisStockEntity.setStockPrice(basisStockVo.getBasisPrice());
            basisStockEntity.setStockSource(new DataItem(BasisCottonConstants.stock_source_1,""));
            basisStockEntity.setTradeStatus(new DataItem(BasisCottonConstants.trade_status_1,""));
            basisStockEntity.setTraderCode(currentCustomCode);
            basisStockEntity.setTraderName(customInfoMap.get(currentCustomCode).getCustomName());
            basisStockEntity.setSellerBasisMarginType(new DataItem(BasisCottonConstants.seller_basis_margin_type_1,""));
            basisStockEntity.setSellerBasisMarginStandard(new BigDecimal(0));
            basisStockEntity.setSellerTradeFeeType(new DataItem(BasisCottonConstants.basis_seller_free_type_1,""));
            basisStockEntity.setSellerDeliveryFeeType(new DataItem(BasisCottonConstants.basis_seller_free_type_1,""));
            basisStockEntity.setSellerTradeFeeStandard(new BigDecimal(0));
            basisStockEntity.setSellerDeliveryFeeStandard(new BigDecimal(0));
            basisStockEntity.setRemark(basisStockVo.getRemark());
            basisStockEntity.setPricingValidTime(basisStockVo.getPricingValidTime());
            basisStockEntity.setInterestCost(basisStockVo.getInterestCost());
            basisStockEntity.setTradeMode(new DataItem(BasisCottonConstants.trade_mode_2,""));
            basisStockEntity.setCreateTime(new Date());
            basisStockEntity.setCreateUserName(currentRealName);
            basisStockMapper.insert(basisStockEntity);

            BasisStockBaseInfoEntity basisStockBaseInfoEntity = new BasisStockBaseInfoEntity();
            basisStockBaseInfoEntity.setId(basisStockBaseInfoId);
            basisStockBaseInfoEntity.setStockCode(stockCode);
            basisStockBaseInfoEntity.setWarehouseReceiptNo(warehouseReceiptNo);
            basisStockBaseInfoEntity.setBatchNo(batchInfoVoMap.get(warehouseReceiptNo).getBatchNo());
            basisStockBaseInfoEntity.setQuantity(batchInfoVoMap.get(warehouseReceiptNo).getQuantity());
            basisStockBaseInfoEntity.setMarksWeight(batchInfoVoMap.get(warehouseReceiptNo).getMarkWeight());
            basisStockBaseInfoEntity.setConditionedWeight(batchInfoVoMap.get(warehouseReceiptNo).getConditionedWeight());
            basisStockBaseInfoEntity.setCrossWeight(batchInfoVoMap.get(warehouseReceiptNo).getGrossWeight());
            basisStockBaseInfoEntity.setProductYear(batchInfoVoMap.get(warehouseReceiptNo).getProductYear());
            basisStockBaseInfoEntity.setFactoryCode(batchInfoVoMap.get(warehouseReceiptNo).getProcessCode());
            basisStockBaseInfoEntity.setFactoryName(batchInfoVoMap.get(warehouseReceiptNo).getProcessCompany());
            basisStockBaseInfoEntity.setWhsPickMode(new DataItem(batchInfoVoMap.get(warehouseReceiptNo).getWhsPickMode(),""));
            basisStockBaseInfoEntity.setMarkDesc(batchInfoVoMap.get(warehouseReceiptNo).getMarkDesc());
            basisStockBaseInfoEntity.setColorGrade(batchInfoVoMap.get(warehouseReceiptNo).getColorGradeCode());

            basisStockBaseInfoEntity.setWhiteCottonL1(batchInfoVoMap.get(warehouseReceiptNo).getWhiteCottonL1());
            basisStockBaseInfoEntity.setWhiteCottonL2(batchInfoVoMap.get(warehouseReceiptNo).getWhiteCottonL2());
            basisStockBaseInfoEntity.setWhiteCottonL3(batchInfoVoMap.get(warehouseReceiptNo).getWhiteCottonL3());
            basisStockBaseInfoEntity.setWhiteCottonL4(batchInfoVoMap.get(warehouseReceiptNo).getWhiteCottonL4());
            basisStockBaseInfoEntity.setWhiteCottonL5(batchInfoVoMap.get(warehouseReceiptNo).getWhiteCottonL5());
            basisStockBaseInfoEntity.setWhiteCottonL6(batchInfoVoMap.get(warehouseReceiptNo).getWhiteCottonL6());
            basisStockBaseInfoEntity.setSpotCottonL1(batchInfoVoMap.get(warehouseReceiptNo).getSpotCottonL1());
            basisStockBaseInfoEntity.setSpotCottonL2(batchInfoVoMap.get(warehouseReceiptNo).getSpotCottonL2());
            basisStockBaseInfoEntity.setSpotCottonL3(batchInfoVoMap.get(warehouseReceiptNo).getSpotCottonL3());
            basisStockBaseInfoEntity.setSpotCottonL4(batchInfoVoMap.get(warehouseReceiptNo).getSpotCottonL4());
            basisStockBaseInfoEntity.setYellowIshCottonL1(batchInfoVoMap.get(warehouseReceiptNo).getYellowIshCottonL1());
            basisStockBaseInfoEntity.setYellowIshCottonL2(batchInfoVoMap.get(warehouseReceiptNo).getYellowIshCottonL2());
            basisStockBaseInfoEntity.setYellowIshCottonL3(batchInfoVoMap.get(warehouseReceiptNo).getYellowIshCottonL3());
            basisStockBaseInfoEntity.setYellowCottonL1(batchInfoVoMap.get(warehouseReceiptNo).getYellowCottonL1());
            basisStockBaseInfoEntity.setYellowCottonL2(batchInfoVoMap.get(warehouseReceiptNo).getYellowCottonL2());

            basisStockBaseInfoEntity.setMainMkl(batchInfoVoMap.get(warehouseReceiptNo).getMainMkl());
            basisStockBaseInfoEntity.setAvgMkl(batchInfoVoMap.get(warehouseReceiptNo).getAvgMkl());
            basisStockBaseInfoEntity.setMaxMkl(batchInfoVoMap.get(warehouseReceiptNo).getMaxMkl());
            basisStockBaseInfoEntity.setMinMkl(batchInfoVoMap.get(warehouseReceiptNo).getMinMkl());
            basisStockBaseInfoEntity.setAvgBreakRate(batchInfoVoMap.get(warehouseReceiptNo).getAvgBreakRate());

            basisStockBaseInfoEntity.setMainLength(batchInfoVoMap.get(warehouseReceiptNo).getMainLength());
            basisStockBaseInfoEntity.setAvgLength(batchInfoVoMap.get(warehouseReceiptNo).getAvgLength());

            basisStockBaseInfoEntity.setRollingQuality(batchInfoVoMap.get(warehouseReceiptNo).getRollingQualityAvg());
            basisStockBaseInfoEntity.setRollingQualityAvg(batchInfoVoMap.get(warehouseReceiptNo).getRollingQualityAvg());
            //basisStockBaseInfoEntity.setRollingQualityLevel(inspectionValueMap.get(warehouseReceiptNo).getRollingQualityAvg());
            basisStockBaseInfoEntity.setUniformityAverageValue(batchInfoVoMap.get(warehouseReceiptNo).getAvgLenUniformity());

            basisStockBaseInfoEntity.setMoistureRate(batchInfoVoMap.get(warehouseReceiptNo).getMoistureRate());
            basisStockBaseInfoEntity.setImpurityRate(batchInfoVoMap.get(warehouseReceiptNo).getImpurityRate());
            basisStockBaseInfoEntity.setBreakValue(batchInfoVoMap.get(warehouseReceiptNo).getAvgBreakRate());

            basisStockBaseInfoEntity.setStockName(batchInfoVoMap.get(warehouseReceiptNo).getStockName());

            //产地
            basisStockBaseInfoEntity.setIntactPlace(batchInfoVoMap.get(warehouseReceiptNo).getPlaceName());
            basisStockBaseInfoEntity.setInspectDate(batchInfoVoMap.get(warehouseReceiptNo).getInspectDate());

            basisStockBaseInfoEntity.setTraderCode(currentCustomCode);
            basisStockBaseInfoEntity.setTraderName(customInfoMap.get(currentCustomCode).getCustomName());
            basisStockBaseInfoEntity.setStorageWhsCode(batchInfoVoMap.get(warehouseReceiptNo).getWhsCode());
            basisStockBaseInfoEntity.setStorageWhsName(batchInfoVoMap.get(warehouseReceiptNo).getWhsName());
            basisStockBaseInfoEntity.setWhsSupervisionStatus(batchInfoVoMap.get(warehouseReceiptNo).getWhsSupervisionStatus());
            basisStockBaseInfoEntity.setSuperviseCode(batchInfoVoMap.get(warehouseReceiptNo).getLoanCompanyCode());
            basisStockBaseInfoEntity.setSuperviseName(batchInfoVoMap.get(warehouseReceiptNo).getLoanCompanyName());
            basisStockBaseInfoEntity.setWhsPledgeChannel(batchInfoVoMap.get(warehouseReceiptNo).getWhsPledgeChannel());
            basisStockBaseInfoMapper.insert(basisStockBaseInfoEntity);


        }

        //构造返回数据
        this.createSuccessResponse(context);
        context.getResponseBody().setData("success", true);
        context.getResponseBody().setData("message", "添加成功");
    }

    private Map<String, BasisBatchInfoVo> getWhsBatchInfo(List<String> warehouseReceiptNumberList){
        List<BasisBatchInfoVo> BatchInfoVoList = basisStockBaseInfoMapper.getBatchInfoVo(warehouseReceiptNumberList);
        BatchInfoVoList.addAll(BatchInfoVoList);
        Map<String, BasisBatchInfoVo> batchInfoVoMap = BatchInfoVoList.stream()
                .collect(Collectors.toMap(
                        BasisBatchInfoVo::getWarehouseReceiptNumber,
                        batchInfoVo -> batchInfoVo,
                        (existing, replacement) -> existing // 保留第一个出现的对象
                ));
        return batchInfoVoMap;
    }

//    private Map<String, InspectionValueVO> getInspectionData(List<String> batchNoList) {
//        List<InspectionValueVO> inspectionValueList = adjustTargetDataMapper.getInspectionValueByBatchNO(batchNoList);
//        Map<String, InspectionValueVO> inspectionValueMap = new HashMap<String, InspectionValueVO>();
//        for (InspectionValueVO inspectionValue : inspectionValueList) {
//            inspectionValueMap.put(inspectionValue.getWarehouseReceiptNo(), inspectionValue);
//        }
//        return inspectionValueMap;
//    }

    private Map<String, CustomInfoVO> getCustomInfo( List<String> customerCodeList){
        //3、查询客户信息
        CustomInfoVO customInfoParm = new CustomInfoVO();
        customInfoParm.setCustomCodes(customerCodeList);
        List<CustomInfoVO> customInfoList = commonMappers.getCustomInfo(customInfoParm);
        //将customInfoList转化成map,以customCode为key,CustomInfoVO为值
        Map<String, CustomInfoVO> customInfoMap = new HashMap<String, CustomInfoVO>();
        for (CustomInfoVO customInfo : customInfoList) {
            customInfoMap.put(customInfo.getCustomCode(), customInfo);
        }
        return customInfoMap;
    }

    /**
     * 获取是否自动审核备注状态 1-否，2-是
     * @return
     */
    private Integer getAuditRemarkStatus(){
        LambdaQueryWrapper<MarketSettingEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MarketSettingEntity::getBasisSettingType, BasisCottonConstants.basis_setting_type_1);
        queryWrapper.eq(MarketSettingEntity::getSettingStatus, BasisCottonConstants.setting_status_1);
        MarketSettingEntity marketSettingEntity = basisMarketSettingMapper.selectOne(queryWrapper);
        if (marketSettingEntity != null) {
            return marketSettingEntity.getAuditRemarkStatus();
        } else {
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "当前不存在已启用的买方点价类型市场设置，不允许提交资源商品");
        }
    }
}
