package com.basiscotton.manager.stock.service;

import com.alibaba.excel.EasyExcel;
import com.basiscotton.base.mappers.BasisStockBaseInfoMapper;
import com.basiscotton.manager.stock.vo.BatchImportWhsReceiptStockVo;
import com.basiscotton.manager.stock.vo.SpotStockVo;
import com.cottoneasy.finance.api.ApiTradeService;
import com.cottoneasy.finance.api.vo.trade.LockingResultVo;
import com.sinosoft.dandelion.system.client.params.DataItem;
import lombok.SneakyThrows;
import org.harry.dandelion.framework.common.utils.DateUtils;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.id.core.service.BizIdGenerator;
import org.harry.dandelion.framework.sequence.ISequence;
import org.harry.dandelion.framework.sequence.ISequenceFactory;
import org.harry.dandelion.framework.web.common.UploadFile;
import org.harry.dandelion.framework.web.message.file.UploadFileMessageBody;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 *
 * @Title: BatchImportWhsReceiptStock.java
 * @Description: 批量导入仓单商品
 * <AUTHOR>
 * @date 2025年9月9日
 * @version V1.0
 */
@Service("spotStock.batchImportWhsReceiptStock.1")
@ApiRequestObject(value = "批量导入仓单商品",name = "batchImportWhsReceiptStock",groups = {"挂牌交易-资源管理"},params = {
        @ApiParamMeta(key = "file", desc = "文件", type = MultipartFile.class)
})
@ApiResponseObject(params = {
		@ApiParamMeta(key = "success",desc = "返回结果",type = boolean.class),
		@ApiParamMeta(key = "consultTitle",desc = "返回消息",type = String.class),
		@ApiParamMeta(key = "linitStatusTitle",desc = "返回消息",type = String.class),
		@ApiParamMeta(key = "WhsNoIsNull",desc = "返回消息",type = String.class),
		@ApiParamMeta(key = "NoWhsStr",desc = "返回消息",type = String.class),
})
public class BatchImportWhsReceiptStock implements IBusinessService {

    @Resource
    private ApiTradeService apiTradeService;

    @Resource
    private BasisStockBaseInfoMapper basisStockBaseInfoMapper;
	@Override
	public void doVerify(ServiceHandlerContext context) {

	}

	@SneakyThrows
	@Override
	public void doWork(ServiceHandlerContext context) {
        //接受文件信息
		UploadFileMessageBody body = (UploadFileMessageBody) context.getRequestBody();
		List<UploadFile> files = (List<UploadFile>) body.getDataSet().get("file");
		UploadFile file = files.get(0);
		Object objList = EasyExcel.read(file.getInputStream(), BatchImportWhsReceiptStockVo.class, null).sheet(0).doReadSync();
		List<BatchImportWhsReceiptStockVo> stockList = (List<BatchImportWhsReceiptStockVo>) objList;
        // 提取仓单号列表
        List<String> warehouseReceiptNoList = stockList.stream()
                .map(BatchImportWhsReceiptStockVo::getWarehouseReceiptNo)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        //校验是否参与金融交易
        LockingResultVo lockingResultVo = apiTradeService.checkLockingStatus(String.join(",", warehouseReceiptNoList));
        List<String> financeWhsNoList = lockingResultVo.getWarehouseList();
        if(financeWhsNoList.size()>0) {
            log.debug("仓单号[{}]，已参与金融中心交易。",warehouseReceiptNoList.toString());
            throw new BusinessException(Constants.serviceErrorCode, warehouseReceiptNoList.toString()+"已参与金融中心交易，不能发起挂牌交易。");
        }
		//校验仓单是否可提交
        List<String> validWarehouseReceipts = basisStockBaseInfoMapper
                .getValidWhsReceiptNoList(warehouseReceiptNoList);
        // 状态不允许提交的仓单号
        List<String> nonExistentReceipts = warehouseReceiptNoList.stream()
                .filter(no -> !validWarehouseReceipts.contains(no))
                .collect(Collectors.toList());
        if (!nonExistentReceipts.isEmpty()) {
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, nonExistentReceipts.toString() + "状态不允许提交");
        }
		this.createSuccessResponse(context);
		context.getResponseBody().setData("success", true);
		context.getResponseBody().setData("dataNum", "成功导入" + warehouseReceiptNoList.size() + "条数据");
	}
}