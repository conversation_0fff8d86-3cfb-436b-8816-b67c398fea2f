package com.basiscotton.manager.stock.service;

import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.mappers.BasisStockMapper;
import com.basiscotton.manager.stock.vo.TraderAllSpotStockResVo;
import com.basiscotton.manager.stock.vo.TraderAllStockReqVo;
import org.harry.dandelion.framework.core.params.PageParameter;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.rep.support.page.PipPagination;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Title: GetTraderAllSpotStockByPage.java
 * @Description: 全部现货挂牌仓单商品列表-交易商
 * <AUTHOR>
 * @date 2025/9/2
 * @version V1.0
 */
@Service("spotStock.getTraderAllSpotStockByPage.1")
@ApiRequestObject(value = "全部现货挂牌仓单商品列表-交易商", name = "getTraderAllSpotStockByPage", groups = {"交易商端-现货挂牌-资源管理"}, params = {
        @ApiParamMeta(key = "pageParameter", desc = "分页参数", type = PageParameter.class),
        @ApiParamMeta(key = "reqVo", desc = "查询vo", type = TraderAllStockReqVo.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "pagination", desc = "查询分页列表", type = TraderAllSpotStockResVo.class,pagination = true),
})
public class GetTraderAllSpotStockByPage implements IBusinessService {

    @Resource
    private BasisStockMapper basisStockMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        String currentUserCustomType = context.getCurrentUserCustomType();
        PageParameter pageParameter = context.getValueObject(PageParameter.class, "pageParameter");
        TraderAllStockReqVo reqVo = context.getValueObject(TraderAllStockReqVo.class, "reqVo");
        if(currentUserCustomType.equals(BasisCottonConstants.custom_type_1)) {
            reqVo.setTraderCode(context.getCurrentUserCustomCode());
        }
        List<String> tradeStatusList = new ArrayList<String>();
        tradeStatusList.add(BasisCottonConstants.trade_status_1);
        tradeStatusList.add(BasisCottonConstants.trade_status_2);
        reqVo.setTradeStatus(tradeStatusList);
        PipPagination<TraderAllSpotStockResVo> pipPagination = new PipPagination<>(pageParameter);
        pipPagination = basisStockMapper.getTraderAllSpotStockByPage(pipPagination, reqVo);

        //创建响应实体
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("pagination", pipPagination);
    }

}
