package com.basiscotton.manager.stock.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.BasisStockEntity;
import com.basiscotton.base.mappers.BasisStockMapper;
import com.basiscotton.tradingcore.business.StockService;
import com.basiscotton.tradingcore.dto.ActiveStockDTO;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * @Title: updStockAuditStatus.java
 * @Description: 审核商品
 * <AUTHOR>
 * @date 2025/5/28
 * @version V1.0
 */
@Service("basisStock.updStockAuditStatus.1")
@ApiRequestObject(value = "审核商品", name = "updStockAuditStatus", groups = {"交易商端-基差交易-资源管理"}, params = {
        @ApiParamMeta(key = "id", desc = "商品ID", type = String.class),
        @ApiParamMeta(key = "auditStatus", desc = "审核状态", type = String.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "success", desc = "返回结果", type = boolean.class),
        @ApiParamMeta(key = "message", desc = "返回消息", type = String.class),
})
public class UpdStockAuditStatus implements IBusinessService {

    @Resource
    private BasisStockMapper basisStockMapper;

    @Resource
    private StockService stockService;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        String id = context.getValueObject(String.class, "id");
        String auditStatus = context.getValueObject(String.class, "auditStatus");

        List<String> idList = Arrays.asList(id.split(","));
        LambdaQueryWrapper<BasisStockEntity> queryWrapper = Wrappers.lambdaQuery(BasisStockEntity.class);
        queryWrapper.in(BasisStockEntity::getId, idList);
        queryWrapper.eq(BasisStockEntity::getResourceAuditStatus, BasisCottonConstants.resource_audit_status_2);
        List<BasisStockEntity> stockList = basisStockMapper.selectList(queryWrapper);

        if(stockList != null && stockList.size() > 0){
            for(BasisStockEntity entity : stockList){
                if(auditStatus != null && BasisCottonConstants.AUDIT_STATUS_1.equals(auditStatus)){
                    ActiveStockDTO activeStockDTO = ActiveStockDTO.builder()
                            .stockId(String.valueOf(entity.getId()))
                            .operatorId(context.getCurrentUserId())
                            .operatorName(context.getCurrentUserRealName())
                            .build();
                    stockService.activeStock(activeStockDTO);
                }
                if(auditStatus != null && BasisCottonConstants.AUDIT_STATUS_2.equals(auditStatus)){
                    LambdaUpdateWrapper<BasisStockEntity> updateWrapper = new LambdaUpdateWrapper<BasisStockEntity>();
                    updateWrapper.eq(BasisStockEntity::getId, entity.getId());
                    updateWrapper.set(BasisStockEntity::getResourceAuditStatus, BasisCottonConstants.resource_audit_status_4);
                    basisStockMapper.update(null, updateWrapper);
                }
            }
        }
        //构造返回数据
        this.createSuccessResponse(context);
        context.getResponseBody().setData("success", true);
        context.getResponseBody().setData("message", "审核成功");
    }

}
