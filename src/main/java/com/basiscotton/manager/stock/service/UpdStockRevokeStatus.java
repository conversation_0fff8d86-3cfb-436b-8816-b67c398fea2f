package com.basiscotton.manager.stock.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.basiscotton.base.entity.BasisStockEntity;
import com.basiscotton.base.enums.StockCancelType;
import com.basiscotton.base.enums.TradeMode;
import com.basiscotton.base.mappers.BasisStockMapper;
import com.basiscotton.tradingcore.business.StockService;
import com.basiscotton.tradingcore.dto.TradingResult;
import com.basiscotton.tradingcore.dto.WithdrawStockDTO;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * @Title: UpdStockRevokeStatus.java
 * @Description: 撤销商品
 * <AUTHOR>
 * @date 2025/5/28
 * @version V1.0
 */
@Service("basisStock.updStockRevokeStatus.1")
@ApiRequestObject(value = "撤销商品", name = "updStockRevokeStatus", groups = {"交易商端-基差交易-资源管理"}, params = {
        @ApiParamMeta(key = "id", desc = "商品ID", type = String.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "success", desc = "返回结果", type = boolean.class),
        @ApiParamMeta(key = "message", desc = "返回消息", type = String.class),
})
public class UpdStockRevokeStatus implements IBusinessService {

    @Resource
    private BasisStockMapper basisStockMapper;

    @Resource
    private StockService stockService;

    @Override
    public void doVerify(ServiceHandlerContext context) {
        //获取参数
        String id = context.getValueObject(String.class, "id");
        if (id == null) {
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "商品ID不能为空");
        }
    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        String id = context.getValueObject(String.class, "id");
        List<String> idList = Arrays.asList(id.split(","));
        LambdaQueryWrapper<BasisStockEntity> queryWrapper = Wrappers.lambdaQuery(BasisStockEntity.class);
        queryWrapper.in(BasisStockEntity::getId, idList);
        List<BasisStockEntity> stockList = basisStockMapper.selectList(queryWrapper);
        if(stockList != null && !stockList.isEmpty()){
            for(BasisStockEntity basisStockEntity : stockList){
                TradeMode tradeMode = TradeMode.getByValue(basisStockEntity.getTradeMode().getCode());
                        WithdrawStockDTO withdrawStockDTO = WithdrawStockDTO.builder()
                        .stockId(String.valueOf(basisStockEntity.getId()))
                        .operatorId(context.getCurrentUserCustomCode())
                        .operatorName(context.getCurrentUserRealName())
                        .cancelType(StockCancelType.SELLER_WITHDRAW)
                        .tradeMode(tradeMode)
                        .build();
                TradingResult tradingResult = stockService.cancelStock(withdrawStockDTO);
                if(!tradingResult.isSuccess()){
                    throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, tradingResult.getMessage());
                }
            }
        }else {
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "未查询到商品信息");
        }
        //构造返回数据
        this.createSuccessResponse(context);
        context.getResponseBody().setData("success", true);
        context.getResponseBody().setData("message", "下架成功");
    }

}
