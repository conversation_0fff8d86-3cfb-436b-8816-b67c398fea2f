package com.basiscotton.manager.stock.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.MarketSettingEntity;
import com.basiscotton.base.mappers.BasisMarketSettingMapper;
import org.apache.kafka.common.protocol.types.Field;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * @Title: GetSpotStockFeeAmount.java
 * @Description: 查询保证金、手续费总金额
 * @Author: GuoCe
 * @Date: 2025-09-03
 */

@Service("spotStock.getSpotStockFeeAmount.1")
@ApiRequestObject(value = "查询总金额", name = "getSpotStockFeeAmount", groups = {"交易商端-挂牌交易-资源管理"}, params = {
        @ApiParamMeta(key = "stockWeight", desc = "商品重量", type = String.class),
        @ApiParamMeta(key = "stockSource", desc = "商品来源", type = String.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "pagination", desc = "查询分页列表", type = MarketSettingEntity.class,pagination = true),
})
public class GetSpotStockFeeAmount implements IBusinessService {

    @Resource
    private BasisMarketSettingMapper basisMarketSettingMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {
        //获取参数
        String stockWeight = context.getValueObject(String.class, "stockWeight");
        String stockSource = context.getValueObject(String.class, "stockSource");
        if (stockWeight == null || stockSource == null) {
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "参数不能为空");
        }
    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        String stockWeight = context.getValueObject(String.class, "stockWeight");
        String stockSource = context.getValueObject(String.class, "stockSource");
        //查询公共设置列表数据
        LambdaQueryWrapper<MarketSettingEntity> queryWrapper = new LambdaQueryWrapper<MarketSettingEntity>();
        queryWrapper.eq(MarketSettingEntity::getBasisSettingType, BasisCottonConstants.basis_setting_type_3);
        queryWrapper.eq(MarketSettingEntity::getSettingStatus, BasisCottonConstants.COMMON_YES);
        MarketSettingEntity marketSetting = basisMarketSettingMapper.selectOne(queryWrapper);
        BigDecimal sellerBasisMarginStandard = marketSetting.getSellerBasisMarginStandard();
        BigDecimal sellerTradeFeeStandard = marketSetting.getSellerTradeFeeStandard();
        BigDecimal sellerDeliveryFeeStandard = marketSetting.getSellerDeliveryFeeStandard();
        BigDecimal sellerFeeStandardAmount = BigDecimal.ZERO;
        if (BasisCottonConstants.stock_source_1.equals(stockSource)) {
            sellerFeeStandardAmount = sellerTradeFeeStandard.add(sellerDeliveryFeeStandard);
        }else if (BasisCottonConstants.stock_source_2.equals(stockSource)) {
            sellerFeeStandardAmount = sellerBasisMarginStandard.add(sellerTradeFeeStandard).add(sellerDeliveryFeeStandard);
        }
        BigDecimal stockFeeAmount = sellerFeeStandardAmount.multiply(new BigDecimal(stockWeight)).setScale(2, BigDecimal.ROUND_HALF_UP);
        //创建响应实体
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("stockFeeAmount", stockFeeAmount);
    }
}
