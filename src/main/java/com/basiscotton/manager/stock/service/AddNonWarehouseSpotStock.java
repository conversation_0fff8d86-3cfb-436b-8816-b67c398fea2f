package com.basiscotton.manager.stock.service;

import com.applet.trade.base.mappers.CommonMappers;
import com.applet.trade.basisStock.vo.CustomInfoVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.BasisSequenceContants;
import com.basiscotton.base.entity.BasisStockBaseInfoEntity;
import com.basiscotton.base.entity.BasisStockEntity;
import com.basiscotton.base.entity.MarketSettingEntity;
import com.basiscotton.base.mappers.BasisMarketSettingMapper;
import com.basiscotton.base.mappers.BasisStockBaseInfoMapper;
import com.basiscotton.base.mappers.BasisStockMapper;
import com.basiscotton.manager.stock.vo.NonWarehouseSpotStockVo;
import com.sinosoft.dandelion.system.client.params.DataItem;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.id.core.service.BizIdGenerator;
import org.harry.dandelion.framework.sequence.ISequenceFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: AddNonWarehouseSpotStock.java
 * @Description: 添加非仓单资源商品-交易商-现货挂牌
 * @date 2025/9/1
 */
@Service("spotStock.addNonWarehouseSpotStock.1")
@ApiRequestObject(value = "添加非仓单现货商品-交易商-现货挂牌", name = "addNonWarehouseSpotStock", groups = {"交易商端-挂牌交易-资源管理"}, params = {
        @ApiParamMeta(key = "nonWarehouseSpotStockVo", desc = "非仓单资源提交信息", type = NonWarehouseSpotStockVo.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "success", desc = "返回结果", type = boolean.class),
        @ApiParamMeta(key = "message", desc = "返回消息", type = String.class),
})
public class AddNonWarehouseSpotStock implements IBusinessService {



    @Resource
    private CommonMappers commonMappers;

    @Resource
    private BasisStockMapper basisStockMapper;

    @Resource
    private BasisStockBaseInfoMapper basisStockBaseInfoMapper;

    @Resource
    private BasisMarketSettingMapper basisMarketSettingMapper;

    @Resource
    private ISequenceFactory sequenceFactory;
    @Override
    public void doVerify(ServiceHandlerContext context) {
        String currentRealName = context.getCurrentUserRealName();
        NonWarehouseSpotStockVo spotStockVo = context.getValueObject(NonWarehouseSpotStockVo.class, "nonWarehouseSpotStockVo");
        if (spotStockVo == null){
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "参数不能为空");
        }
        //本人已添加的不允许再次添加
        LambdaQueryWrapper<BasisStockEntity> queryWrapper = new LambdaQueryWrapper<BasisStockEntity>();
        queryWrapper.eq(BasisStockEntity::getBatchNo, spotStockVo.getBatchNo());
        queryWrapper.eq(BasisStockEntity::getCreateUserName, currentRealName);
        queryWrapper.in(BasisStockEntity::getResourceAuditStatus, BasisCottonConstants.resource_audit_status_4);
        if(basisStockMapper.selectCount(queryWrapper) > 0){
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "该批次已添加");
        }
        //已提交、未成交的批次不允许再次提交
        List<String> tradeStatusList = new ArrayList<String>();
        tradeStatusList.add(BasisCottonConstants.trade_status_1);
        tradeStatusList.add(BasisCottonConstants.trade_status_2);
        LambdaQueryWrapper<BasisStockEntity> queryWrapper1 = new LambdaQueryWrapper<BasisStockEntity>();
        queryWrapper1.eq(BasisStockEntity::getBatchNo, spotStockVo.getBatchNo());
        queryWrapper1.in(BasisStockEntity::getTradeStatus, tradeStatusList);
        if(basisStockMapper.selectCount(queryWrapper1) > 0){
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "该批次已上架");
        }
    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        String currentCustomCode = context.getCurrentUserCustomCode();
        String currentRealName = context.getCurrentUserRealName();
        NonWarehouseSpotStockVo spotStockVo = context.getValueObject(NonWarehouseSpotStockVo.class, "nonWarehouseSpotStockVo");
        String currentUserCustomType = context.getCurrentUserCustomType();
        String tradeName = BasisCottonConstants.custom_type_1.equals(currentUserCustomType) ?
                currentRealName :
                spotStockVo.getTraderName();
        String tradeCode = BasisCottonConstants.custom_type_1.equals(currentUserCustomType) ?
                currentCustomCode :
                spotStockVo.getTraderCode();
        MarketSettingEntity marketSettingEntity = this.getMarketSetting();
        long basisStockId = BizIdGenerator.getInstance().generateBizId();
        long basisStockBaseInfoId = BizIdGenerator.getInstance().generateBizId();
        String stockCode = sequenceFactory.create(BasisSequenceContants.SEQ_GPSPM).next();
        BasisStockEntity basisStockEntity = new BasisStockEntity();
        basisStockEntity.setId(basisStockId);
        basisStockEntity.setStockBaseInfoId(basisStockBaseInfoId);
        basisStockEntity.setStockCode(stockCode);
        basisStockEntity.setBatchNo(spotStockVo.getBatchNo());
        basisStockEntity.setResourceTradeType(new DataItem(BasisCottonConstants.resource_trade_type_1,""));
        basisStockEntity.setResourceDisplayType(new DataItem(BasisCottonConstants.resource_display_type_1,""));
        basisStockEntity.setResourceAuditStatus(new DataItem(BasisCottonConstants.resource_audit_status_4, ""));
        basisStockEntity.setStockPrice(spotStockVo.getStockPrice());
        basisStockEntity.setStockValidityEnd(spotStockVo.getStockValidityEnd());
        basisStockEntity.setNegotiable(spotStockVo.getNegotiable());
        basisStockEntity.setStockSource(new DataItem(BasisCottonConstants.stock_source_2,""));
        basisStockEntity.setTraderCode(tradeCode);
        basisStockEntity.setTraderName(tradeName);
        basisStockEntity.setSellerBasisMarginType(marketSettingEntity.getSellerBasisMarginType());
        basisStockEntity.setSellerBasisMarginStandard(marketSettingEntity.getSellerBasisMarginStandard());
        basisStockEntity.setSellerTradeFeeType(marketSettingEntity.getSellerTradeFeeType());
        basisStockEntity.setSellerDeliveryFeeType(marketSettingEntity.getSellerDeliveryFeeType());
        basisStockEntity.setSellerTradeFeeStandard(marketSettingEntity.getSellerTradeFeeStandard());
        basisStockEntity.setSellerDeliveryFeeStandard(marketSettingEntity.getSellerDeliveryFeeStandard());
        basisStockEntity.setTransportSubsidyApplyParty(spotStockVo.getTransportSubsidyApplyParty());
        basisStockEntity.setStockWeight(spotStockVo.getConditionedWeight());
        basisStockEntity.setRemark(spotStockVo.getRemark());
        basisStockEntity.setTradeMode(new DataItem(BasisCottonConstants.trade_mode_1,""));
        basisStockEntity.setCreateTime(new Date());
        basisStockEntity.setCreateUserName(currentRealName);
        basisStockMapper.insert(basisStockEntity);
        //商品基础信息
        BasisStockBaseInfoEntity basisStockBaseInfoEntity = new BasisStockBaseInfoEntity();
        basisStockBaseInfoEntity.setId(basisStockBaseInfoId);
        basisStockBaseInfoEntity.setStockCode(stockCode);
        basisStockBaseInfoEntity.setBatchNo(spotStockVo.getBatchNo());
        basisStockBaseInfoEntity.setConditionedWeight(spotStockVo.getConditionedWeight());
        basisStockBaseInfoEntity.setProductYear(spotStockVo.getProductYear());
        basisStockBaseInfoEntity.setColorGrade(spotStockVo.getColorGrade());
        basisStockBaseInfoEntity.setAvgMkl(spotStockVo.getAvgMkl());
        basisStockBaseInfoEntity.setAvgLength(spotStockVo.getAvgLength());
        basisStockBaseInfoEntity.setBreakValue(spotStockVo.getBreakValue());
        basisStockBaseInfoEntity.setUniformityAverageValue(spotStockVo.getUniformityAverageValue());
        basisStockBaseInfoEntity.setImpurityRate(spotStockVo.getImpurityRate());
        basisStockBaseInfoEntity.setMoistureRate(spotStockVo.getMoistureRate());
        basisStockBaseInfoEntity.setTraderCode(tradeCode);
        basisStockBaseInfoEntity.setTraderName(tradeName);
        basisStockBaseInfoMapper.insert(basisStockBaseInfoEntity);
        //构造返回数据
        this.createSuccessResponse(context);
        context.getResponseBody().setData("success", true);
        context.getResponseBody().setData("message", "添加成功");
    }

    private Map<String, CustomInfoVO> getCustomInfo( List<String> customerCodeList){
        //3、查询客户信息
        CustomInfoVO customInfoParm = new CustomInfoVO();
        customInfoParm.setCustomCodes(customerCodeList);
        List<CustomInfoVO> customInfoList = commonMappers.getCustomInfo(customInfoParm);
        //将customInfoList转化成map,以customCode为key,CustomInfoVO为值
        Map<String, CustomInfoVO> customInfoMap = new HashMap<String, CustomInfoVO>();
        for (CustomInfoVO customInfo : customInfoList) {
            customInfoMap.put(customInfo.getCustomCode(), customInfo);
        }
        return customInfoMap;
    }

    /**
     * 获取现货挂牌类型市场设置
     * @return
     */
    private MarketSettingEntity getMarketSetting(){
        LambdaQueryWrapper<MarketSettingEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MarketSettingEntity::getBasisSettingType, BasisCottonConstants.basis_setting_type_3);
        queryWrapper.eq(MarketSettingEntity::getSettingStatus, BasisCottonConstants.setting_status_1);
        MarketSettingEntity marketSettingEntity = basisMarketSettingMapper.selectOne(queryWrapper);
        if (marketSettingEntity != null) {
            return marketSettingEntity;
        } else {
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "当前不存在已启用的现货挂牌类型市场设置，不允许提交资源商品");
        }
    }
}
