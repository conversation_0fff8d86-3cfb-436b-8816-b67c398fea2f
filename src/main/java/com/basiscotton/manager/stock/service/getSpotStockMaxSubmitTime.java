package com.basiscotton.manager.stock.service;

import com.basiscotton.common.tradetime.service.SpotTradeTimeWindowService;
import com.basiscotton.manager.stock.vo.SpotStockVo;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: getSpotStockMaxSubmitTime.java
 * @Description: 获取现货挂牌资源提交的最大有效期
 * @date 2025/9/3 15:13
 */
@Service("spotStock.getSpotStockMaxSubmitTime.1")
@ApiRequestObject(value = "获取现货挂牌资源提交的最大有效期-交易商-现货挂牌", name = "getSpotStockMaxSubmitTime", groups = {"交易商端-挂牌交易-资源管理"}, params = {

})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "success", desc = "返回结果", type = boolean.class),
        @ApiParamMeta(key = "message", desc = "返回消息", type = String.class),
})
public class getSpotStockMaxSubmitTime implements IBusinessService {

    @Resource
    private SpotTradeTimeWindowService spotTradeTimeWindowService;
    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        LocalDate maxSubmitTime = spotTradeTimeWindowService.getMaxSubmitTimeWithNaturalDay();
        //创建响应实体
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("maxSubmitTime", maxSubmitTime);
    }
}
