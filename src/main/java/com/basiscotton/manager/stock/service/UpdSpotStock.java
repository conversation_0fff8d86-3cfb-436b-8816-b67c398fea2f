package com.basiscotton.manager.stock.service;

import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.BasisStockEntity;
import com.basiscotton.base.enums.TradeMode;
import com.basiscotton.base.mappers.BasisStockMapper;
import com.basiscotton.manager.stock.vo.SpotStockVo;
import com.basiscotton.tradingcore.business.StockService;
import com.basiscotton.tradingcore.dto.ChangeStockPriceDTO;
import com.basiscotton.tradingcore.dto.TradingResult;
import com.sinosoft.dandelion.system.client.params.DataItem;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;

/**
 * @Title: UpdSpotStock.java
 * @Description: 修改现货挂牌商品信息
 * <AUTHOR>
 * @date 2025/9/1
 * @version V1.0
 */
@Service("spotStock.updSpotStock.1")
@ApiRequestObject(value = "修改现货挂牌商品信息-交易商", name = "updSpotStock", groups = {"交易商端-挂牌交易-资源管理"}, params = {
        @ApiParamMeta(key = "spotStockVo", desc = "资源信息", type = SpotStockVo.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "success", desc = "返回结果", type = boolean.class),
        @ApiParamMeta(key = "message", desc = "返回消息"),
})
public class UpdSpotStock implements IBusinessService {

    @Resource
    private BasisStockMapper basisStockMapper;

    @Resource
    private StockService stockService;

    @Override
    public void doVerify(ServiceHandlerContext context) {
        //获取参数
        SpotStockVo spotStockVo = context.getValueObject(SpotStockVo.class, "spotStockVo");
        if (spotStockVo == null) {
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "资源信息参数不能为空");
        }
        Long stockId = spotStockVo.getID();
        BasisStockEntity stock = basisStockMapper.selectById(stockId);
        DataItem tradeStatus = stock.getTradeStatus();
        if(BasisCottonConstants.trade_status_3.equals(tradeStatus.getCode())){
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "商品已成交，无法资源维护");
        }
    }

    @Override
    public void doWork(ServiceHandlerContext context) {

        SpotStockVo spotStockVo = context.getValueObject(SpotStockVo.class, "spotStockVo");
        BasisStockEntity stock = basisStockMapper.selectById(spotStockVo.getID());
        LocalDate validityEnd;
        if (spotStockVo.getStockValidityEnd() != null) {
            validityEnd = spotStockVo.getStockValidityEnd().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        } else {
            validityEnd = stock.getStockValidityEnd().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        }
        ChangeStockPriceDTO dto = ChangeStockPriceDTO.builder()
                .stockId(String.valueOf(spotStockVo.getID()))
                .traderCode(context.getCurrentUserCustomCode())
                .traderName(context.getCurrentUserCustomName())
                .stockPrice(spotStockVo.getStockPrice())
                .validityEnd(validityEnd)
                .tradeMode(TradeMode.SPOT_LISTING)
                .build();

        //调用报价服务
        TradingResult tradingResult = stockService.changeStockPrice(dto);
        this.createSuccessResponse(context);
        context.getResponseBody().setData("success", tradingResult.isSuccess());
        context.getResponseBody().setData("message", tradingResult.getMessage());
    }
}
