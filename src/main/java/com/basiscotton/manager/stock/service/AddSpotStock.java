package com.basiscotton.manager.stock.service;

import com.applet.trade.base.mappers.CommonMappers;
import com.applet.trade.basisStock.vo.CustomInfoVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.BasisSequenceContants;
import com.basiscotton.base.entity.BasisStockBaseInfoEntity;
import com.basiscotton.base.entity.BasisStockEntity;
import com.basiscotton.base.entity.MarketSettingEntity;
import com.basiscotton.base.enums.TradeMode;
import com.basiscotton.base.mappers.BasisMarketSettingMapper;
import com.basiscotton.base.mappers.BasisStockBaseInfoMapper;
import com.basiscotton.base.mappers.BasisStockMapper;
import com.basiscotton.common.apiservice.IBasisTradeSettlementService;
import com.basiscotton.common.apiservice.IBasisTradeStorageService;
import com.basiscotton.common.apiservice.dto.HandleMarginDTO;
import com.basiscotton.manager.stock.vo.BasisBatchInfoVo;
import com.basiscotton.manager.stock.vo.SpotStockVo;
import com.basiscotton.tradingcore.cache.bo.StockLocker;
import com.cottoneasy.finance.api.ApiTradeService;
import com.cottoneasy.finance.api.vo.trade.LockingResultVo;
import com.cottoneasy.storage.trade.base.vo.response.BaseResponse;
import com.github.benmanes.caffeine.cache.Cache;
import com.sinosoft.dandelion.system.client.params.DataItem;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.harry.dandelion.framework.id.core.service.BizIdGenerator;
import org.harry.dandelion.framework.sequence.ISequenceFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: AddSpotStock.java
 * @Description: 添加仓单资源商品- 交易商 - 现货挂牌
 * @date 2025/9/1
 */
@Service("spotStock.addSpotStock.1")
@ApiRequestObject(value = "添加仓单现货商品-交易商-现货挂牌", name = "addSpotStock", groups = {"交易商端-挂牌交易-资源管理"}, params = {
        @ApiParamMeta(key = "spotStockVoList", desc = "仓单资源提交信息", type = SpotStockVo.class)
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "success", desc = "返回结果", type = boolean.class),
        @ApiParamMeta(key = "message", desc = "返回消息", type = String.class),
})
public class AddSpotStock implements IBusinessService {



    @Resource
    private CommonMappers commonMappers;

    @Resource
    private BasisStockMapper basisStockMapper;

    @Resource
    private BasisStockBaseInfoMapper basisStockBaseInfoMapper;

    @Resource
    private BasisMarketSettingMapper basisMarketSettingMapper;

    @Resource
    private ISequenceFactory sequenceFactory;

    @Resource
    private IBasisTradeSettlementService basisTradeSettlementService;

    @Resource
    private IBasisTradeStorageService basisTradeStorageService;

    @Resource
    Cache<String, StockLocker> basisStockCache;

    @Resource
    private ApiTradeService apiTradeService;
    @Override
    public void doVerify(ServiceHandlerContext context) {
        //获取参数
        List<SpotStockVo> spotStockVoList = context.getValueObjects(SpotStockVo.class, "spotStockVoList");
        if (spotStockVoList == null || spotStockVoList.isEmpty()) {
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "参数不能为空");
        }
        // 提取仓单号列表
        List<String> warehouseReceiptNoList = spotStockVoList.stream()
                .map(SpotStockVo::getWarehouseReceiptNo)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        //调用金融中心验证是否参与金融交易
        LockingResultVo lockingResultVo = apiTradeService.checkLockingStatus(String.join(",", warehouseReceiptNoList));
        List<String> financeWhsNoList = lockingResultVo.getWarehouseList();
        if(financeWhsNoList.size()>0) {
            log.debug("仓单号[{}]，已参与金融中心交易。",warehouseReceiptNoList.toString());
            throw new BusinessException(Constants.serviceErrorCode, warehouseReceiptNoList.toString()+"已参与金融中心交易，不能发起挂牌交易。");
        }
    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        String currentCustomCode = context.getCurrentUserCustomCode();
        String currentRealName = context.getCurrentUserRealName();
        MarketSettingEntity marketSettingEntity = this.getMarketSetting();
        List<SpotStockVo> spotStockVoList = context.getValueObjects(SpotStockVo.class, "spotStockVoList");
        // 提取仓单号列表
        List<String> warehouseReceiptNoList = spotStockVoList.stream()
                .map(SpotStockVo::getWarehouseReceiptNo)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        Map<String, BasisBatchInfoVo> batchInfoVoMap = getWhsBatchInfo(warehouseReceiptNoList);
        for (SpotStockVo spotStockVo : spotStockVoList) {
            String warehouseReceiptNo = spotStockVo.getWarehouseReceiptNo();
            BasisStockEntity basisStockEntity = new BasisStockEntity();
            long spotStockId = BizIdGenerator.getInstance().generateBizId();
            long spotStockBaseInfoId = BizIdGenerator.getInstance().generateBizId();
            String stockCode = sequenceFactory.create(BasisSequenceContants.SEQ_GPSPM).next();
            //交易手续费标准
            BigDecimal sellerTradeFeeStandard = marketSettingEntity.getSellerTradeFeeStandard();
            //商品公定重量
            BigDecimal stockWeight = batchInfoVoMap.get(warehouseReceiptNo).getConditionedWeight();
            //交易手续费金额
            BigDecimal sellerTradeFeeAmount = sellerTradeFeeStandard.multiply(stockWeight).setScale(2, BigDecimal.ROUND_HALF_UP);
            basisStockEntity.setSellerFreezeMarginAmount(BigDecimal.ZERO);
            basisStockEntity.setSellerFreezeDeliveryFeeAmount(BigDecimal.ZERO);
            basisStockEntity.setId(spotStockId);
            basisStockEntity.setStockBaseInfoId(spotStockBaseInfoId);
            basisStockEntity.setStockCode(stockCode);
            basisStockEntity.setWarehouseReceiptNo(warehouseReceiptNo);
            basisStockEntity.setBatchNo(batchInfoVoMap.get(warehouseReceiptNo).getBatchNo());
            basisStockEntity.setResourceTradeType(new DataItem(BasisCottonConstants.resource_trade_type_1,""));
            basisStockEntity.setResourceDisplayType(new DataItem(BasisCottonConstants.resource_display_type_1,""));
            //是- 全都自动审核，否-有备注的需手动审核
            if(StringUtil.isNotEmpty(spotStockVo.getRemark()) && BasisCottonConstants.audit_status_1.equals(marketSettingEntity.getAuditRemarkStatus())) {
                basisStockEntity.setResourceAuditStatus(new DataItem(BasisCottonConstants.resource_audit_status_1, ""));
                basisStockEntity.setTradeStatus(new DataItem(BasisCottonConstants.trade_status_1,""));
            }else{
                basisStockEntity.setResourceAuditStatus(new DataItem(BasisCottonConstants.resource_audit_status_2, ""));
                basisStockEntity.setTradeStatus(new DataItem(BasisCottonConstants.trade_status_2,""));
                StockLocker stockLocker1 = new StockLocker(String.valueOf(spotStockId));
                basisStockCache.put(String.valueOf(spotStockId), stockLocker1);
            }
            basisStockEntity.setStockPrice(spotStockVo.getStockPrice());
            basisStockEntity.setStockWeight(stockWeight);
            basisStockEntity.setStockValidityEnd(spotStockVo.getStockValidityEnd());
            basisStockEntity.setNegotiable(spotStockVo.getNegotiable());
            basisStockEntity.setStockSource(new DataItem(BasisCottonConstants.stock_source_1,""));
            basisStockEntity.setTraderCode(batchInfoVoMap.get(warehouseReceiptNo).getTraderCode());
            basisStockEntity.setTraderName(batchInfoVoMap.get(warehouseReceiptNo).getTraderName());
            basisStockEntity.setSellerBasisMarginType(marketSettingEntity.getSellerBasisMarginType());
            basisStockEntity.setSellerBasisMarginStandard(marketSettingEntity.getSellerBasisMarginStandard());
            basisStockEntity.setSellerTradeFeeType(marketSettingEntity.getSellerTradeFeeType());
            basisStockEntity.setSellerDeliveryFeeType(marketSettingEntity.getSellerDeliveryFeeType());
            basisStockEntity.setSellerTradeFeeStandard(marketSettingEntity.getSellerTradeFeeStandard());
            basisStockEntity.setSellerDeliveryFeeStandard(marketSettingEntity.getSellerDeliveryFeeStandard());
            basisStockEntity.setTransportSubsidyApplyParty(spotStockVo.getTransportSubsidyApplyParty());
            basisStockEntity.setSellerTradeFeeCollectPhase(spotStockVo.getSellerTradeFeeCollectPhase());
            basisStockEntity.setRemark(spotStockVo.getRemark());
            basisStockEntity.setTradeMode(new DataItem(BasisCottonConstants.trade_mode_1,""));
            basisStockEntity.setCreateTime(new Date());
            basisStockEntity.setCreateUserName(currentRealName);
            //成交时收取手续费 需冻结
            if (Objects.equals(BasisCottonConstants.SELLER_TRADE_FEE_COLLECT_PHASE_1, spotStockVo.getSellerTradeFeeCollectPhase().getCode())) {
                basisStockEntity.setSellerFreezeTradeFeeAmount(sellerTradeFeeAmount);
                HandleMarginDTO marginDTO = HandleMarginDTO.quoteToHandleMarginDTOFromStockTradFee(TradeMode.SPOT_LISTING).apply(basisStockEntity);
                basisTradeSettlementService.freezeMargin(Collections.singletonList(marginDTO));
            }else {
                basisStockEntity.setSellerFreezeTradeFeeAmount(BigDecimal.ZERO);
            }
//            // 修改仓单状态为现货挂牌
//            List<BaseResponse> baseResponses = basisTradeStorageService.updateWarehouseReceiptStatus(warehouseReceiptNo,
//                    BasisCottonConstants.TRANSACTION_STATUS_4,
//                    null,
//                    null);
//            if (baseResponses == null || baseResponses.isEmpty()) {
//                log.warn("卖方提交商品, 修改仓单状态为现货挂牌失败, 商品 ID: {}", spotStockId);
//                throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, "修改仓单状态失败");
//            }
            basisStockMapper.insert(basisStockEntity);

            BasisStockBaseInfoEntity basisStockBaseInfoEntity = new BasisStockBaseInfoEntity();
            basisStockBaseInfoEntity.setId(spotStockBaseInfoId);
            basisStockBaseInfoEntity.setStockCode(stockCode);
            basisStockBaseInfoEntity.setWarehouseReceiptNo(warehouseReceiptNo);
            basisStockBaseInfoEntity.setBatchNo(batchInfoVoMap.get(warehouseReceiptNo).getBatchNo());
            basisStockBaseInfoEntity.setQuantity(batchInfoVoMap.get(warehouseReceiptNo).getQuantity());
            basisStockBaseInfoEntity.setMarksWeight(batchInfoVoMap.get(warehouseReceiptNo).getMarkWeight());
            basisStockBaseInfoEntity.setConditionedWeight(batchInfoVoMap.get(warehouseReceiptNo).getConditionedWeight());
            basisStockBaseInfoEntity.setCrossWeight(batchInfoVoMap.get(warehouseReceiptNo).getGrossWeight());
            basisStockBaseInfoEntity.setProductYear(batchInfoVoMap.get(warehouseReceiptNo).getProductYear());
            basisStockBaseInfoEntity.setFactoryCode(batchInfoVoMap.get(warehouseReceiptNo).getProcessCode());
            basisStockBaseInfoEntity.setFactoryName(batchInfoVoMap.get(warehouseReceiptNo).getProcessCompany());
            basisStockBaseInfoEntity.setWhsPickMode(new DataItem(batchInfoVoMap.get(warehouseReceiptNo).getWhsPickMode(),""));
            basisStockBaseInfoEntity.setMarkDesc(batchInfoVoMap.get(warehouseReceiptNo).getMarkDesc());
            basisStockBaseInfoEntity.setColorGrade(batchInfoVoMap.get(warehouseReceiptNo).getColorGradeCode());

            basisStockBaseInfoEntity.setWhiteCottonL1(batchInfoVoMap.get(warehouseReceiptNo).getWhiteCottonL1());
            basisStockBaseInfoEntity.setWhiteCottonL2(batchInfoVoMap.get(warehouseReceiptNo).getWhiteCottonL2());
            basisStockBaseInfoEntity.setWhiteCottonL3(batchInfoVoMap.get(warehouseReceiptNo).getWhiteCottonL3());
            basisStockBaseInfoEntity.setWhiteCottonL4(batchInfoVoMap.get(warehouseReceiptNo).getWhiteCottonL4());
            basisStockBaseInfoEntity.setWhiteCottonL5(batchInfoVoMap.get(warehouseReceiptNo).getWhiteCottonL5());
            basisStockBaseInfoEntity.setWhiteCottonL6(batchInfoVoMap.get(warehouseReceiptNo).getWhiteCottonL6());
            basisStockBaseInfoEntity.setSpotCottonL1(batchInfoVoMap.get(warehouseReceiptNo).getSpotCottonL1());
            basisStockBaseInfoEntity.setSpotCottonL2(batchInfoVoMap.get(warehouseReceiptNo).getSpotCottonL2());
            basisStockBaseInfoEntity.setSpotCottonL3(batchInfoVoMap.get(warehouseReceiptNo).getSpotCottonL3());
            basisStockBaseInfoEntity.setSpotCottonL4(batchInfoVoMap.get(warehouseReceiptNo).getSpotCottonL4());
            basisStockBaseInfoEntity.setYellowIshCottonL1(batchInfoVoMap.get(warehouseReceiptNo).getYellowIshCottonL1());
            basisStockBaseInfoEntity.setYellowIshCottonL2(batchInfoVoMap.get(warehouseReceiptNo).getYellowIshCottonL2());
            basisStockBaseInfoEntity.setYellowIshCottonL3(batchInfoVoMap.get(warehouseReceiptNo).getYellowIshCottonL3());
            basisStockBaseInfoEntity.setYellowCottonL1(batchInfoVoMap.get(warehouseReceiptNo).getYellowCottonL1());
            basisStockBaseInfoEntity.setYellowCottonL2(batchInfoVoMap.get(warehouseReceiptNo).getYellowCottonL2());

            basisStockBaseInfoEntity.setMainMkl(batchInfoVoMap.get(warehouseReceiptNo).getMainMkl());
            basisStockBaseInfoEntity.setAvgMkl(batchInfoVoMap.get(warehouseReceiptNo).getAvgMkl());
            basisStockBaseInfoEntity.setMaxMkl(batchInfoVoMap.get(warehouseReceiptNo).getMaxMkl());
            basisStockBaseInfoEntity.setMinMkl(batchInfoVoMap.get(warehouseReceiptNo).getMinMkl());
            basisStockBaseInfoEntity.setAvgBreakRate(batchInfoVoMap.get(warehouseReceiptNo).getAvgBreakRate());

            basisStockBaseInfoEntity.setMainLength(batchInfoVoMap.get(warehouseReceiptNo).getMainLength());
            basisStockBaseInfoEntity.setAvgLength(batchInfoVoMap.get(warehouseReceiptNo).getAvgLength());

            basisStockBaseInfoEntity.setRollingQuality(batchInfoVoMap.get(warehouseReceiptNo).getRollingQualityAvg());
            basisStockBaseInfoEntity.setRollingQualityAvg(batchInfoVoMap.get(warehouseReceiptNo).getRollingQualityAvg());
            //basisStockBaseInfoEntity.setRollingQualityLevel(inspectionValueMap.get(warehouseReceiptNo).getRollingQualityAvg());
            basisStockBaseInfoEntity.setUniformityAverageValue(batchInfoVoMap.get(warehouseReceiptNo).getAvgLenUniformity());

            basisStockBaseInfoEntity.setMoistureRate(batchInfoVoMap.get(warehouseReceiptNo).getMoistureRate());
            basisStockBaseInfoEntity.setImpurityRate(batchInfoVoMap.get(warehouseReceiptNo).getImpurityRate());
            basisStockBaseInfoEntity.setBreakValue(batchInfoVoMap.get(warehouseReceiptNo).getAvgBreakRate());

            basisStockBaseInfoEntity.setStockName(batchInfoVoMap.get(warehouseReceiptNo).getStockName());

            //产地
            basisStockBaseInfoEntity.setIntactPlace(batchInfoVoMap.get(warehouseReceiptNo).getPlaceName());
            basisStockBaseInfoEntity.setInspectDate(batchInfoVoMap.get(warehouseReceiptNo).getInspectDate());

            basisStockBaseInfoEntity.setTraderCode(batchInfoVoMap.get(warehouseReceiptNo).getTraderCode());
            basisStockBaseInfoEntity.setTraderName(batchInfoVoMap.get(warehouseReceiptNo).getTraderName());
            basisStockBaseInfoEntity.setStorageWhsCode(batchInfoVoMap.get(warehouseReceiptNo).getWhsCode());
            basisStockBaseInfoEntity.setStorageWhsName(batchInfoVoMap.get(warehouseReceiptNo).getWhsName());
            basisStockBaseInfoEntity.setWhsSupervisionStatus(batchInfoVoMap.get(warehouseReceiptNo).getWhsSupervisionStatus());
            basisStockBaseInfoEntity.setSuperviseCode(batchInfoVoMap.get(warehouseReceiptNo).getLoanCompanyCode());
            basisStockBaseInfoEntity.setSuperviseName(batchInfoVoMap.get(warehouseReceiptNo).getLoanCompanyName());
            basisStockBaseInfoEntity.setWhsPledgeChannel(batchInfoVoMap.get(warehouseReceiptNo).getWhsPledgeChannel());
            basisStockBaseInfoMapper.insert(basisStockBaseInfoEntity);
        }

        //构造返回数据
        this.createSuccessResponse(context);
        context.getResponseBody().setData("success", true);
        context.getResponseBody().setData("message", "添加成功");
    }

    private Map<String, BasisBatchInfoVo> getWhsBatchInfo(List<String> warehouseReceiptNumberList){
        List<BasisBatchInfoVo> BatchInfoVoList = basisStockBaseInfoMapper.getBatchInfoVo(warehouseReceiptNumberList);
        BatchInfoVoList.addAll(BatchInfoVoList);
        Map<String, BasisBatchInfoVo> batchInfoVoMap = BatchInfoVoList.stream()
                .collect(Collectors.toMap(
                        BasisBatchInfoVo::getWarehouseReceiptNumber,
                        batchInfoVo -> batchInfoVo,
                        (existing, replacement) -> existing // 保留第一个出现的对象
                ));
        return batchInfoVoMap;
    }

    /**
     * 获取现货挂牌类型市场设置
     * @return
     */
    private MarketSettingEntity getMarketSetting(){
        LambdaQueryWrapper<MarketSettingEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MarketSettingEntity::getBasisSettingType, BasisCottonConstants.basis_setting_type_3);
        queryWrapper.eq(MarketSettingEntity::getSettingStatus, BasisCottonConstants.setting_status_1);
        MarketSettingEntity marketSettingEntity = basisMarketSettingMapper.selectOne(queryWrapper);
        if (marketSettingEntity != null) {
            return marketSettingEntity;
        } else {
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "当前不存在已启用的现货挂牌类型市场设置，不允许提交资源商品");
        }
    }
}
