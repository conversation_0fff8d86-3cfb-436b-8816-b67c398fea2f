//package com.basiscotton.manager.stock.service;
//
//import com.alibaba.excel.EasyExcel;
//import com.sinosoft.dandelion.system.client.params.DataItem;
//import lombok.SneakyThrows;
//import org.harry.dandelion.framework.common.utils.DateUtils;
//import org.harry.dandelion.framework.core.service.IBusinessService;
//import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
//import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
//import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
//import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
//import org.harry.dandelion.framework.id.core.service.BizIdGenerator;
//import org.harry.dandelion.framework.sequence.ISequence;
//import org.harry.dandelion.framework.sequence.ISequenceFactory;
//import org.harry.dandelion.framework.web.common.UploadFile;
//import org.harry.dandelion.framework.web.message.file.UploadFileMessageBody;
//import org.springframework.stereotype.Service;
//import org.springframework.web.multipart.MultipartFile;
//
//import javax.annotation.Resource;
//import java.math.BigDecimal;
//import java.util.ArrayList;
//import java.util.List;
//
///**
// *
// * @Title: importConsultStock.java
// * @Description: 导入他人授权仓单
// * <AUTHOR>
// * @date 2021年12月29日
// * @version V1.0
// */
//@Service("consultTradingTrader.addOtherCD.1")
//@ApiRequestObject(value = "导入他人授权仓单",name = "addOtherCD",groups = {"仓单管理"},params = {
//        @ApiParamMeta(key = "file", desc = "文件", type = MultipartFile.class)
//})
//@ApiResponseObject(params = {
//		@ApiParamMeta(key = "success",desc = "返回结果",type = boolean.class),
//		@ApiParamMeta(key = "consultTitle",desc = "返回消息",type = String.class),
//		@ApiParamMeta(key = "linitStatusTitle",desc = "返回消息",type = String.class),
//		@ApiParamMeta(key = "WhsNoIsNull",desc = "返回消息",type = String.class),
//		@ApiParamMeta(key = "NoWhsStr",desc = "返回消息",type = String.class),
//})
//public class ManagerBatchImportFCDStock implements IBusinessService {
//
//	@Override
//	public void doVerify(ServiceHandlerContext context) {
//
//	}
//
//	@SneakyThrows
//	@SuppressWarnings("unchecked")
//	@Override
//	public void doWork(ServiceHandlerContext context) {
//        //接受文件信息
//		//接受文件信息
//		UploadFileMessageBody body = (UploadFileMessageBody) context.getRequestBody();
//		List<UploadFile> files = (List<UploadFile>) body.getDataSet().get("file");
//		UploadFile file = files.get(0);
//		Object objList = EasyExcel.read(file.getInputStream(), ImportOtherWarehouseReceiptVo.class, null).sheet(0).doReadSync();
//		List<ImportOtherWarehouseReceiptVo> orderLists = (List<ImportOtherWarehouseReceiptVo>) objList;
//		//提示信息
//		int dataNum = 0;
//		//构造商品集合、仓单号集合
//		List<ConsultStockEntity> stockList = new ArrayList<ConsultStockEntity>();
//		List<String> warehouseReceiptNoList = new ArrayList<String>();
//		for (ImportOtherWarehouseReceiptVo vo : orderLists) {
//			warehouseReceiptNoList.add(vo.getWareHouseReceiptNo());
//			ConsultStockEntity stock = new ConsultStockEntity();
//			stock.setWarehouseReceiptNo(vo.getWareHouseReceiptNo());
//			stock.setBatchNo(vo.getBatchNumber());
//			stock.setTraderCode(vo.getTraderCode());
//			stock.setTraderName(vo.getTraderName());
//			stock.setWarehouseName(vo.getWhsName());
//			stockList.add(stock);
//		}
//		//验证仓单是否可以授权
//		ITradeVerifyServerHandlerChainBuilder iTradeVerifyServerHandlerChainBuilder = TradeHandlerChainFactory.getVerifiyHandlerChainBuilder();
//		iTradeVerifyServerHandlerChainBuilder.buildVerifyOtherAddStatusHandlerChain(warehouseReceiptNoList,stockList,true);
//
//		// 构造返回数据
//		for (ImportOtherWarehouseReceiptVo orderList : orderLists) {
//			WareHouseReceiptsVo whs = new WareHouseReceiptsVo();
//			whs = warehouseReceiptMapper.selectWareHouse1(orderList.getWareHouseReceiptNo(),null);
//			if (whs != null) {
//				dataNum += 1;
//				this.insertData(whs,context.getCurrentUserCustomCode(),context.getCurrentUserCustomName());
//			}
//		}
//		//添加资源导入记录
//		StockResourcesRecordEntity entity = new StockResourcesRecordEntity();
//		entity.setId(BizIdGenerator.getInstance().generateBizId());
//		entity.setResourcesType(new DataItem(StockCottonContants.WAREHOUSE_RECEIPT_TYPE_1, ""));
//		entity.setImportTime(DateUtils.getDate());
//		entity.setImportNum(dataNum);
//		if (dataNum != 0) {
//			entity.setImportStatus(new DataItem(StockCottonContants.SUCCESS, ""));
//		} else {
//			entity.setImportStatus(new DataItem(StockCottonContants.FAIL, ""));
//		}
//		//获取用户信息
//		entity.setOperator(context.getCurrentUserCustomCode());
//		entity.setOperatorId(context.getCurrentUserId());
//		stockResourcesRecordMapper.insert(entity);
//
//		this.createSuccessResponse(context);
//		context.getResponseBody().setData("success", true);
//		context.getResponseBody().setData("dataNum", "成功导入" + dataNum + "条数据");
//	}
//
//	private void insertData(WareHouseReceiptsVo whs,String traderCode,String traderName) {
//		//商品编号
//		ISequence sequence = sequenceFactory.create(SequenceContants.SEQ_STOCKNO);
//		String stockNo = sequence.next();
//
//		ConsultStockEntity stock = new ConsultStockEntity();
//		stock = RoundMapstruct.INSTANCE.OtherEntityToStock(whs);
//		stock.setId(BizIdGenerator.getInstance().generateBizId());
//		stock.setStockNo(stockNo);
//		stock.setConsultPriceContract1(BigDecimal.ZERO);
//		stock.setConsultStockType(new DataItem(StockCottonContants.STOCK_1, ""));
//		stock.setConsultStockStatus(Integer.valueOf(StockCottonContants.CONSULT_STOCK_STATUS_1));
//		stock.setSuperviseName(whs.getLoanCompanyName());
//		stock.setExtendField3(whs.getLoanCompanyCode());
//		stock.setTrdSupervisionStatus(whs.getTrdSupervisionStatus());
//		stock.setTrdPledgeStatus(whs.getWhsPledgeStatus());
//		stock.setTrdPledgeChannel(whs.getTrdPledgeChannel());
//		stock.setStorageStatus(whs.getTrdStorageStatus());
//        stock.setSignBuyerCode(traderCode);
//        stock.setSignBuyerName(traderName);
//        stock.setAgentCode(traderCode);
//		stock.setAgentName(traderName);
//		stock.setConsultWithStatus(Integer.valueOf(StockCottonContants.CONSULT_WITH_STATUS_NO));
//		stock.setLimitStatus(new DataItem(StockCottonContants.LIMIT_STATUS_1, ""));
//		consultStockMapper.insert(stock);
//	}
//}