package com.basiscotton.manager.stock.enums;

import lombok.Getter;

/**
 * @Title: ColorGradeEnum.java
 * @Description: 颜色级枚举
 *
 * <AUTHOR>
 * @date 2025/5/24
 * @version V1.0
 */
public enum ColorGradeEnum {

    WHITE_COTTON_L1("11", "WHITE_COTTON_L1","白棉1级"),
    WHITE_COTTON_L2("21", "WHITE_COTTON_L2","白棉2级"),
    WHITE_COTTON_L3("31", "WHITE_COTTON_L3","白棉3级"),
    WHITE_COTTON_L4("41", "WHITE_COTTON_L4","白棉4级"),
    WHITE_COTTON_L5("51", "WHITE_COTTON_L5","白棉5级"),
    WHITE_COTTON_L6("61", "WHITE_COTTON_L6","白棉6级"),
    SPOT_COTTON_L1("12", "SPOT_COTTON_L1","淡点污棉1级"),
    SPOT_COTTON_L2("22", "SPOT_COTTON_L2","淡点污棉2级"),
    SPOT_COTTON_L3("32", "SPOT_COTTON_L3","淡点污棉3级"),
    SPOT_COTTON_L4("42", "SPOT_COTTON_L4","淡点污棉4级"),
    YELLOW_ISH_COTTON_L1("13", "YELLOW_ISH_COTTON_L1","淡黄染棉1级"),
    YELLOW_ISH_COTTON_L2("23", "YELLOW_ISH_COTTON_L2","淡黄染棉2级"),
    YELLOW_ISH_COTTON_L3("33", "YELLOW_ISH_COTTON_L3","淡黄染棉3级"),
    YELLOW_COTTON_L1("14", "YELLOW_COTTON_L1","黄染棉1级"),
    YELLOW_COTTON_L2("24", "YELLOW_COTTON_L2","黄染棉2级");

    @Getter
    private final String code;
    @Getter
    private final String name;
    @Getter
    private final String describe;

    ColorGradeEnum(String code, String name, String describe) {
            this.code = code;
            this.name = name;
            this.describe = describe;
    }

    public static String getNameByCode(String code) {
        for (ColorGradeEnum value : ColorGradeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return null;
    }

    public static String getCodeByDescribe(String describe) {
        for (ColorGradeEnum value : ColorGradeEnum.values()) {
            if (value.getDescribe().equals(describe)) {
                return value.getCode();
            }
        }
        return null;
    }
}
