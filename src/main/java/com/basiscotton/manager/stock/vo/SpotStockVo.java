package com.basiscotton.manager.stock.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.sinosoft.dandelion.system.client.params.DataItem;
import com.sinosoft.dandelion.system.client.serializer.DataItemCodeDeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: SpotStockVo.java
 * @Description: 现货挂牌仓单资源提交VO
 * @date 2025/9/1
 */

@Data
public class SpotStockVo implements Serializable {


    @ApiModelProperty(value="商品id")
    private Long ID;

    /**
     * 商品价格
     */
    @ApiModelProperty(value="商品价格")
    private BigDecimal stockPrice;

    /**
     *挂牌有效期(截止日期)
     */
    @ApiModelProperty(value = "挂牌有效期(截止日期)")
    private Date stockValidityEnd;

    /**
     * 能否议价 0 否 1 是
     */
    @ApiModelProperty(value="能否议价 0 否 1 是")
    private Integer negotiable;

    /**
     * 备注
     */
    @ApiModelProperty(value="备注")
    private String remark;

    /**
     * 运费补贴申领方
     */
    @ApiModelProperty(value="运费补贴申领方")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem transportSubsidyApplyParty;

    /**
     * 卖方手续费收取环节 1 成交时 2 随货款
     */
    @ApiModelProperty("卖方手续费收取环节 1 成交时 2 随货款")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem sellerTradeFeeCollectPhase;

    /**
     * 仓单号
     */
    @ApiModelProperty("仓单号")
    private String warehouseReceiptNo;

}
