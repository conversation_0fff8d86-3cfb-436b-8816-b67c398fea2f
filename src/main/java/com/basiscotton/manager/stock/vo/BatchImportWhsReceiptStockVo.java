package com.basiscotton.manager.stock.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import com.sinosoft.dandelion.system.client.params.DataItem;
import com.sinosoft.dandelion.system.client.serializer.DataItemCodeDeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * <AUTHOR>
 * @version V1.0
 * @Title: BatchImportWhsReceiptStockVo
 * @Description: 仓单商品导入
 * @date 2025/9/10
 */
@Data
public class BatchImportWhsReceiptStockVo implements Serializable {

    @ExcelProperty(value = "仓单号", index = 0)
    @ApiModelProperty(value = "仓单号")
    private String warehouseReceiptNo;

    @ExcelProperty(value = "批号", index = 1)
    @ApiModelProperty(value = "批号")
    private String batchNo;

    @ExcelProperty(value = "单价(元/吨)", index = 2)
    @ApiModelProperty(value = "商品价格")
    private BigDecimal stockPrice;

    @ExcelProperty(value = "挂牌有效期（xxxx-xx-xx）", index = 3)
    @ApiModelProperty(value = "挂牌有效期(截止日期)")
    private Date stockValidityEnd;

    @ExcelProperty(value = "运费补贴申领方", index = 4)
    @ApiModelProperty(value="运费补贴申领方")
    private String transportSubsidyApplyParty;
}