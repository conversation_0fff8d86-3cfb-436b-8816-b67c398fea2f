package com.basiscotton.manager.stock.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Title: TraderAllStockReqVo.java
 * @Description: 交易商资源列表查询vo
 * <AUTHOR>
 * @date 2025/5/24
 * @version V1.0
 */
@Data
public class TraderAllStockReqVo implements Serializable {

	//默认查询项
	private String resourceAuditStatus;
	private List<String> tradeStatus;
	private String traderCode;
	private String traderName;

	//页面查询项
	@ApiModelProperty(value = "仓单号")
	private String warehouseReceiptNo;

	@ApiModelProperty(value = "批号")
	private String batchNo;

	@ApiModelProperty(value = "仓库名称")
	private String storageWhsName;

	@ApiModelProperty(value = "商品来源")
	private String stockSource;

	@ApiModelProperty(value = "商品码")
	private String stockCode;

	@ApiModelProperty("创建人")
	private String createUserName;
}

