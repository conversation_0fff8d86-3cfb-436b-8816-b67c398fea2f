package com.basiscotton.manager.stock.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.sinosoft.dandelion.system.client.params.DataItem;
import com.sinosoft.dandelion.system.client.serializer.DataItemCodeDeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: SpotStockVo.java
 * @Description: 现货挂牌仓单资源提交VO
 * @date 2025/9/1
 */

@Data
public class NonWarehouseSpotStockVo implements Serializable {

    /**
     * 批号
     */
    @ApiModelProperty("批号")
    private String batchNo;

    @ApiModelProperty(value = "颜色级")
    private String colorGrade;

    @ApiModelProperty("公定重量（吨）")
    private BigDecimal conditionedWeight;

    @ApiModelProperty(value="马克隆值平均值")
    private BigDecimal avgMkl;

    @ApiModelProperty(value = "长度")
    private BigDecimal avgLength;

    @ApiModelProperty(value = "强力")
    private BigDecimal breakValue;

    @ApiModelProperty(value = "长整")
    private String uniformityAverageValue;

    @ApiModelProperty(value = "含杂")
    private String impurityRate;

    @ApiModelProperty(value = "回潮")
    private String moistureRate;

    @ApiModelProperty(value = "生产年度")
    private String productYear;

    /**
     * 商品价格
     */
    @ApiModelProperty(value="商品价格")
    private BigDecimal stockPrice;

    /**
     *挂牌有效期(截止日期)
     */
    @ApiModelProperty(value = "挂牌有效期(截止日期)")
    private Date stockValidityEnd;

    /**
     * 能否议价 0 否 1 是
     */
    @ApiModelProperty(value="能否议价 0 否 1 是")
    private Integer negotiable;

    /**
     * 备注
     */
    @ApiModelProperty(value="备注")
    private String remark;

    @ApiModelProperty(value = "运费补贴申领方")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem transportSubsidyApplyParty;

    @ApiModelProperty(value = "货权人名称")
    private String traderName;

    @ApiModelProperty(value = "货权人代码")
    private String traderCode;

}
