package com.basiscotton.manager.stock.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Title: UpdBasisStockVo.java
 * @Description: 资源维护vo
 * <AUTHOR>
 * @date 2025/6/3
 * @version V1.0
 */
@Data
public class UpdBasisStockVo implements Serializable {

    @ApiModelProperty(value="商品id")
    private String idStr;
    /**
     * 期货合约
     */
    @ApiModelProperty(value="期货合约")
    private String futureCode;

    /**
     *基差价格
     */
    @ApiModelProperty(value = "基差价格")
    private BigDecimal basisPrice;

    /**
     *点价有效期
     */
    @ApiModelProperty(value = "点价有效期")
    private Date pricingValidTime;

    /**
     * 备注
     */
    @ApiModelProperty(value="备注")
    private String remark;

    /**
     *利息成本
     */
    @ApiModelProperty(value = "利息成本")
    private BigDecimal interestCost;
}
