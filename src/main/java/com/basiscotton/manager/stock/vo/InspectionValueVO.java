package com.basiscotton.manager.stock.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: InspectionValueVo.java
 * @Description: 棉花质量检验数据值对象
 * @date 2025/9/4
 */

@Data
public class InspectionValueVO {

    @ApiModelProperty(value = "批号")
    private String batchNo;

    @ApiModelProperty(value = "颜色级")
    private String colorGrade;

    @ApiModelProperty("公定重量（吨）")
    private BigDecimal conditionedWeight;

    @ApiModelProperty(value="马克隆值平均值")
    private BigDecimal avgMkl;

    @ApiModelProperty(value = "长度")
    private BigDecimal avgLength;

    @ApiModelProperty(value = "强力")
    private String breakValue;

    @ApiModelProperty(value = "长整")
    private String uniformityAverageValue;

    @ApiModelProperty(value = "含杂")
    private String impurityRate;

    @ApiModelProperty(value = "回潮")
    private String moistureRate;

    @ApiModelProperty(value = "生产年度")
    private String productYear;

}
