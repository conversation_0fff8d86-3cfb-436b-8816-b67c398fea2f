package com.basiscotton.manager.stock.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: BasisStockVo.java
 * @Description: 仓单资源提交VO
 * @date 2025/5/25 19:18
 */
@Data
public class BasisStockVo implements Serializable {


    /**
     * 期货合约
     */
    @ApiModelProperty(value="期货合约")
    private String futureCode;

    @ApiModelProperty(value="点价方1-买方 2-卖方")
    private String pricingParty;

    /**
     *基差价格
     */
    @ApiModelProperty(value = "基差价格")
    private BigDecimal basisPrice;

    /**
     *点价有效期
     */
    @ApiModelProperty(value = "点价有效期")
    private Date pricingValidTime;

    /**
     * 备注
     */
    @ApiModelProperty(value="备注")
    private String remark;

    /**
     *利息成本
     */
    @ApiModelProperty(value = "利息成本")
    private BigDecimal interestCost;
}
