package com.basiscotton.manager.traderecord.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Title: AdjustTargetContractVo.java
 * @Description: 调整目标合同查询结果Vo
 * <AUTHOR> Name]
 * @date [Current Date]
 * @version V1.0
 */
@Data
public class AdjustTargetContractVo implements Serializable {

	@ApiModelProperty(value = "交易序列类型: 1-协商 2-竞卖 3-基差 4-现货挂牌")
	private String transSeqType;

	@ApiModelProperty(value = "卖方交易商代码")
	private String sellerTraderCode;

	@ApiModelProperty(value = "卖方交易商名称")
	private String sellerTraderName;

	@ApiModelProperty(value = "买方交易商代码")
	private String buyerTraderCode;

	@ApiModelProperty(value = "买方交易商名称")
	private String buyerTraderName;

	@ApiModelProperty(value = "成交时间")
	private String tradeTime;

	@ApiModelProperty(value = "批号")
	private String batchNo;

	@ApiModelProperty(value = "仓单类型: 仓单/非仓单")
	private String warehouseReceiptType;

	@ApiModelProperty(value = "产地")
	private String place;

	//批号
	@ApiModelProperty(value = "批号列表")
	private String batchNumber;

	//批号集合
	@ApiModelProperty(value = "批号List")
	private List<String> batchNumberList;


	@ApiModelProperty(value = "开始成交日期")
	private String beginTradeTime;


	@ApiModelProperty(value = "结束成交日期")
	private String endTradeTime;


	@ApiModelProperty(value = "客户编号")
	private String customCode;


	@ApiModelProperty(value = "商品码")
	private String stockCode;

	@ApiModelProperty(value = "合同号")
	private String contractNo;
	@ApiModelProperty(value = "交易重量")
	private String tradeWeight;

	@ApiModelProperty(value = "交易价格")
	private String tradeBasisPrice;


	@ApiModelProperty(value = "报价时间")
	private String quoteTime;

	@ApiModelProperty(value = "报价重量")
	private String quoteWeight;

	@ApiModelProperty(value = "报价价格")
	private String quotePrice;

	@ApiModelProperty(value = "报价状态")
	private String quoteStatus;

	@ApiModelProperty(value = "合计总重量")
	private String totalTradeWeight;






}