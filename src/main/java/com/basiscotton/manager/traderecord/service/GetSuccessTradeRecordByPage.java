package com.basiscotton.manager.traderecord.service;

import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.mappers.AdjustTargetContractMapper;
import com.basiscotton.manager.stock.vo.BasisWhsReceiptParamVo;
import com.basiscotton.manager.stock.vo.BasisWhsReceiptVo;
import com.basiscotton.manager.traderecord.vo.AdjustTargetContractVo;
import org.apache.commons.lang3.StringUtils;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.params.PageParameter;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.rep.support.page.PipPagination;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: GetTradeRecordByPage.java
 * @Description: 成交记录
 * @date 2025/9/3 17:46
 */
@Service("tradeRecord.GetSuccessTradeRecordByPage.1")
@ApiRequestObject(value = "成交记录", name = "GetSuccessTradeRecordByPage", groups = {"市场端-成交记录"}, params = {
        @ApiParamMeta(key = "pageParameter", desc = "分页参数", type = PageParameter.class),
        @ApiParamMeta(key = "searchVo", desc = "查询vo", type = BasisWhsReceiptParamVo.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "pagination", desc = "查询分页列表", type = BasisWhsReceiptVo.class, pagination = true),
})
public class GetSuccessTradeRecordByPage implements IBusinessService {

    @Resource
    AdjustTargetContractMapper adjustTargetContractMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {

        PageParameter pageParameter = context.getValueObject(PageParameter.class, "pageParameter");
        AdjustTargetContractVo paramVo = context.getValueObject(AdjustTargetContractVo.class, "searchVo");
        if (StringUtils.isNotEmpty(paramVo.getBatchNumber())) {
            paramVo.setBatchNumberList(Arrays.asList(paramVo.getBatchNumber().split(",")));
        }

        PipPagination<AdjustTargetContractVo> pipPagination = new PipPagination<>(pageParameter);
        pipPagination = adjustTargetContractMapper.selectTrdBasisDeliveryTargets(pipPagination, paramVo);
        String   total= adjustTargetContractMapper.selectTotalTradeWeight(paramVo);
        if(pipPagination!=null &&pipPagination.getRecords().size()>0&& StringUtils.isNotEmpty(total)) {
            pipPagination.getRecords().forEach(record -> { record.setTotalTradeWeight(total); });
        }
        //创建响应实体
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("pagination", pipPagination);

    }

    private boolean isNotMarketCustmerType(ServiceHandlerContext context) {
        String currentUserCustomType = context.getCurrentUserCustomType();
        return Objects.isNull(currentUserCustomType) || !Objects.equals(currentUserCustomType, BasisCottonConstants.custom_type_9);
    }
}
