package com.basiscotton.manager.traderecord.service;

import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.mappers.AdjustTargetContractMapper;
import com.basiscotton.manager.stock.vo.BasisWhsReceiptParamVo;
import com.basiscotton.manager.stock.vo.BasisWhsReceiptVo;
import com.basiscotton.manager.traderecord.vo.AdjustTargetContractVo;
import org.apache.commons.lang3.StringUtils;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.params.PageParameter;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.rep.support.page.PipPagination;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: GetTradeRecordByPage.java
 * @Description: 交易成交结果
 * @date 2025/9/3 17:46
 */
@Service("tradeRecord.GetTradeRecordByPage.1")
@ApiRequestObject(value = "交易成交结果", name = "GetTradeRecordByPage", groups = {"市场端-交易成交结果"}, params = {
        @ApiParamMeta(key = "pageParameter", desc = "分页参数", type = PageParameter.class),
        @ApiParamMeta(key = "searchVo", desc = "查询vo", type = BasisWhsReceiptParamVo.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "pagination", desc = "查询分页列表", type = BasisWhsReceiptVo.class, pagination = true),
})
public class GetTradeRecordByPage implements IBusinessService {

    @Resource
    AdjustTargetContractMapper adjustTargetContractMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {
        AdjustTargetContractVo paramVo = context.getValueObject(AdjustTargetContractVo.class, "searchVo");
        // 非市场客户类型，批号不能为空
        if (isNotMarketCustmerType(context) && (Objects.isNull(paramVo) || StringUtils.isEmpty(paramVo.getBatchNumber()))) {
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "批号不能为空");
        }

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        String customCode = context.getCurrentUserCustomCode();
        PageParameter pageParameter = context.getValueObject(PageParameter.class, "pageParameter");
        AdjustTargetContractVo paramVo = context.getValueObject(AdjustTargetContractVo.class, "searchVo");
        if (StringUtils.isNotEmpty(paramVo.getBatchNumber())) {
            paramVo.setBatchNumberList(Arrays.asList(paramVo.getBatchNumber().split(",")));
        }
        paramVo.setCustomCode(customCode);

        PipPagination<AdjustTargetContractVo> pipPagination = new PipPagination<>(pageParameter);
        pipPagination = adjustTargetContractMapper.selectAdjustTargetContracts(pipPagination, paramVo);
        //创建响应实体
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("pagination", pipPagination);

    }

    private boolean isNotMarketCustmerType(ServiceHandlerContext context) {
        String currentUserCustomType = context.getCurrentUserCustomType();
        return Objects.isNull(currentUserCustomType) || !Objects.equals(currentUserCustomType, BasisCottonConstants.custom_type_9);
    }
}
