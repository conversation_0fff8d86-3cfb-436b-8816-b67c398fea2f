package com.basiscotton.manager.order.service;

import com.basiscotton.base.mappers.BasisOrderMapper;
import com.basiscotton.manager.order.vo.BasisOrderResVo;
import com.basiscotton.manager.order.vo.BasisOrderParamVo;
import org.harry.dandelion.framework.core.params.PageParameter;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.rep.support.page.PipPagination;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: getBasisOrderByPage.java
 * @Description: 获取基差订单列表
 * @date 2025/6/1 11:24
 */
@Service("basisOrder.getBasisOrderByPage.1")
@ApiRequestObject(value = "获取基差订单列表", name = "getBasisOrderByPage",  groups= {"管理端-基差订单类"},params= {
        @ApiParamMeta(key = "pageParameter",  desc = "分页参数", type = PageParameter.class),
        @ApiParamMeta(key = "basisOrderParamVo", desc = "查询参数", type = BasisOrderParamVo.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "pagination", desc = "查询分页列表", type = BasisOrderResVo.class),
})
public class getBasisOrderByPage implements IBusinessService {

    @Resource
     private BasisOrderMapper basisOrderMapper;
    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        PageParameter pageParameter = context.getValueObject(PageParameter.class, "pageParameter");
        BasisOrderParamVo paramVo = context.getValueObject(BasisOrderParamVo.class, "basisOrderParamVo");
        // 组装查询参数
        PipPagination<BasisOrderResVo> pagination = new PipPagination<BasisOrderResVo>(pageParameter);
        pagination = basisOrderMapper.selectBasisOrderByPage(pagination,paramVo);
        //创建响应实体
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("pagination", pagination);
    }
}
