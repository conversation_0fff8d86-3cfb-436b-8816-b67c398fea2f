package com.basiscotton.manager.order.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: BasisOrderVo.java
 * @Description: 订单查询类
 * @date 2025/6/1 10:31
 */
@Data
public class BasisOrderParamVo implements Serializable {

      /**
       * 商品编码
       */
      @ApiModelProperty(value="商品编码")
      private String stockCode;


      /**
       * 卖方交易商代码
       */
      @ApiModelProperty(value="卖方交易商代码")
      private String sellerCustomCode;

      /**
       * 卖方交易商名称
       */
      @ApiModelProperty(value="卖方交易商名称")
      private String sellerCustomName;

      /**
       * 买方交易商代码
       */
      @ApiModelProperty(value="买方交易商代码")
      private String buyerCustomCode;

      /**
       * 买方交易商名称
       */
      @ApiModelProperty(value="买方交易商名称")
      private String buyerCustomName;


      /**
       *成交时间
       */
      @ApiModelProperty(value = "成交时间")
      private String startTradeDate;
      /**
       *成交时间
       */
      @ApiModelProperty(value = "成交结束时间")
      private String endTradeDate;
}
