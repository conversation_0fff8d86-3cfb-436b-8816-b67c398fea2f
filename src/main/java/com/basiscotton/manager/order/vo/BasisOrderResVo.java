package com.basiscotton.manager.order.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.sinosoft.dandelion.system.client.params.DataItem;
import com.sinosoft.dandelion.system.client.serializer.DataItemCodeDeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class BasisOrderResVo implements Serializable {

	@ApiModelProperty(value="主键")
	@JSONField(serializeUsing = ToStringSerializer.class)
	private Long id;

	@ApiModelProperty(value="合同id")
	@JSONField(serializeUsing = ToStringSerializer.class)
	private Long contractId;

	@ApiModelProperty(value="商品id")
	@JSONField(serializeUsing = ToStringSerializer.class)
	private Long stockId;

	@ApiModelProperty(value="商品编码")
	private String stockCode;

	@ApiModelProperty(value="商品基础信息ID")
	@JSONField(serializeUsing = ToStringSerializer.class)
	private Long stockBaseInfoId;

	@ApiModelProperty(value="委托表id")
	@JSONField(serializeUsing = ToStringSerializer.class)
	private Long quoteId;

	@ApiModelProperty(value="卖方交易商代码")
	private String sellerCustomCode;

	@ApiModelProperty(value="卖方交易商名称")
	private String sellerCustomName;

	@ApiModelProperty(value="买方交易商代码")
	private String buyerCustomCode;

	@ApiModelProperty(value="买方交易商名称")
	private String buyerCustomName;

	@ApiModelProperty(value = "成交价格")
	private BigDecimal tradePrice;

	@ApiModelProperty(value = "成交数量")
	private BigDecimal tradeQuantity;

	@ApiModelProperty(value = "成交时间")
	private Date tradeDate;

	@ApiModelProperty(value = "卖方保证金标准")
	private BigDecimal sellerMarginStandard;

	@ApiModelProperty(value = "卖方保证金金额")
	private BigDecimal sellerMarginAmount;

	@ApiModelProperty(value = "卖方手续费金额")
	private BigDecimal sellerFeeAmount;

	@ApiModelProperty(value = "卖方手续费标准")
	private BigDecimal sellerFeeStandard;

	@ApiModelProperty(value = "卖方保证金收取方式: 1-集团账户 2-电商")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem basisSellerMarginType;

	@ApiModelProperty(value = "卖方手续费收取方式: 1-集团账户 2-电商")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem basisSellerFeeType;

	@ApiModelProperty(value = "买方保证金标准")
	private BigDecimal buyerMarginStandard;

	@ApiModelProperty(value = "买方保证金金额")
	private BigDecimal buyerMarginAmount;

	@ApiModelProperty(value = "买方手续费金额")
	private BigDecimal buyerFeeAmount;

	@ApiModelProperty(value = "买方手续费标准")
	private BigDecimal buyerFeeStandard;

	@ApiModelProperty(value = "买方保证金收取方式: 1-集团账户 2-电商")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem basisBuyerMarginType;

	@ApiModelProperty(value = "买方手续费收取方式: 1-集团账户 2-电商")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem basisBuyerFeeType;

	@ApiModelProperty(value = "报价方式: 1-一口价 2-锁基差 3-即时点价")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem quoteType;

	@ApiModelProperty(value = "点价方: 1-买方 2-卖方")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem pricingParty;

	@ApiModelProperty(value="期货合约")
	private String futureCode;

	@ApiModelProperty(value = "一口价")
	private BigDecimal fixedPrice;

	@ApiModelProperty(value = "基差价格")
	private String basisPrice;

	@ApiModelProperty(value = "基差价格范围")
	private String basisPriceRange;

	@ApiModelProperty(value = "点价价格")
	private BigDecimal pricingPrice;

	@ApiModelProperty(value = "点价有效期")
	private Date pricingValidTime;

	@ApiModelProperty(value = "资金监管状态：1、正常 2、三方监管-籽棉贷款 3、三方监管-皮棉调销 4、三方监管-融资监管状态 5、质押监管 6、质押及三方监管")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem whsSupervisionStatus;

	@ApiModelProperty(value = "监管机构代码")
	private String superviseCode;

	@ApiModelProperty(value = "监管机构名称")
	private String superviseName;

	@ApiModelProperty(value = "金融渠道：1正常、2购销、3金益棉、4棉贸通、5E棉通、6棉E通、7民生银行、8中国银行、9农商银行")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem whsPledgeChannel;

}