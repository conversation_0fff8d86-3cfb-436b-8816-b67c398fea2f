package com.basiscotton.manager.hall.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Title: BuyerStockReqSortVo.java
 * @Description: 买方点价-排序vo
 * <AUTHOR>
 * @date 2025/6/13
 * @version V1.0
 */
@Data
public class BuyerStockReqSortVo implements Serializable {

	@ApiModelProperty(value = "排序字段名称")
	private String sortName;

	@ApiModelProperty(value = "排序类型asc正序、desc倒序")
	private String sortType;

	@ApiModelProperty(value = "排序顺序")
	private Integer sortIndex;


}

