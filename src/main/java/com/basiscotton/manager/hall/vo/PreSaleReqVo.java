package com.basiscotton.manager.hall.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Title: PreSaleReqVo.java
 * @Description: 全部预售商品列表查询vo
 * <AUTHOR>
 * @date 2025/5/24
 * @version V1.0
 */
@Data
public class PreSaleReqVo extends InspectDataReqVo {

	//默认查询项
	private String resourceAuditStatus;
	private List<String> tradeStatus;
	private String preStockSource;
	private String traderCode;

	@ApiModelProperty(value = "产地")
	private String intactPlace;

	@ApiModelProperty(value = "产地")
	private List<String> intactPlaceList;

	//页面查询项
	@ApiModelProperty(value = "交货日期-开始")
	private String startDeliveryTime;
	@ApiModelProperty(value = "交货日期-结束")
	private String endDeliveryTime;


}

