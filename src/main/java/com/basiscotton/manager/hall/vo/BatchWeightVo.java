package com.basiscotton.manager.hall.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class BatchWeightVo implements Serializable {

	/**
	 *批检验重量ID
	 */
	@TableId("ID")
	@ApiModelProperty(value = "批检验重量ID")
	@JSONField(serializeUsing = ToStringSerializer.class)
	private Long id;
	
	/**
	 *加工批号
	 */
	@TableField("BATCH_CODE")
	@ApiModelProperty(value = "加工批号")
	private String batchCode;
	
	/**
	 *检验批号（组批号） 
	 */
	@TableField("INSPECT_CODE")
	@ApiModelProperty(value = "检验批号（组批号） ")
	private String inspectCode;
	
	/**
	 *产地             
	 */
	@TableField("PLACE")
	@ApiModelProperty(value = "产地")
	private String place;
	
	/**
	 *加工单位   
	 */
	@TableField("PROCESS_COMPANY")
	@ApiModelProperty(value = "加工单位   ")
	private String processCompany;
	
	/**
	 *检验单位   
	 */
	@TableField("INSPECT_COMPANY")
	@ApiModelProperty(value = "检验单位   ")
	private String inspectCompany;
	
	/**
	 *仓储库            
	 */
	@TableField("REPOSITORY")
	@ApiModelProperty(value = "仓储库")
	private String repository;
	
	/**
	 *产品名称  
	 */
	@TableField("PRODUCT_NAME")
	@ApiModelProperty(value = "产品名称  ")
	private String productName;

	/**
	 *执行标准 
	 */
	@TableField("EXECUTE_STANDARD")
	@ApiModelProperty(value = "执行标准")
	private String executeStandard;
	
	/**
	 *检验日期
	 */
	@TableField("INSPECT_DATE")
	@ApiModelProperty(value = "检验日期")
	private Date inspectDate;
	
	/**
	 *包数（件）
	 */
	@TableField("PACKET_NUM")
	@ApiModelProperty(value = "包数（件）")
	private Integer packetNum;
	
	/**
	 *毛重（t）
	 */
	@TableField("GROSS_WEIGHT")
	@ApiModelProperty(value = "毛重（t）")
	private BigDecimal grossWeight;
	
	/**
	 *皮重（kg）
	 */
	@TableField("TARE_WEIGHT")
	@ApiModelProperty(value = "皮重（kg）")
	private BigDecimal tareWeight;
	
	/**
	 *净重（t）
	 */
	@TableField("NET_WEIGHT")
	@ApiModelProperty(value = "净重（t）")
	private BigDecimal netWeight;
	
	/**
	 *公定重量（t）
	 */
	@TableField("PUB_WEIGHT")
	@ApiModelProperty(value = "公定重量（t）")
	private BigDecimal pubWeight;
	
	/**
	 *回潮率（%）
	 */
	@TableField("MOISTURE_RATE")
	@ApiModelProperty(value = "回潮率（%）")
	private BigDecimal moistureRate;
	
	/**
	 *含杂率（%）
	 */
	@TableField("IMPURITY_RATE")
	@ApiModelProperty(value = "含杂率（%）")
	private BigDecimal impurityRate;
	
	/**
	 *备注
	 */
	@TableField("REMARK")
	@ApiModelProperty(value = "备注")
	private String remark;
	
	/**
	 *新字段，意义不明
	 */
	@TableField("NEW_COMMENT")
	@ApiModelProperty(value = "新字段，意义不明")
	private String newComment;
	
	/**
	 *上传日期(数据上传到中纤局的日期)
	 */
	@TableField("UP_TIME")
	@ApiModelProperty(value = "上传日期(数据上传到中纤局的日期)")
	private Date upTime;
	
	/**
	 *检验单位代码
	 */
	@TableField("INSPECT_COMPANY_CODE")
	@ApiModelProperty(value = "检验单位代码")
	private String inspectCompanyCode;
	
	/**
	 *生产年度
	 */
	@TableField("WORK_YEAR")
	@ApiModelProperty(value = "生产年度")
	private Integer workYear;
	
	/**
	 *加工厂代码
	 */
	@TableField("ENTERPRISE_CODE")
	@ApiModelProperty(value = "加工厂代码")
	private String enterpriseCode;

	/**
	 * 中纤局同步到新疆的日期 (中纤局同步到新疆的日期)
	 */
	@TableField("SYNC_TIME")
	@ApiModelProperty(value = "中纤局同步到新疆的日期，既同步智棉的创建时间")
	private Date syncTime;


	/**
	 *创建时间(中纤局同步到新疆的日期)
	 */
	@TableField("CREATE_TIME")
	@ApiModelProperty(value = "创建时间(中纤局同步到新疆的日期)")
	private Date createTime;
	
	/**
	 *2:二次组批,1：首次组批
	 */
	@TableField("TYPE")
	@ApiModelProperty(value = "2:二次组批,1：首次组批")
	private Integer type;
	
	/**
	 *更新时间
	 */
	@TableField("UPDATE_TIME")
	@ApiModelProperty(value = "更新时间")
	private Date updateTime;
	
	/**
	 *创建人
	 */
	@TableField("CREATE_BY")
	@ApiModelProperty(value = "创建人")
	private String createBy;
	
	/**
	 *更新人
	 */
	@TableField("UPDATE_BY")
	@ApiModelProperty(value = "更新人")
	private String updateBy;
	
	/**
	 *删除标志:1:正常2：删除
	 */
	@TableField("DEL_FLAG")
	@ApiModelProperty(value = "删除标志:1:正常2：删除")
	private Integer delFlag;
	

}