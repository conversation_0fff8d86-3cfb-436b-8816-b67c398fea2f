package com.basiscotton.manager.hall.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Title: BasisPreQuoteVo.java
 * @Description: 报价vo
 * <AUTHOR>
 * @date 2025/5/29
 * @version V1.0
 */
@Data
public class BasisPreQuoteVo implements Serializable {

	@ApiModelProperty(value = "商品id")
	private String stockId;

	@ApiModelProperty(value = "价格")
	private String quotePrice;

	@ApiModelProperty(value = "登录账户")
	private String userName;

	@ApiModelProperty(value = "客户代码")
	private String customCode;

	@ApiModelProperty(value = "客户名称")
	private String customName;

}

