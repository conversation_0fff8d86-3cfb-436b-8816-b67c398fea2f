package com.basiscotton.manager.hall.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class NowStockStatisticsVo implements Serializable {

	@ApiModelProperty(value="挂单量")
	private String stockStatistics;

	@ApiModelProperty(value="报价量")
	private String quoteStatistics;

	@ApiModelProperty(value="成交量")
	private String tradeStatistics;


}

