package com.basiscotton.manager.hall.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 公检基本信息
 */
@Data
public class QuanlityBaseInfoVo extends BatchInspectVo implements Serializable {

	@ApiModelProperty(value = "产品名称  ")
	private String productName;

	@ApiModelProperty(value = "批次件数  ")
	private String quantity;

	@ApiModelProperty(value = "检验包数（件）")
	private Integer packetNum;

	@ApiModelProperty(value = "毛重（t）")
	private BigDecimal grossWeight;

	@ApiModelProperty(value = "皮重（kg）")
	private BigDecimal tareWeight;

	@ApiModelProperty(value = "公定重量（t）")
	private BigDecimal pubWeight;

	@ApiModelProperty(value = "净重（t）")
	private BigDecimal netWeight;

	@ApiModelProperty(value = "回潮率（%）")
	private BigDecimal moistureRate;

	@ApiModelProperty(value = "含杂率（%）")
	private BigDecimal impurityRate;

}
