package com.basiscotton.manager.hall.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.jdbc.SQL;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Data
@EqualsAndHashCode(callSuper = true)
public class SpotStockReqVO extends InspectDataReqVo {

    @ApiModelProperty(value = "产地")
    private List<String> intactPlaceList;

    @ApiModelProperty(value = "棉花年度")
    private String productYear;

    @ApiModelProperty(value = "采摘方式")
    private String whsPickMode;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @Override
    public SQL buildSpotStockSql(SQL sql) {
        sql = super.buildSpotStockSql(sql);

        if (Objects.nonNull(getIntactPlaceList()) && !getIntactPlaceList().isEmpty()) {
            String field = getIntactPlaceList().stream()
                    .map(place -> "'%" + place + "%'")
                    .map(place -> "baseInfo.intact_place like " + place)
                    .collect(Collectors.joining(" or "));
            sql.WHERE("( " + field + " )");
        }
        if (Objects.nonNull(getProductYear()) && !getProductYear().isEmpty()) {
            sql.WHERE("stock.product_year = " + getProductYear());
        }
        if (Objects.nonNull(getWhsPickMode()) && !getWhsPickMode().isEmpty()) {
            sql.WHERE("stock.whs_pick_mode = " + getWhsPickMode());
        }
        if (Objects.nonNull(getWarehouseName()) && !getWarehouseName().isEmpty()) {
            sql.WHERE("stock.warehouse_name like concat('%', " + getWarehouseName() + ", '%')");
        }
        return sql;
    }
}
