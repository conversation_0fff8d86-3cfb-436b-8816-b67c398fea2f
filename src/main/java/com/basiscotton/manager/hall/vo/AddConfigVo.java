package com.basiscotton.manager.hall.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class AddConfigVo implements Serializable {

	@ApiModelProperty(value = "id")
	private String id;

	@ApiModelProperty(value="模板类型")
	private String templateType;

	@ApiModelProperty(value="模板名称")
	private String templateName;

	@ApiModelProperty(value="模板内容")
	private String templateValue;


}

