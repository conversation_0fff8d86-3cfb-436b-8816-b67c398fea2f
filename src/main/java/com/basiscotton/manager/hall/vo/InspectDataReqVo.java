package com.basiscotton.manager.hall.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.ibatis.jdbc.SQL;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.basiscotton.manager.constants.InspectDataConstants.COLOR_GRADE_FIELD_MAP;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: InspectDataReqVo.java
 * @Description: 公检信息查询vo
 * @date 2025/5/24
 */
@Data
public class InspectDataReqVo implements Serializable {

    //主体颜色级
    private List<String> colorGrade;

    //是否开启颜色比率占比
    private boolean isColorRate = false;
    //开始颜色级
    private String beginColor;
    //开始颜色比率
    private BigDecimal beginColorRate;

    //且
    private boolean isJust = false;
    //结束颜色级
    private String endColor;
    //结束颜色比率
    private Double endColorRate;

    private List<String> endColorList;

    private List<String> beginColorList;

    @ApiModelProperty(value = "长度-最小")
    private BigDecimal beginLengthLevel;

    @ApiModelProperty(value = "长度-最大")
    private BigDecimal endLengthLevel;

    @ApiModelProperty(value = "长度-最小值")
    private BigDecimal minLength;

    @ApiModelProperty(value = "强力-最小")
    private BigDecimal beginBreak;

    @ApiModelProperty(value = "强力-最大")
    private BigDecimal endBreak;

    @ApiModelProperty(value = "强力-最小值")
    private BigDecimal minBreak;

    @ApiModelProperty(value = "马值-最小")
    private BigDecimal beginMKL;

    @ApiModelProperty(value = "马值-最大")
    private BigDecimal endMKL;

    @ApiModelProperty(value = "马值-最小值")
    private BigDecimal minMKL;

    @ApiModelProperty(value = "长整-最小")
    private BigDecimal beginLenUniformity;

    @ApiModelProperty(value = "长整-最大")
    private BigDecimal endLenUniformity;

    @ApiModelProperty(value = "含杂-最小")
    private BigDecimal beginImpurityRate;

    @ApiModelProperty(value = "含杂-最大")
    private BigDecimal endImpurityRate;

    @ApiModelProperty(value = "回潮-最小")
    private BigDecimal beginMoistureContent;

    @ApiModelProperty(value = "回潮-最大")
    private BigDecimal endMoistureContent;

    @ApiModelProperty(value = "Rd%-最小值")
    private BigDecimal minRd;

    @ApiModelProperty(value = "Rd%-最大值")
    private BigDecimal maxRd;

    @ApiModelProperty(value = "+b-最小值")
    private BigDecimal minB;

    @ApiModelProperty(value = "+b-最大值")
    private BigDecimal maxB;

    public SQL buildSpotStockSql(SQL sql) {
        // 主体颜色级
        if (Objects.nonNull(getColorGrade()) && !getColorGrade().isEmpty()) {
            String field = getColorGrade().stream()
                    .map(color -> "'" + color + "'")
                    .collect(Collectors.joining(","));
            sql.WHERE(" baseInfo.color_grade in ( " + field + ")");
        }
        if (isColorRate()) {
            String field = COLOR_GRADE_FIELD_MAP.get(this.beginColor);
            sql.WHERE(" baseInfo.color_grade = " + field + "and " + field + " >=" + beginColorRate);
        }
        if (isJust()) {
            String field = COLOR_GRADE_FIELD_MAP.get(this.endColor);
            sql.WHERE(" baseInfo.color_grade = " + field + "and " + field + " <=" + endColorRate);
        }
        // 最小长度
        if (Objects.nonNull(getBeginLengthLevel())) {
            sql.WHERE(" baseInfo.avg_length >= " + getBeginLengthLevel());
        }
        // 最大长度
        if (Objects.nonNull(getEndLengthLevel())) {
            sql.WHERE(" baseInfo.avg_length <= " + getEndLengthLevel());
        }
        // 最小长度
        if (Objects.nonNull(getMinLength())) {
            sql.WHERE(" baseInfo.avg_length >= " + getMinLength());
        }
        // 范围最小强力
        if (Objects.nonNull(getBeginBreak())) {
            sql.WHERE(" baseInfo.break_value >= " + getBeginBreak());
        }
        // 范围最大强力
        if (Objects.nonNull(getEndBreak())) {
            sql.WHERE(" baseInfo.break_value <= " + getEndBreak());
        }
        // 最小强力
        if (Objects.nonNull(getMinBreak())) {
            sql.WHERE(" baseInfo.break_value >= " + getMinBreak());
        }
        // 范围最小马值
        if (Objects.nonNull(getBeginMKL())) {
            sql.WHERE(" baseInfo.main_mkl >= " + getBeginMKL());
        }
        // 范围最大马值
        if (Objects.nonNull(getEndMKL())) {
            sql.WHERE(" baseInfo.main_mkl <= " + getEndMKL());
        }
        // 最小马值
        if (Objects.nonNull(getMinMKL())) {
            sql.WHERE(" baseInfo.main_mkl >= " + getMinMKL());
        }
        // 范围最小长整
        if (Objects.nonNull(getBeginLenUniformity())) {
            sql.WHERE(" baseInfo.uniformity_average_value >= " + getBeginLenUniformity());
        }
        // 范围最大长整
        if (Objects.nonNull(getEndLenUniformity())) {
            sql.WHERE(" baseInfo.uniformity_average_value <= " + getEndLenUniformity());
        }
        // 含杂最小值
        if (Objects.nonNull(getBeginImpurityRate())) {
            sql.WHERE(" baseInfo.impurity_rate >= " + getBeginImpurityRate());
        }
        // 含杂最大值
        if (Objects.nonNull(getEndImpurityRate())) {
            sql.WHERE(" baseInfo.impurity_rate <= " + getEndImpurityRate());
        }
        // 回潮最小值
        if (Objects.nonNull(getBeginMoistureContent())) {
            sql.WHERE(" baseInfo.moisture_rate >= " + getBeginMoistureContent());
        }
        // 回潮最大值
        if (Objects.nonNull(getEndMoistureContent())) {
            sql.WHERE(" baseInfo.moisture_rate <= " + getEndMoistureContent());
        }
        // Rd%最小值
        if (Objects.nonNull(getMinRd())) {
            sql.WHERE(" baseInfo.avg_rd >= " + getMinRd());
        }
        // Rd%最大值
        if (Objects.nonNull(getMaxRd())) {
            sql.WHERE(" baseInfo.avg_rd <= " + getMaxRd());
        }
        // +b最小值
        if (Objects.nonNull(getMinB())) {
            sql.WHERE(" baseInfo.avg_plus_b >= " + getMinB());
        }
        // +b最大值
        if (Objects.nonNull(getMaxB())) {
            sql.WHERE(" baseInfo.avg_plus_b <= " + getMaxB());
        }

        return sql;
    }

}

