package com.basiscotton.manager.hall.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Title: AllStockReqVo.java
 * @Description: 全部资源列表查询vo
 * <AUTHOR>
 * @date 2025/5/24
 * @version V1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BuyerStockReqVo extends InspectDataReqVo {

	//默认查询项
	private String resourceAuditStatus;
	private String quoteType;
	private String pricingParty;
	private List<String> tradeStatus;
	private String traderCode;
	private String tradeMode;

    @ApiModelProperty(value = "商品码")
    private String stockCode;

	//页面查询项
	@ApiModelProperty(value = "产地")
	private String intactPlace;

	@ApiModelProperty(value = "产地")
	private List<String> intactPlaceList;

	@ApiModelProperty(value = "棉花年度")
	private String productYear;

	@ApiModelProperty(value = "采摘方式")
	private String whsPickMode;

	@ApiModelProperty(value = "加工单位")
	private String factoryName;

	@ApiModelProperty(value = "仓库名称")
	private String storageWhsName;

	@ApiModelProperty(value = "批号")
	private String batchNo;

	@ApiModelProperty(value = "关键字")
	private String keyWordsStr;

	@ApiModelProperty(value = "优选查询数据")
	private Integer queryNum;

	@ApiModelProperty(value = "仓库名称")
	private String warehouseNameStr;

	@ApiModelProperty(value = "仓库名称")
	private List<String> warehouseNameList;

	@ApiModelProperty(value = "卖方名称")
	private String traderNameStr;

	@ApiModelProperty(value = "卖方名称集合")
	private List<String> traderNameList;

	@ApiModelProperty(value = "商品码排序")
	private String stockCodeSort;

	@ApiModelProperty(value = "排序vo")
	private List<BuyerStockReqSortVo> reqSortVoList;
	private List<String> sortList;
}

