package com.basiscotton.manager.hall.core.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.*;
import com.basiscotton.base.mappers.*;
import com.basiscotton.common.apiservice.IBasisTradeSettlementService;
import com.basiscotton.manager.hall.vo.BasisQuoteVo;
import com.sinosoft.cnce.sc.base.enumeration.FinancialEntityAccountTypeEnum;
import com.sinosoft.cnce.sc.base.vo.SCResponseVO;
import com.sinosoft.cnce.sc.base.vo.query.CustomerFundsVO;
import com.sinosoft.dandelion.system.client.params.DataItem;
import org.harry.dandelion.framework.common.utils.DateUtils;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.ServiceManager;
import org.harry.dandelion.framework.id.core.service.BizIdGenerator;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @Title: IBasisQuoteService.java
 * @Description: 基差报价服务
 * <AUTHOR>
 * @date 2025/5/24
 * @version V1.0
 */
public class BasisPreQuoteService implements IBasisQuoteService {

	@Override
	public void createQuote(ServiceHandlerContext context, BasisQuoteVo quoteVo) {
		BasisPreStockMapper basisPreStockMapper = ServiceManager.getBean(BasisPreStockMapper.class);
		BasisPreStockBaseInfoMapper basisPreStockBaseInfoMapper = ServiceManager.getBean(BasisPreStockBaseInfoMapper.class);
		BasisQuoteMapper basisQuoteMapper = ServiceManager.getBean(BasisQuoteMapper.class);
		BasisOrderMapper basisOrderMapper = ServiceManager.getBean(BasisOrderMapper.class);
		String qouteCode = context.getCurrentUserCustomCode();
		Date startDate = DateUtils.getDate();
		BasisQuoteEntity agentEntity = new BasisQuoteEntity();
		//验证报价
		BasisPreStockEntity stockEntity = basisPreStockMapper.selectById(quoteVo.getStockId());
		this.verifyQuote(qouteCode,stockEntity);
		//创建委托信息
		BasisPreStockBaseInfoEntity baseStock = basisPreStockBaseInfoMapper.selectById(stockEntity.getStockBaseInfoId());
		Long agentId = BizIdGenerator.getInstance().generateBizId();
		agentEntity.setId(agentId);
		agentEntity = this.buildAgent(agentEntity, quoteVo, stockEntity, baseStock);
		agentEntity= this.freezeBuyerCapital(agentEntity, stockEntity, quoteVo);
		basisQuoteMapper.insert(agentEntity);
		//创建成交信息
		BasisOrderEntity orderEntity = this.buildOrder(quoteVo, agentEntity,stockEntity, baseStock, agentId);
		basisOrderMapper.insert(orderEntity);
		//修改商品状态
		LambdaUpdateWrapper<BasisPreStockEntity> stockWrapper = new LambdaUpdateWrapper<BasisPreStockEntity>();
		stockWrapper.eq(BasisPreStockEntity::getId, stockEntity.getId());
		stockWrapper.set(BasisPreStockEntity::getTradeStatus, new DataItem(BasisCottonConstants.trade_status_2,""));
		basisPreStockMapper.update(null, stockWrapper);
		//修改委托标配数据
		LambdaUpdateWrapper<BasisQuoteEntity> agentWrapper = new LambdaUpdateWrapper<BasisQuoteEntity>();
		agentWrapper.eq(BasisQuoteEntity::getId, agentId);
		agentWrapper.set(BasisQuoteEntity::getQuoteStatus, new DataItem(BasisCottonConstants.quote_status_2,""));
		basisQuoteMapper.update(null, agentWrapper);
	}

	private void verifyQuote(String qouteCode,BasisPreStockEntity stockEntity) {
		if(stockEntity == null){
			throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "商品不存在");
		}
		this.verifyStockStatus(qouteCode,stockEntity);
	}

	private void verifyStockStatus(String qouteCode,BasisPreStockEntity stockEntity){
		DataItem auditStatus = stockEntity.getResourceAuditStatus();
		DataItem tradeStatus = stockEntity.getTradeStatus();
		String traderCode = stockEntity.getTraderCode();
		if(qouteCode.equals(traderCode)){
			throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "商品所属货权人是自己，无法出价");
		}

		if(auditStatus == null || tradeStatus == null){
			throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "商品状态不正确，请刷新重试");
		}

		boolean messageStatus = true;
		if(BasisCottonConstants.resource_audit_status_3.equals(auditStatus.getCode())){
			if(BasisCottonConstants.trade_status_1.equals(tradeStatus.getCode()) || BasisCottonConstants.trade_status_2.equals(tradeStatus.getCode())){
				messageStatus = false;
			}
		}
		if(messageStatus){
			throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "商品状态不正确，请刷新重试");
		}
	}

	private BasisQuoteEntity freezeBuyerCapital(BasisQuoteEntity agentEntity,  BasisPreStockEntity stock,BasisQuoteVo quoteVo){
		IBasisTradeSettlementService basisTradeSettlementService = ServiceManager.getBean(IBasisTradeSettlementService.class);
		BasisMarketSettingMapper basisMarketSettingMapper = ServiceManager.getBean(BasisMarketSettingMapper.class);
		LambdaQueryWrapper<MarketSettingEntity> queryWrapper = new LambdaQueryWrapper<MarketSettingEntity>();
		queryWrapper.eq(MarketSettingEntity::getSettingStatus, BasisCottonConstants.COMMON_YES);
		queryWrapper.eq(MarketSettingEntity::getBasisSettingType, BasisCottonConstants.basis_setting_type_2);
		MarketSettingEntity commonSetting = basisMarketSettingMapper.selectOne(queryWrapper);

		BasisTraderSettingMapper traderSettingMapper = ServiceManager.getBean(BasisTraderSettingMapper.class);
		LambdaQueryWrapper<BasisTraderSettingEntity> traderSettingQuery = new LambdaQueryWrapper<BasisTraderSettingEntity>();
		traderSettingQuery.eq(BasisTraderSettingEntity::getTraderCode, quoteVo.getCustomCode());
		traderSettingQuery.eq(BasisTraderSettingEntity::getBasisSettingType, BasisCottonConstants.basis_setting_type_2);
		BasisTraderSettingEntity traderSetting = traderSettingMapper.selectOne(traderSettingQuery);
		//查询交易商资金设置
		BigDecimal buyerMarginStandard = new BigDecimal("0");
		BigDecimal buyerTradeFeeStandard = new BigDecimal("0");
		BigDecimal buyerDeliveryFeeStandard = new BigDecimal("0");
		if(commonSetting != null){
			buyerMarginStandard = commonSetting.getBuyerBasisMarginStandard();
			buyerTradeFeeStandard = commonSetting.getBuyerTradeFeeStandard();
			buyerDeliveryFeeStandard = commonSetting.getBuyerDeliveryFeeStandard();
		}
		if(traderSetting != null){
			buyerMarginStandard = traderSetting.getMarginStandard();
			buyerTradeFeeStandard = traderSetting.getTradeFeeStandard();
			buyerDeliveryFeeStandard = traderSetting.getDeliveryFeeStandard();
		}
		BigDecimal allMarginAmount = buyerMarginStandard.multiply(stock.getPreStockWeight());
		BigDecimal allTradeAmount = buyerTradeFeeStandard.multiply(stock.getPreStockWeight());
		BigDecimal allDeliveryAmount = buyerDeliveryFeeStandard.multiply(stock.getPreStockWeight());
		//验证账户资金是否大于冻结资金
		CustomerFundsVO fundsVo = basisTradeSettlementService.queryMarginAccountCapital(quoteVo.getCustomCode());
		if(fundsVo.getBalance().compareTo(allMarginAmount.add(allDeliveryAmount).add(allTradeAmount)) < 0){
			throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "账户资金不足,出价失败");
		}
		//冻结保证金\交易手续费\冻结交割手续费
		String accountType = FinancialEntityAccountTypeEnum.FINANCIAL_ENTITY_ACCOUNT_TYPE_1.getCode();
		Map<String,Object> businessReq = new HashMap<String,Object>();
		SCResponseVO marginResponseVO = basisTradeSettlementService.freezeCapital(stock.getStockCode(), quoteVo.getCustomCode(), allMarginAmount,accountType,businessReq,null);
		SCResponseVO tradeResponseVO = basisTradeSettlementService.freezeCapital(stock.getStockCode(), quoteVo.getCustomCode(), allTradeAmount,accountType,businessReq,null);
		SCResponseVO deliveryResponseVO = basisTradeSettlementService.freezeCapital(stock.getStockCode(), quoteVo.getCustomCode(), allDeliveryAmount,accountType,businessReq,null);

		agentEntity.setBuyerBasisMarginType(new DataItem(BasisCottonConstants.buyer_basis_margin_type_1,""));
		agentEntity.setBuyerTradeFeeType(new DataItem(BasisCottonConstants.buyer_trade_fee_type_1,""));
		agentEntity.setBuyerDeliveryFeeType(new DataItem(BasisCottonConstants.buyer_delivery_fee_type_1,""));
		agentEntity.setBuyerBasisMarginStandard(buyerMarginStandard);
		agentEntity.setBuyerBasisMarginAmount(allMarginAmount);
		agentEntity.setBuyerTradeFeeStandard(buyerTradeFeeStandard);
		agentEntity.setBuyerTradeFeeAmount(allTradeAmount);
		agentEntity.setBuyerDeliveryFeeStandard(buyerDeliveryFeeStandard);
		agentEntity.setBuyerDeliveryFeeAmount(allDeliveryAmount);
		return agentEntity;
	}

	private BasisQuoteEntity buildAgent(BasisQuoteEntity agentEntity, BasisQuoteVo quoteVo, BasisPreStockEntity stockEntity, BasisPreStockBaseInfoEntity baseStock){
		agentEntity.setStockId(stockEntity.getId());
		agentEntity.setStockCode(stockEntity.getStockCode());
		agentEntity.setUserName(quoteVo.getUserName());
		agentEntity.setTraderCode(quoteVo.getCustomCode());
		agentEntity.setTraderName(quoteVo.getCustomName());
		agentEntity.setQuoteStatus(new DataItem(BasisCottonConstants.quote_type_1,""));
		agentEntity.setQuoteWeight(stockEntity.getPreStockWeight());
        return agentEntity;
    }

	private BasisOrderEntity buildOrder(BasisQuoteVo quoteVo, BasisQuoteEntity agentEntity,BasisPreStockEntity stockEntity, BasisPreStockBaseInfoEntity baseStock,Long agentId){
		BasisOrderEntity orderEntity = new BasisOrderEntity();
		orderEntity.setId(BizIdGenerator.getInstance().generateBizId());
		orderEntity.setContractId(null);
		orderEntity.setStockId(stockEntity.getId());
		orderEntity.setStockCode(stockEntity.getStockCode());
		orderEntity.setQuoteId(agentId);
		orderEntity.setSellerCustomCode(baseStock.getTraderCode());
		orderEntity.setSellerCustomName(baseStock.getTraderName());
		orderEntity.setBuyerCustomCode(quoteVo.getCustomCode());
		orderEntity.setBuyerCustomName(quoteVo.getCustomName());
		orderEntity.setTradeQuantity(stockEntity.getPreStockWeight());
		orderEntity.setTradeDate(new Date());
		orderEntity.setSellerMarginStandard(stockEntity.getSellerMarginStandard());
		orderEntity.setSellerMarginAmount(stockEntity.getSellerMarginStandard().multiply(stockEntity.getPreStockWeight()));
		orderEntity.setSellerTradeFeeStandard(stockEntity.getSellerTradeFeeStandard());
		orderEntity.setSellerTradeFeeAmount(stockEntity.getSellerTradeFeeStandard().multiply(stockEntity.getPreStockWeight()));
		orderEntity.setSellerDeliveryFeeStandard(stockEntity.getSellerDeliveryFeeStandard());
		orderEntity.setSellerDeliveryFeeAmount(stockEntity.getSellerDeliveryFeeStandard().multiply(stockEntity.getPreStockWeight()));
		orderEntity.setBasisSellerMarginType(stockEntity.getBasisSellerMarginType());
		orderEntity.setBasisSellerTradeFeeType(stockEntity.getBasisSellerTradeFeeType());
		orderEntity.setBasisSellerDeliveryFeeType(stockEntity.getBasisSellerDeliveryFeeType());
		orderEntity.setBuyerMarginStandard(agentEntity.getBuyerBasisMarginStandard());
		orderEntity.setBuyerMarginAmount(agentEntity.getBuyerBasisMarginAmount());
		orderEntity.setBuyerTradeFeeStandard(agentEntity.getBuyerTradeFeeStandard());
		orderEntity.setBuyerTradeFeeAmount(agentEntity.getBuyerTradeFeeAmount());
		orderEntity.setBuyerDeliveryFeeStandard(agentEntity.getBuyerDeliveryFeeStandard());
		orderEntity.setBuyerDeliveryFeeAmount(agentEntity.getBuyerDeliveryFeeAmount());
		orderEntity.setBasisBuyerMarginType(agentEntity.getBuyerBasisMarginType());
		orderEntity.setBasisBuyerTradeFeeType(agentEntity.getBuyerTradeFeeType());
		orderEntity.setBasisBuyerDeliveryFeeType(agentEntity.getBuyerDeliveryFeeType());
		orderEntity.setQuoteType(stockEntity.getQuoteType());
		orderEntity.setPricingParty(stockEntity.getPricingParty());
		orderEntity.setFutureCode(stockEntity.getFutureCode());
		orderEntity.setBasisPriceRange(quoteVo.getPreQuotePrice());
		orderEntity.setPricingValidTime(stockEntity.getPricingValidTime());
		return orderEntity;
	}
}
