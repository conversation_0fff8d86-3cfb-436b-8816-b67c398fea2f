package com.basiscotton.manager.hall.core.impl;

import com.basiscotton.manager.hall.vo.BasisQuoteVo;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;

/**
 * @Title: IBasisQuoteService.java
 * @Description: 基差报价服务
 * <AUTHOR>
 * @date 2025/5/24
 * @version V1.0
 */
public interface IBasisQuoteService {

	public void createQuote(ServiceHandlerContext context, BasisQuoteVo quoteVo);

}
