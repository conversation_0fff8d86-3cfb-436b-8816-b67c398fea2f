package com.basiscotton.manager.hall.service;

import com.basiscotton.base.mappers.BasisStockMapper;
import com.basiscotton.manager.hall.vo.NowTradeStockTitleVo;
import org.harry.dandelion.framework.common.utils.DateUtils;
import org.harry.dandelion.framework.common.utils.ListUtils;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Title: GetNowTradeStockTiTle.java
 * @Description: 获取当日成交信息
 * <AUTHOR>
 * @date 2025/9/6
 * @version V1.0
 */
@Service("basisHall.getNowTradeStockTiTle.1")
@ApiRequestObject(value = "获取当日成交信息", name = "getNowTradeStockTiTle", groups = {"交易商端-基差交易-交易大厅"}, params = {

})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "titleList", desc = "查询分页列表", type = NowTradeStockTitleVo.class,multipart = true),
})
public class GetNowTradeStockTiTle implements IBusinessService {

    @Resource
    private BasisStockMapper basisStockMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        String nowDate = DateUtils.getDate(DateUtils.YYYY_MM_DD);
        List<NowTradeStockTitleVo> titleList = basisStockMapper.getNowTradeStockTiTle(nowDate);
        if(!(ListUtils.isNullOrEmpty(titleList))){
            for (NowTradeStockTitleVo vo : titleList){
                String content = "商品："+vo.getStockCode()+"已成交，成交价为"+vo.getTradeBasisPrice()+"元/吨";
                vo.setContent(content);
            }
        }
        //创建响应实体
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("titleList", titleList);
    }

}
