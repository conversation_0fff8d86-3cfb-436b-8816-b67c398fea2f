package com.basiscotton.manager.hall.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.basiscotton.base.entity.BasisStockEntity;
import com.basiscotton.base.mappers.BasisStockBaseInfoMapper;
import com.basiscotton.base.mappers.BasisStockMapper;
import com.basiscotton.manager.hall.vo.BatchInspectVo;
import com.basiscotton.manager.hall.vo.BatchWeightVo;
import com.basiscotton.manager.hall.vo.QuanlityBaseInfoVo;
import com.basiscotton.manager.stock.vo.BasisWhsReceiptVo;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Title: GetStockTelInfo.java
 * @Description: 获取商品联系方式
 * <AUTHOR>
 * @date 2025/6/1
 * @version V1.0
 */
@Service("basisHall.getQuanlityInfo.1")
@ApiRequestObject(value = "获取批次组批数据", name = "getQuanlityInfo", groups = {"交易商端-基差交易-交易大厅"}, params = {
        @ApiParamMeta(key = "stockCode", desc = "商品编码", type = String.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "baseInfoVo", desc = "组批数据", type = QuanlityBaseInfoVo.class),
})
public class GetQuanlityInfo implements IBusinessService {

    @Resource
    private BasisStockMapper basisStockMapper;

    @Resource
    private BasisStockBaseInfoMapper basisStockBaseInfoMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        String stockCode = context.getValueObject(String.class, "stockCode");

        QuanlityBaseInfoVo baseInfoVo = new QuanlityBaseInfoVo();
        //查询商品信息
        LambdaQueryWrapper<BasisStockEntity> queryWrapper = new LambdaQueryWrapper<BasisStockEntity>();
        queryWrapper.eq(BasisStockEntity::getStockCode, stockCode);
        BasisStockEntity stockEntity = basisStockMapper.selectOne(queryWrapper);
        String wareNo = stockEntity.getWarehouseReceiptNo();
        String batchNo  = stockEntity.getBatchNo();
        if(StringUtil.isNotEmpty(wareNo) && StringUtil.isNotEmpty(batchNo)){
            BasisWhsReceiptVo whsReceiptVo = basisStockBaseInfoMapper.getWhsReceiptVo(wareNo);
            BatchInspectVo batchInspectVo = basisStockBaseInfoMapper.getBatchInspectVo(batchNo);
            BatchWeightVo batchWeightVo = basisStockBaseInfoMapper.getBatchWeightVo(batchNo);

            baseInfoVo.setQuantity(String.valueOf(whsReceiptVo.getQuantity()));
            if (Objects.nonNull(batchInspectVo))
                BeanUtils.copyProperties(batchInspectVo, baseInfoVo);
            if (Objects.nonNull(batchWeightVo))
                BeanUtils.copyProperties(batchWeightVo, baseInfoVo);
        }
        //创建响应实体
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("baseInfoVo", baseInfoVo);
    }
}
