package com.basiscotton.manager.hall.service;

import com.basiscotton.manager.hall.core.BasisQuoteProcessor;
import com.basiscotton.manager.hall.core.impl.BasisBuyerQuoteService;
import com.basiscotton.manager.hall.vo.BasisQuoteVo;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

/**
 * @Title: CreateQuotePrice.java
 * @Description: 报价-买方点价
 * <AUTHOR>
 * @date 2025/5/24
 * @version V1.0
 */
@Service("basisHall.createQuotePrice.1")
@ApiRequestObject(value = "报价-买方点价", name = "createQuotePrice", groups = {"交易商端-基差交易-交易大厅"}, params = {
        @ApiParamMeta(key = "quoteVo", desc = "报价信息", type = BasisQuoteVo.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "success", desc = "返回结果", type = boolean.class),
        @ApiParamMeta(key = "message", desc = "返回消息", type = String.class),
})
public class CreateQuotePrice implements IBusinessService {

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        BasisQuoteVo quoteVo = context.getValueObject(BasisQuoteVo.class, "quoteVo");
        quoteVo.setUserName(context.getCurrentLoginName());
        quoteVo.setCustomCode(context.getCurrentUserCustomCode());
        quoteVo.setCustomName(context.getCurrentUserCustomName());
        BasisQuoteProcessor basisQuoteProcessor = new BasisQuoteProcessor();
        basisQuoteProcessor.process(new BasisBuyerQuoteService(), context,quoteVo);

        //构造返回数据
        this.createSuccessResponse(context);
        context.getResponseBody().setData("success", true);
        context.getResponseBody().setData("message", "点价成功");
    }



}
