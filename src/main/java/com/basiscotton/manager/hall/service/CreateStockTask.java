package com.basiscotton.manager.hall.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.BasisPreStockEntity;
import com.basiscotton.base.entity.BasisQuoteEntity;
import com.basiscotton.base.entity.BasisStockEntity;
import com.basiscotton.base.mappers.BasisPreStockMapper;
import com.basiscotton.base.mappers.BasisQuoteMapper;
import com.basiscotton.base.mappers.BasisStockMapper;
import com.basiscotton.common.apiservice.IBasisTradeSettlementService;
import com.basiscotton.common.apiservice.IBasisTradeStorageService;
import com.sinosoft.cnce.sc.base.enumeration.FinancialEntityAccountTypeEnum;
import com.sinosoft.cnce.sc.base.vo.SCResponseVO;
import lombok.extern.slf4j.Slf4j;
import org.harry.dandelion.framework.common.utils.DateUtils;
import org.harry.dandelion.framework.scheduler.quartz.job.DandelionJob;
import org.quartz.JobExecutionContext;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Title: CreateStockTask.java
 * @Description: 定期清除基差交易商品
 * <AUTHOR>
 * @date 2025/6/10
 * @version V1.0
 */
@Slf4j
public class CreateStockTask extends DandelionJob {
    @Resource
    private BasisStockMapper basisStockMapper;

    @Resource
    private BasisPreStockMapper basisPreStockMapper;

    @Resource
    private BasisQuoteMapper basisQuoteMapper;

    @Resource
    private IBasisTradeSettlementService basisTradeSettlementService;

    @Resource
    private IBasisTradeStorageService basisTradeStorageService;

    @Override
    public void process(JobExecutionContext context) throws Exception {
        this.handleStock();
        this.handlePreStock();
    }

    private void handleStock(){
        //驳回商品或者作废商品
        List<BasisStockEntity> stockList = basisStockMapper.getAllStock(DateUtils.getDate(DateUtils.YYYY_MM_DD));
        if(stockList != null && stockList.size() > 0){
            for (BasisStockEntity stock : stockList) {
                LambdaUpdateWrapper<BasisStockEntity> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(BasisStockEntity::getId, stock.getId())
                        .set(BasisStockEntity::getResourceAuditStatus, BasisCottonConstants.resource_audit_status_4);
                basisStockMapper.update(null, updateWrapper);
                List<Long> stockIdList = stockList.stream().map(BasisStockEntity::getId).collect(Collectors.toList());

                LambdaQueryWrapper<BasisQuoteEntity> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.in(basisQuoteEntity -> basisQuoteEntity.getStockId(), stockIdList);
                queryWrapper.eq(basisQuoteEntity -> basisQuoteEntity.getQuoteStatus(), BasisCottonConstants.quote_status_2);
                List<BasisQuoteEntity> quoteList = basisQuoteMapper.selectList(queryWrapper);
                if(quoteList != null && quoteList.size() > 0) {
                    //释放保证金
                    for (BasisQuoteEntity quote : quoteList) {
                        BigDecimal marginAmount = quote.getBuyerBasisMarginAmount();
                        BigDecimal tradeFeeAmount = quote.getBuyerTradeFeeAmount();
                        BigDecimal deliveryFeeAmount = quote.getBuyerDeliveryFeeAmount();
                        String accountType = FinancialEntityAccountTypeEnum.FINANCIAL_ENTITY_ACCOUNT_TYPE_1.getCode();
                        Map<String,Object> businessReq = new HashMap<String,Object>();
                        SCResponseVO marginResponseVO = basisTradeSettlementService.freeCapital(quote.getStockCode(), quote.getTraderCode(), marginAmount,accountType,businessReq,null);
                        SCResponseVO tradeResponseVO = basisTradeSettlementService.freeCapital(quote.getStockCode(), quote.getTraderCode(), tradeFeeAmount,accountType,businessReq,null);
                        SCResponseVO deliveryResponseVO = basisTradeSettlementService.freeCapital(quote.getStockCode(), quote.getTraderCode(), deliveryFeeAmount,accountType,businessReq,null);

                        LambdaUpdateWrapper<BasisQuoteEntity>  updateQuoteWrapper = new LambdaUpdateWrapper<>();
                        updateQuoteWrapper.eq(BasisQuoteEntity::getId, quote.getId())
                                .set(basisQuoteEntity -> basisQuoteEntity.getQuoteStatus(), BasisCottonConstants.quote_status_3);
                        basisQuoteMapper.update(null, updateQuoteWrapper);
                    }
                    //修改仓单状态
                    List<Long> succStockIdList = quoteList.stream().map(BasisQuoteEntity::getStockId).collect(Collectors.toList());
                    LambdaQueryWrapper<BasisStockEntity> stockWrapper = new LambdaQueryWrapper<>();
                    stockWrapper.in(BasisStockEntity::getId, succStockIdList);
                    List<BasisStockEntity> succStockList = basisStockMapper.selectList(stockWrapper);
                    if(succStockList != null && succStockList.size() > 0) {
                        for (BasisStockEntity stockEntity : succStockList) {
                            List<String> whsNoList = succStockList.stream().map(BasisStockEntity::getWarehouseReceiptNo).collect(Collectors.toList());
                            if(whsNoList != null && whsNoList.size() > 0) {
                                String whsNoStr = whsNoList.stream().collect(Collectors.joining(","));
                                basisTradeStorageService.updateWarehouseReceiptStatus(whsNoStr, BasisCottonConstants.TRANSACTION_STATUS_2,new HashMap<>(),"");
                            }
                        }
                    }
                }
            }
        }
    }

    private void handlePreStock(){
        //驳回商品或者作废商品
        List<BasisPreStockEntity> stockList = basisPreStockMapper.getAllStock(DateUtils.getDate(DateUtils.YYYY_MM_DD));
        if(stockList != null && stockList.size() > 0){
            for (BasisPreStockEntity stock : stockList) {
                LambdaUpdateWrapper<BasisStockEntity> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(BasisStockEntity::getId, stock.getId())
                        .set(BasisStockEntity::getResourceAuditStatus, BasisCottonConstants.resource_audit_status_4);
                basisStockMapper.update(null, updateWrapper);
                List<Long> stockIdList = stockList.stream().map(BasisPreStockEntity::getId).collect(Collectors.toList());

                LambdaQueryWrapper<BasisQuoteEntity> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.in(basisQuoteEntity -> basisQuoteEntity.getStockId(), stockIdList);
                queryWrapper.eq(basisQuoteEntity -> basisQuoteEntity.getQuoteStatus(), BasisCottonConstants.quote_status_2);
                List<BasisQuoteEntity> quoteList = basisQuoteMapper.selectList(queryWrapper);
                if(quoteList != null && quoteList.size() > 0) {
                    //释放保证金
                    for (BasisQuoteEntity quote : quoteList) {
                        BigDecimal marginAmount = quote.getBuyerBasisMarginAmount();
                        BigDecimal tradeFeeAmount = quote.getBuyerTradeFeeAmount();
                        BigDecimal deliveryFeeAmount = quote.getBuyerDeliveryFeeAmount();
                        String accountType = FinancialEntityAccountTypeEnum.FINANCIAL_ENTITY_ACCOUNT_TYPE_1.getCode();
                        Map<String,Object> businessReq = new HashMap<String,Object>();
                        SCResponseVO marginResponseVO = basisTradeSettlementService.freeCapital(quote.getStockCode(), quote.getTraderCode(), marginAmount,accountType,businessReq,null);
                        SCResponseVO tradeResponseVO = basisTradeSettlementService.freeCapital(quote.getStockCode(), quote.getTraderCode(), tradeFeeAmount,accountType,businessReq,null);
                        SCResponseVO deliveryResponseVO = basisTradeSettlementService.freeCapital(quote.getStockCode(), quote.getTraderCode(), deliveryFeeAmount,accountType,businessReq,null);

                        LambdaUpdateWrapper<BasisQuoteEntity>  updateQuoteWrapper = new LambdaUpdateWrapper<>();
                        updateQuoteWrapper.eq(BasisQuoteEntity::getId, quote.getId())
                                .set(basisQuoteEntity -> basisQuoteEntity.getQuoteStatus(), BasisCottonConstants.quote_status_3);
                        basisQuoteMapper.update(null, updateQuoteWrapper);
                    }
                }
            }
        }
    }
}
