package com.basiscotton.manager.hall.service;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.BasisPreStockEntity;
import com.basiscotton.base.entity.BasisStockEntity;
import com.basiscotton.base.mappers.BasisPreStockMapper;
import com.basiscotton.base.mappers.BasisStockMapper;
import lombok.extern.slf4j.Slf4j;
import org.harry.dandelion.framework.common.utils.DateUtils;
import org.harry.dandelion.framework.scheduler.quartz.job.DandelionJob;
import org.quartz.JobExecutionContext;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Title: CreateResourceTask.java
 * @Description: 定期清除基差资源
 * <AUTHOR>
 * @date 2025/6/10
 * @version V1.0
 */
@Slf4j
public class CreateResourceTask extends DandelionJob {
    @Resource
    private BasisStockMapper basisStockMapper;

    @Resource
    private BasisPreStockMapper basisPreStockMapper;

    @Override
    public void process(JobExecutionContext context) throws Exception {
        this.handleResource();
        this.handlePreResource();
    }

    private void handleResource(){
        //驳回商品或者作废商品
        List<BasisStockEntity> stockList = basisStockMapper.getAllResource(DateUtils.getDate(DateUtils.YYYY_MM_DD));
        if(stockList != null && stockList.size() > 0){
            List<Long> stockIdList = stockList.stream().map(BasisStockEntity::getId).collect(Collectors.toList());
            LambdaUpdateWrapper<BasisStockEntity> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(BasisStockEntity::getId, stockIdList)
                    .set(BasisStockEntity::getResourceAuditStatus, BasisCottonConstants.resource_audit_status_4);
            basisStockMapper.update(null, updateWrapper);
        }
    }

    private void handlePreResource(){
        //驳回商品或者作废商品
        List<BasisPreStockEntity> stockList = basisPreStockMapper.getAllResource(DateUtils.getDate(DateUtils.YYYY_MM_DD));
        if(stockList != null && stockList.size() > 0){
            for (BasisPreStockEntity stock : stockList) {
                List<Long> stockIdList = stockList.stream().map(BasisPreStockEntity::getId).collect(Collectors.toList());
                LambdaUpdateWrapper<BasisStockEntity> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.in(BasisStockEntity::getId, stockIdList)
                        .set(BasisStockEntity::getResourceAuditStatus, BasisCottonConstants.resource_audit_status_4);
                basisStockMapper.update(null, updateWrapper);
            }
        }
    }
}
