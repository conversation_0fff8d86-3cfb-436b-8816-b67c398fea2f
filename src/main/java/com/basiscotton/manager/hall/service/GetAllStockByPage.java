package com.basiscotton.manager.hall.service;

import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.mappers.BasisStockMapper;
import com.basiscotton.manager.hall.vo.BuyerStockReqVo;
import com.basiscotton.manager.hall.vo.BuyerStockResVo;
import com.basiscotton.manager.stock.enums.ColorGradeEnum;
import org.harry.dandelion.framework.core.params.PageParameter;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.rep.support.page.PipPagination;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Title: GetAllStockByPage.java
 * @Description: 全部资源列表
 * <AUTHOR>
 * @date 2025/5/24
 * @version V1.0
 */
@Service("basisHall.getAllStockByPage.1")
@ApiRequestObject(value = "全部资源列表", name = "getAllStockByPage", groups = {"交易商端-基差交易-交易大厅"}, params = {
        @ApiParamMeta(key = "pageParameter", desc = "分页参数", type = PageParameter.class),
        @ApiParamMeta(key = "reqVo", desc = "查询vo", type = BuyerStockReqVo.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "pagination", desc = "查询分页列表", type = BuyerStockResVo.class,pagination = true),
})
public class GetAllStockByPage implements IBusinessService {

    @Resource
    private BasisStockMapper basisStockMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        PageParameter pageParameter = context.getValueObject(PageParameter.class, "pageParameter");
        BuyerStockReqVo reqVo = context.getValueObject(BuyerStockReqVo.class, "reqVo");
        reqVo = this.buildInspectSearch(reqVo);
        reqVo.setResourceAuditStatus(BasisCottonConstants.resource_audit_status_3);
        List<String> tradeStatusList = new ArrayList<String>();
        tradeStatusList.add(BasisCottonConstants.trade_status_1);
        tradeStatusList.add(BasisCottonConstants.trade_status_2);
        reqVo.setTradeStatus(tradeStatusList);
        PipPagination<BuyerStockResVo> pipPagination = new PipPagination<>(pageParameter);
        pipPagination = basisStockMapper.getAllStockByPage(pipPagination, reqVo);

        //创建响应实体
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("pagination", pipPagination);
    }


    private BuyerStockReqVo buildInspectSearch(BuyerStockReqVo reqVo){
        reqVo.setColorGrade(this.colorConversion(reqVo.getColorGrade()));
        //开启颜色占比查询
        //设置颜色级占比筛选
        if (reqVo.isColorRate()) {
            //选择占比，不使用颜色级筛选条件
            reqVo.setColorGrade(new ArrayList<>());
            String beginColor = reqVo.getBeginColor();
            List<String> result = getValuesLessThan(ColorGradeEnum.getNameByCode(beginColor));
            //myInspectDataQueryVo.setBeginColor(ColorGradeEnum.getNameByCode(beginColor));
            reqVo.setBeginColorList(result);
        }
        //勾选且
        if (reqVo.isJust()){
            String endColor = reqVo.getEndColor();
            List<String> result = printValuesAfterTarget(ColorGradeEnum.getNameByCode(endColor));
            //myInspectDataQueryVo.setEndColor(ColorGradeEnum.getNameByCode(endColor));
            reqVo.setEndColorList(result);
        }
        return reqVo;
    }

    private static List<String> colorConversion(List<String> colorGradeList) {
        Map<String,String> map = new HashMap<String,String>();
        map.put("白棉1级","11");
        map.put("白棉2级","21");
        map.put("白棉3级","31");
        map.put("白棉4级","41");
        map.put("白棉5级","51");
        map.put("淡点污棉1级","12");
        map.put("淡点污棉2级","22");
        map.put("淡点污棉3级","32");
        map.put("淡黄染棉1级","13");
        map.put("淡黄染棉2级","23");
        map.put("淡黄染棉3级","33");
        map.put("黄染棉1级","14");
        map.put("黄染棉2级","24");

        List<String> colorIntList = new ArrayList<String>();
        if(colorGradeList != null && colorGradeList.size() > 0){
            for(String colorStr : colorGradeList){
                for (String keyStr : map.keySet()) {
                    if (keyStr != null && keyStr.equals(colorStr)) {
                        colorIntList.add(map.get(keyStr));
                    }
                }
            }
        }
        return colorIntList;
    }

    private static List<String> getValuesLessThan(String targetValue) {
        Map<String,String> map = new HashMap<String,String>();
        map.put("1","WHITE_COTTON_L1");
        map.put("2","WHITE_COTTON_L2");
        map.put("3","WHITE_COTTON_L3");
        map.put("4","WHITE_COTTON_L4");
        map.put("5","WHITE_COTTON_L5");
        map.put("6","WHITE_COTTON_L6");
        map.put("7","SPOT_COTTON_L1");
        map.put("8","SPOT_COTTON_L2");
        map.put("9","SPOT_COTTON_L3");
        map.put("10","SPOT_COTTON_L4");
        map.put("11","YELLOW_ISH_COTTON_L1");
        map.put("12","YELLOW_ISH_COTTON_L2");
        map.put("13","YELLOW_ISH_COTTON_L3");
        map.put("14","YELLOW_COTTON_L1");
        map.put("15","YELLOW_COTTON_L2");
        List<String> targetKeys = new ArrayList<>();
        // 找到所有值为targetValue的键
        for (Map.Entry<String, String> entry : map.entrySet()) {
            if (entry.getValue().equals(targetValue)) {
                targetKeys.add(entry.getKey());
            }
        }
        if (targetKeys.isEmpty()) {
            return Collections.emptyList(); // 目标值不存在
        }
        // 找到最大的键值作为基准
        int maxKey = targetKeys.stream()
                .mapToInt(Integer::parseInt)
                .max()
                .getAsInt();
        // 收集所有键小于maxKey的value
        List<String> result = new ArrayList<>();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            int key = Integer.parseInt(entry.getKey());
            if (key < maxKey) {
                result.add(entry.getValue());
            }
        }
        result.add(targetValue);
        return result;
    }

    private static List<String> printValuesAfterTarget(String targetValue ) {
        Map<String,String> map = new HashMap<String,String>();

        map.put("1","WHITE_COTTON_L1");
        map.put("2","WHITE_COTTON_L2");
        map.put("3","WHITE_COTTON_L3");
        map.put("4","WHITE_COTTON_L4");
        map.put("5","WHITE_COTTON_L5");
        map.put("6","WHITE_COTTON_L6");
        map.put("7","SPOT_COTTON_L1");
        map.put("8","SPOT_COTTON_L2");
        map.put("9","SPOT_COTTON_L3");
        map.put("10","SPOT_COTTON_L4");
        map.put("11","YELLOW_ISH_COTTON_L1");
        map.put("12","YELLOW_ISH_COTTON_L2");
        map.put("13","YELLOW_ISH_COTTON_L3");
        map.put("14","YELLOW_COTTON_L1");
        map.put("15","YELLOW_COTTON_L2");

        // 收集所有与目标value匹配的key，并转换为整数
        List<Integer> targetKeys = new ArrayList<>();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            if (entry.getValue().equals(targetValue)) {
                targetKeys.add(Integer.parseInt(entry.getKey()));
            }
        }
        if (targetKeys.isEmpty()) {
            return Collections.emptyList();
        }

        // 找到最大的目标key
        int    tempkey = Collections.max(targetKeys);



        // 将Map的所有key转换为整数并排序
        List<Integer> sortedKeys = map.keySet().stream()
                .map(Integer::parseInt)
                .sorted()
                .collect(Collectors.toList());

        // 收集所有大于maxKey的key对应的value
        List<String> result = new ArrayList<>();
        result.add(targetValue);
        for (Integer key : sortedKeys) {
            if (key > tempkey) {
                result.add(map.get(String.valueOf(key)));
            }
        }

        return result;
    }
}
