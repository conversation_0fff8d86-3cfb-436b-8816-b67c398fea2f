package com.basiscotton.manager.hall.service;

import com.basiscotton.manager.hall.vo.BuyerStockResVo;
import com.basiscotton.manager.hall.vo.SpotStockReqVO;
import org.apache.ibatis.jdbc.SQL;
import org.harry.dandelion.framework.rep.support.page.PipPagination;

public class SpotStockSqlBuilder {

    public String buildAllStockQuery(PipPagination<BuyerStockResVo> pipPagination, SpotStockReqVO reqVo) {
        SQL sql = new SQL();
        sql.SELECT("*");
        sql.FROM("trd_basis_stock stock");
        sql.LEFT_OUTER_JOIN("trd_basis_stock_base_info baseInfo ON stock.stock_base_info_id = baseInfo.id");
        // 现货挂牌交易
        sql.WHERE("stock.trade_mode = 1");
        // 未成交
        sql.WHERE("stock.trade_status = 2");

        sql = reqVo.buildSpotStockSql(sql);
        return sql.toString();
    }
}
