package com.basiscotton.manager.hall.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.basiscotton.base.entity.BasisQueryTemplateEntity;
import com.basiscotton.base.mappers.BasisQueryTemplateMapper;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Title: GetQueryTemplateByCode.java
 * @Description: 查询自定义模板
 * <AUTHOR>
 * @date 2025/6/4
 * @version V1.0
 */
@Service("basisHall.getQueryTemplateByCode.1")
@ApiRequestObject(value = "查询自定义模板", name = "getQueryTemplateByCode", groups = {"交易商端-基差交易-交易大厅"}, params = {
        @ApiParamMeta(key = "type", desc = "类型", type = String.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "templateList", desc = "模板列表", type = BasisQueryTemplateEntity.class,pagination = true),
})
public class GetQueryTemplateByCode implements IBusinessService {

    @Resource
    private BasisQueryTemplateMapper basisQueryTemplateMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {

        String type = context.getValueObject(String.class, "type");
        //获取参数
        String traderCode = context.getCurrentUserCustomCode();

        List<BasisQueryTemplateEntity> templateList = new ArrayList<BasisQueryTemplateEntity>();
        if(StringUtil.isNotEmpty(traderCode)){
            LambdaQueryWrapper<BasisQueryTemplateEntity> queryWrapper = new LambdaQueryWrapper<BasisQueryTemplateEntity>();
            queryWrapper.eq(BasisQueryTemplateEntity::getTraderCode, traderCode);
            queryWrapper.eq(BasisQueryTemplateEntity::getTemplateType, type);
            queryWrapper.orderByDesc(BasisQueryTemplateEntity::getCreateTime);
            templateList = basisQueryTemplateMapper.selectList(queryWrapper);
        }
        //构造返回数据
        this.createSuccessResponse(context);
        context.getResponseBody().setData("templateList", templateList);
    }
}