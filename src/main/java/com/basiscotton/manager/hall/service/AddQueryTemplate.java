package com.basiscotton.manager.hall.service;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.basiscotton.base.entity.BasisQueryTemplateEntity;
import com.basiscotton.base.mappers.BasisQueryTemplateMapper;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.harry.dandelion.framework.id.core.service.BizIdGenerator;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Title: AddQueryTemplate.java
 * @Description: 添加自定义查询模板
 * <AUTHOR>
 * @date 2025/6/4
 * @version V1.0
 */
@Service("basisHall.addQueryTemplate.1")
@ApiRequestObject(value = "添加自定义查询模板", name = "addQueryTemplate", groups = {"交易商端-基差交易-交易大厅"}, params = {
        @ApiParamMeta(key = "template", desc = "模板信息", type = BasisQueryTemplateEntity.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "success", desc = "返回结果", type = boolean.class),
        @ApiParamMeta(key = "message", desc = "返回消息", type = String.class),
})
public class AddQueryTemplate implements IBusinessService {

    @Resource
    private BasisQueryTemplateMapper basisQueryTemplateMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        BasisQueryTemplateEntity template = context.getValueObject(BasisQueryTemplateEntity.class, "template");

        if(StringUtil.isNotEmpty(template.getId())){
            LambdaUpdateWrapper<BasisQueryTemplateEntity> tempWrapper = new LambdaUpdateWrapper<BasisQueryTemplateEntity>();
            tempWrapper.eq(BasisQueryTemplateEntity::getId, template.getId());
            tempWrapper.set(BasisQueryTemplateEntity::getTemplateName, template.getTemplateName());
            tempWrapper.set(BasisQueryTemplateEntity::getTemplateValue, template.getTemplateValue());
            basisQueryTemplateMapper.update(null,tempWrapper);
        }else{
            template.setId(BizIdGenerator.getInstance().generateBizId());
            template.setTraderCode(context.getCurrentUserCustomCode());
            template.setTraderName(context.getCurrentUserCustomName());
            basisQueryTemplateMapper.insert(template);
        }
        //构造返回数据
        this.createSuccessResponse(context);
        context.getResponseBody().setData("success", true);
        context.getResponseBody().setData("message", "添加成功");
    }
}