package com.basiscotton.manager.hall.service;

import com.basiscotton.base.mappers.BasisStockMapper;
import com.basiscotton.manager.hall.vo.BuyerStockReqVo;
import com.basiscotton.manager.hall.vo.BuyerStockResVo;
import com.basiscotton.manager.hall.vo.SpotStockReqVO;
import org.harry.dandelion.framework.core.params.PageParameter;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.rep.support.page.PipPagination;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Title: GetAllSpotStockByPage.java
 * @Description: 全部现货挂牌资源列表
 * <AUTHOR>
 * @date 2025/9/3
 * @version V1.0
 */
@Service("spotHall.getAllSpotStockByPage.1")
@ApiRequestObject(value = "全部现货挂牌资源列表", name = "getAllSpotStockByPage", groups = {"交易商端-挂牌交易-交易大厅"}, params = {
        @ApiParamMeta(key = "pageParameter", desc = "分页参数", type = PageParameter.class),
        @ApiParamMeta(key = "reqVo", desc = "查询vo", type = BuyerStockReqVo.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "pagination", desc = "查询分页列表", type = BuyerStockResVo.class,pagination = true),
})
public class GetAllSpotStockByPage implements IBusinessService {

    @Resource
    private BasisStockMapper basisStockMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        PageParameter pageParameter = context.getValueObject(PageParameter.class, "pageParameter");
        SpotStockReqVO reqVo = context.getValueObject(SpotStockReqVO.class, "reqVo");
        PipPagination<BuyerStockResVo> pipPagination = new PipPagination<>(pageParameter);
        pipPagination = basisStockMapper.getAllSpotStockByPage(pipPagination, reqVo);

        //创建响应实体
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("pagination", pipPagination);
    }
}
