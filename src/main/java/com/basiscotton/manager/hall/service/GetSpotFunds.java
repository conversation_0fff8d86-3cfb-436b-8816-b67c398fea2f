package com.basiscotton.manager.hall.service;

import com.basiscotton.base.entity.SpotFundsEntity;
import com.basiscotton.base.mappers.SpotFundsMapper;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: GetSpotFunds.java
 * @Description: 获取现货挂牌的资金情况-交易中心
 * @date 2025/9/1 13:46
 */
@Service("spotHall.getSpotFunds.1")
@ApiRequestObject(value = "获取现货挂牌的资金情况-交易中心", name = "getSpotFunds", groups = {"交易商端-现货挂牌-交易大厅"}, params = {

})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "spotFundsEntity", desc = "资金情况-现货挂牌", type = SpotFundsEntity.class),
})
public class GetSpotFunds  implements IBusinessService {
    @Resource
    private SpotFundsMapper spotFundsMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {
//        //获取参数
        String tradeCode = context.getValueObject(String.class, "tradeCode");
//        if (StringUtil.isEmpty(tradeCode)) {
//            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, ExceptionEnums.S0001.getEMsg());
//        }

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        String tradeCode = context.getCurrentUserCustomCode();
        SpotFundsEntity spotFundsEntity = new SpotFundsEntity();
        List<SpotFundsEntity> list = spotFundsMapper.getSpotFundsByTraderCode(tradeCode);
        if (list != null && list.size() > 0) {
            spotFundsEntity = list.get(0);
        }
        //创建响应实体
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("spotFundsEntity", spotFundsEntity);
    }
}
