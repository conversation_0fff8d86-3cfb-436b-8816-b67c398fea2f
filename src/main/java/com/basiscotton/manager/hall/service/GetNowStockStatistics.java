package com.basiscotton.manager.hall.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.BasisQuoteEntity;
import com.basiscotton.base.entity.BasisStockEntity;
import com.basiscotton.base.mappers.BasisQuoteMapper;
import com.basiscotton.base.mappers.BasisStockMapper;
import com.basiscotton.manager.hall.vo.NowStockStatisticsVo;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

import static com.basiscotton.base.BasisCottonConstants.trade_status_2;
import static com.basiscotton.base.BasisCottonConstants.trade_status_3;

/**
 * @Title: GetNowStockStatistics.java
 * @Description: 统计当天商品挂单量，报价量，成交量
 * <AUTHOR>
 * @date 2025/9/6
 * @version V1.0
 */
@Service("basisHall.getNowStockStatistics.1")
@ApiRequestObject(value = "统计当天商品挂单量，报价量，成交量", name = "getNowStockStatistics", groups = {"交易商端-基差交易-交易大厅"})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "nowStockStatisticsVo", desc = "查询分页列表", type = NowStockStatisticsVo.class),
})
public class GetNowStockStatistics implements IBusinessService {

    @Resource
    private BasisStockMapper basisStockMapper;

    @Resource
    private BasisQuoteMapper basisQuoteMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        List<BasisStockEntity> todayStockList = getTodayStockList();

        // 当日总挂单量
        BigDecimal spotWeight = todayStockList.stream()
                .map(BasisStockEntity::getStockWeight)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 当日总成交量
        BigDecimal tradeWeight = todayStockList.stream()
                .filter(stock -> Objects.equals(stock.getTradeStatus().getCode(), trade_status_3))
                .map(BasisStockEntity::getStockWeight)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        List<BasisQuoteEntity> todayQuoteList = getTodayQuoteList();
        // 当日总报价量
        BigDecimal quoteWeight = todayQuoteList.stream()
                .map(BasisQuoteEntity::getQuoteWeight)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        NowStockStatisticsVo statistics = new NowStockStatisticsVo();
        statistics.setStockStatistics(spotWeight.toString());
        statistics.setQuoteStatistics(quoteWeight.toString());
        statistics.setTradeStatistics(tradeWeight.toString());

        //创建响应实体
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("nowStockStatisticsVo", statistics);
    }

    private List<BasisStockEntity> getTodayStockList() {
        LambdaQueryWrapper<BasisStockEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(BasisStockEntity::getTradeMode, BasisCottonConstants.trade_mode_1);
        qw.in(BasisStockEntity::getTradeStatus, trade_status_2, trade_status_3);
        qw.ge(BasisStockEntity::getCreateTime, LocalDate.now());
        return basisStockMapper.selectList(qw);
    }

    private List<BasisQuoteEntity> getTodayQuoteList() {
        LambdaQueryWrapper<BasisQuoteEntity> qw = new LambdaQueryWrapper<>();
        qw.ge(BasisQuoteEntity::getQuoteTime, LocalDate.now());
        return basisQuoteMapper.selectList(qw);
    }

}
