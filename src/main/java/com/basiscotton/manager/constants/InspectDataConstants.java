package com.basiscotton.manager.constants;

import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class InspectDataConstants {

    private InspectDataConstants() {
    }

    private static final String[][] colorGradeFieldArray = new String[][]{
            {"白棉1级", "11"},
            {"白棉2级", "21"},
            {"白棉3级", "31"},
            {"白棉4级", "41"},
            {"白棉5级", "51"},
            {"淡点污棉1级", "12"},
            {"淡点污棉2级", "22"},
            {"淡点污棉3级", "32"},
            {"淡黄染棉1级", "13"},
            {"淡黄染棉2级", "23"},
            {"淡黄染棉3级", "33"},
            {"黄染棉1级", "14"},
            {"黄染棉2级", "24"}
    };

    public static final Map<String, String> COLOR_GRADE_FIELD_MAP = Collections.unmodifiableMap(
            Stream.of(colorGradeFieldArray)
                    .collect(Collectors.toMap(a -> a[0], a -> a[1]))
    );


}
