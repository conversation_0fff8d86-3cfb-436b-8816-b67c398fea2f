package com.basiscotton.manager.notice.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.BasisNoticeEntity;
import com.basiscotton.base.mappers.BasisNoticeMapper;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


@Service("basisHall.getBasisNoticeService.1")
@ApiRequestObject(value = "查询基差广播通知", name = "getBasisNoticeService",groups = {"交易商端-基差交易-广播消息管理"}, params = {
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "noticeList",desc = "广播消息集合",type = BasisNoticeEntity.class,multipart = true)
})
public class GetBasisNoticeService implements IBusinessService {

	@Resource
	private BasisNoticeMapper basisNoticeMapper;

	@Override
	public void doVerify(ServiceHandlerContext context) {

	}

	@Override
	public void doWork(ServiceHandlerContext context) {
		LambdaQueryWrapper<BasisNoticeEntity> queryWrapper = Wrappers.lambdaQuery();
		queryWrapper.eq(BasisNoticeEntity::getNoticeType, BasisCottonConstants.noticeType_2);
		queryWrapper.eq(BasisNoticeEntity::getPublishStatus, BasisCottonConstants.publish_status_2);
		queryWrapper.eq(BasisNoticeEntity::getDelFlag, BasisCottonConstants.notice_nodel_flag);
		queryWrapper.orderByDesc(BasisNoticeEntity::getCreateTime);
		List<BasisNoticeEntity> noticeList = basisNoticeMapper.selectList(queryWrapper);
		//构造响应数据
		this.createSuccessResponse(context);
		context.getResponseBody().setData("noticeList", noticeList);
	}
}
