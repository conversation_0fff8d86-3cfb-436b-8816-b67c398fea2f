package com.basiscotton.manager.notice.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.BasisNoticeEntity;
import com.basiscotton.base.mappers.BasisNoticeMapper;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Title: DelBasisContract.java
 * @Description: 删除期货合约
 * <AUTHOR>
 * @date 2025/5/23
 * @version V1.0
 */
@Service("basisManage.delBasisNotice.1")
@ApiRequestObject(value = "删除广告通知", name = "delBasisNotice", groups = {"交易商端-基差交易-广播消息管理"}, params = {
        @ApiParamMeta(key = "id", desc = "广告通知id", type = String.class),
})
@ApiResponseObject(params = {
		@ApiParamMeta(key = "success", desc = "返回结果", type = boolean.class),
        @ApiParamMeta(key = "message", desc = "返回消息", type = String.class),
})
public class DelBasisNotice implements IBusinessService {

    @Resource
    private BasisNoticeMapper basisNoticeMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
    	String id = context.getValueObject(String.class, "id");
        QueryWrapper<BasisNoticeEntity> wrapper =  new QueryWrapper<>();
        wrapper.eq("id", id);
        BasisNoticeEntity basisNoticeEntity =  new BasisNoticeEntity();
        basisNoticeEntity.setDelFlag(BasisCottonConstants.notice_del_flag);
        basisNoticeMapper.update(basisNoticeEntity,wrapper);
        //构造返回数据
        this.createSuccessResponse(context);
        context.getResponseBody().setData("success", true);
        context.getResponseBody().setData("message", "删除成功");
    }
}
