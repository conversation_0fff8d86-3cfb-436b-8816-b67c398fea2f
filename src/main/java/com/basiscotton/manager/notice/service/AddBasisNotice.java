package com.basiscotton.manager.notice.service;

import com.basiscotton.base.entity.BasisNoticeEntity;
import com.basiscotton.base.mappers.BasisNoticeMapper;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.id.core.service.BizIdGenerator;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
/**
 * @Title: AddBasisFuture.java
 * @Description: 添加基差公告
 * <AUTHOR>
 * @date 2025/6/5
 * @version V1.0
 */
@Service("basisManage.addBasisNotice.1")
@ApiRequestObject(value = "基差交易公告添加", name = "addBasisNotice", groups = {"交易商端-基差交易-广播消息管理"}, params = {
        @ApiParamMeta(key = "noticeEntity", desc = "基差交易公告添加", type = BasisNoticeEntity.class),
})
@ApiResponseObject(params = {
		@ApiParamMeta(key = "success", desc = "返回结果", type = boolean.class),
        @ApiParamMeta(key = "message", desc = "返回消息", type = String.class),
})
public class AddBasisNotice implements IBusinessService {

    @Resource
    private BasisNoticeMapper basisNoticeMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        BasisNoticeEntity addBasisNotice = context.getValueObject(BasisNoticeEntity.class, "noticeEntity");
        addBasisNotice.setId(BizIdGenerator.getInstance().generateBizId());
        basisNoticeMapper.insert(addBasisNotice);
        //构造返回数据
        this.createSuccessResponse(context);
        context.getResponseBody().setData("success", true);
        context.getResponseBody().setData("message", "添加成功");
    }

}
