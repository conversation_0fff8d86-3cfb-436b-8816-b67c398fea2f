package com.basiscotton.manager.notice.service;

import com.basiscotton.base.entity.BasisNoticeEntity;
import com.basiscotton.base.mappers.BasisNoticeMapper;
import com.basiscotton.manager.notice.vo.BasisNoticeVo;
import org.harry.dandelion.framework.core.params.PageParameter;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.rep.support.page.PipPagination;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Title: getBasisNoticeList.java
 * @Description: 基差交易公告列表
 * <AUTHOR>
 * @date 2025/6/6
 * @version V1.0
 */
@Service("basisManage.getBasisNoticeList.1")
@ApiRequestObject(value = "基差交易公告列表", name = "getBasisNoticeList",groups = {"交易商端-基差交易-广播消息管理"}, params = {
        @ApiParamMeta(key = "pageParameter", desc = "分页参数", type = PageParameter.class),
        @ApiParamMeta(key = "queryParam", desc = "广播消息分页查询条件", type = BasisNoticeVo.class)
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "pipPagination",desc = "基差交易公告列表分页查询",type = BasisNoticeEntity.class)
})
public class GetBasisNoticeList implements IBusinessService {

    @Resource
    private BasisNoticeMapper basisNoticeMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取分页对象
        PageParameter pageParameter = context.getValueObject(PageParameter.class, "pageParameter");
        //获取查询条件
        BasisNoticeVo queryParam = context.getValueObject(BasisNoticeVo.class, "queryParam");
        PipPagination<BasisNoticeEntity> pipPagination = new PipPagination<BasisNoticeEntity>(pageParameter);
        pipPagination = basisNoticeMapper.queryNoticeByPage(pipPagination, queryParam);
        //构造响应数据
        this.createSuccessResponse(context);
        context.getResponseBody().setData("pagination", pipPagination);
    }
}
