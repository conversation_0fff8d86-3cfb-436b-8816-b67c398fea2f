package com.basiscotton.manager.notice.service;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.BasisNoticeEntity;
import com.basiscotton.base.mappers.BasisNoticeMapper;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Title: UpdBasisFuture.java
 * @Description: 编辑期货合约
 * <AUTHOR>
 * @date 2025/5/23
 * @version V1.0
 */
@Service("basisManage.updBasisNotice.1")
@ApiRequestObject(value = "编辑广播消息", name = "updBasisNotice", groups = {"交易商端-基差交易-广播消息管理"}, params = {
        @ApiParamMeta(key = "noticeEntity", desc = "编辑广播消息", type = BasisNoticeEntity.class),
})
@ApiResponseObject(params = {
		@ApiParamMeta(key = "success", desc = "返回结果", type = boolean.class),
        @ApiParamMeta(key = "message", desc = "返回消息", type = String.class),
})
public class UpdBasisNotice implements IBusinessService {

    @Resource
    private BasisNoticeMapper basisNoticeMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        BasisNoticeEntity basicNotice = context.getValueObject(BasisNoticeEntity.class, "noticeEntity");
        LambdaUpdateWrapper<BasisNoticeEntity> updateWrapper = new LambdaUpdateWrapper<BasisNoticeEntity>();
        basicNotice.setDelFlag(BasisCottonConstants.notice_nodel_flag);
        updateWrapper.eq(BasisNoticeEntity::getId,basicNotice.getId());
        updateWrapper.set(BasisNoticeEntity::getTitle,basicNotice.getTitle());
        updateWrapper.set(BasisNoticeEntity::getContent,basicNotice.getContent());
        updateWrapper.set(BasisNoticeEntity::getNoticeType,basicNotice.getNoticeType());
        updateWrapper.set(BasisNoticeEntity::getPublishStatus,basicNotice.getPublishStatus());
        updateWrapper.set(BasisNoticeEntity::getDelFlag, basicNotice.getDelFlag());
        basisNoticeMapper.update(null, updateWrapper);
        //构造返回数据
        this.createSuccessResponse(context);
        context.getResponseBody().setData("success", true);
        context.getResponseBody().setData("message", "修改成功");
    }

}
