package com.basiscotton.manager.resources.manage.service;

import com.basiscotton.base.mappers.BasisStockBaseInfoMapper;
import com.basiscotton.manager.resources.manage.vo.MBasisWhsReceiptParamVo;
import com.basiscotton.manager.resources.manage.vo.MBasisWhsReceiptVo;
import org.harry.dandelion.framework.core.params.PageParameter;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.harry.dandelion.framework.rep.support.page.PipPagination;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * @Title: GetBasisWhsReceiptByPage.java
 * @Description: 仓单资源列表-管理端
 * <AUTHOR>
 * @date 2025/5/30
 * @version V1.0
 */
@Service("basisStock.getBasisWhsReceiptByPage.1")
@ApiRequestObject(value = "仓单资源列表-管理端", name = "getBasisWhsReceiptByPage", groups = {"管理端-基差交易-资源管理"}, params = {
        @ApiParamMeta(key = "pageParameter", desc = "分页参数", type = PageParameter.class),
        @ApiParamMeta(key = "paramVo", desc = "查询vo", type = MBasisWhsReceiptParamVo.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "pagination", desc = "查询分页列表", type = MBasisWhsReceiptVo.class,pagination = true),
})
public class GetBasisWhsReceiptByPage implements IBusinessService {

    @Resource
    private BasisStockBaseInfoMapper basisStockBaseInfoMapper;
    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        String currentCustomCode = context.getCurrentUserCustomCode();
        PageParameter pageParameter = context.getValueObject(PageParameter.class, "pageParameter");
        MBasisWhsReceiptParamVo paramVo = context.getValueObject(MBasisWhsReceiptParamVo.class, "paramVo");
        if(StringUtil.isNotEmpty(paramVo.getBatchNumber())){
            paramVo.setBatchNumberList(Arrays.asList(paramVo.getBatchNumber().split(",")));
        }
        if(StringUtil.isNotEmpty(paramVo.getWhsNoStr())){
            paramVo.setWhsNoList(Arrays.asList(paramVo.getWhsNoStr().split(",")));
        }
        PipPagination<MBasisWhsReceiptVo> pipPagination = new PipPagination<MBasisWhsReceiptVo>(pageParameter);
        pipPagination = basisStockBaseInfoMapper.selectBasisWarehouseReceipt(pipPagination, paramVo);

        //创建响应实体
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("pagination", pipPagination);

    }
}
