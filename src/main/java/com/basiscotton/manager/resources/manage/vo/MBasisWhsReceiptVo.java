package com.basiscotton.manager.resources.manage.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.sinosoft.dandelion.system.client.params.DataItem;
import com.sinosoft.dandelion.system.client.serializer.DataItemCodeDeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class MBasisWhsReceiptVo implements Serializable {

	@ApiModelProperty(value = "仓单号")
	private String warehouseReceiptNumber;
	
	@ApiModelProperty(value = "当前货权人代码")
	private String traderCode;
	
	@ApiModelProperty(value = "当前货权人名称")
	private String traderName;


	@ApiModelProperty(value = "批号")
	private String batchNumber;
	
	@ApiModelProperty(value = "仓库代码")
	private String wareHouseCode;
	
	@ApiModelProperty(value = "仓库名称")
	private String wareHouseName;

	@ApiModelProperty(value = "件数：186")
	private Integer quantity;
	
	@ApiModelProperty(value = "唛头重量")
	private BigDecimal markWeight;
	
	@ApiModelProperty(value = "实际重量")
	private BigDecimal weight;

	@ApiModelProperty("公定重量（吨）")
	private BigDecimal conditionedWeight;

	@ApiModelProperty(value = "生产年度")
	private String productYear;
	
	@ApiModelProperty(value = "长度级")
	private String lengthClass;
	
	//颜色级
	@TableField("COLOR_LEVEL")
	@ApiModelProperty(value = "颜色级")
	private String colorLevel;

	@ApiModelProperty(value = "含杂率")
	private  BigDecimal dirty;

	//回潮率
	@ApiModelProperty(value = "回潮率")
	private  BigDecimal moistureRegain;

	//轧工质量
	@ApiModelProperty(value = "轧工质量")
	private  BigDecimal rllingQualityAvg;

	//马克隆
	@ApiModelProperty(value = "马克隆")
	private String macron;

	//断裂比
	@ApiModelProperty(value = "断裂比")
	private String strengthScale;

	//长度整齐度
	@ApiModelProperty(value = "长度整齐度")
	private BigDecimal tidyAvg;

	/**
	 *品名
	 */
	@TableField("STOCK_NAME")
	@ApiModelProperty(value = "品名")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem stockName;
	
	@ApiModelProperty(value = "产地")
	private String placeCode;
	
	@ApiModelProperty(value = "限制流转状态")
	private String limitStatus;
	
	@ApiModelProperty("监管机构代码")
	private String loanCompanyCode;
	
	@ApiModelProperty("监管机构")
	private String loanCompanyName;
	
	@ApiModelProperty("仓单状态1-正常、2-已注销、3-管控中")
	private String warehouseReceiptStatus;
	
	@ApiModelProperty("仓单生效状态1-未生效、2-已生效")
	private String warehouseReceiptEnableStatus;
	
	@ApiModelProperty("金融业务渠道：1正常、2购销、3金益棉、4棉贸通、5E棉通、6棉E通、7民生银行、8中国银行、9农商银行")
	private DataItem trdPledgeChannel;
	
	@ApiModelProperty("资金服务状态：1正常、2仓单质押、3代理采购、4合作销售、5委托融资、6订单融资")
	private DataItem trdPledgeStatus;
	
	@ApiModelProperty("基础监管状态1、正常 2、委托监管")
	private String agentSupervisionStatus;
	
	@ApiModelProperty("交易状态正常、交易中")
	private String whsTransactionStatus;
	
	@ApiModelProperty("资金监管状态：1、正常 2、三方监管-籽棉贷款 3、三方监管-皮棉调销 4、三方监管-融资监管状态 5、质押监管")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem trdSupervisionStatus;
	
	@ApiModelProperty("仓储业务状态：1、正常 2、注册中 3、注销中 4、移库中 5、过户中")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem trdStorageStatus;
	
	@ApiModelProperty("资金服务状态")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem financingServiceStatus;
	
	@ApiModelProperty("监管是否生效1、未生效2、已生效3、已失效")
	private String superviseStatus;

	@ApiModelProperty("是否保税棉: 1是  2否")
	private Integer bondedStatus;
	
}