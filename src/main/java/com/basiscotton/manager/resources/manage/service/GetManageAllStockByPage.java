package com.basiscotton.manager.resources.manage.service;

import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.mappers.BasisStockMapper;
import com.basiscotton.manager.resources.manage.vo.ManageStockReqVo;
import com.basiscotton.manager.resources.manage.vo.ManageStockResVo;
import org.harry.dandelion.framework.core.params.PageParameter;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.rep.support.page.PipPagination;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Title: GetManageAllStockByPage.java
 * @Description: 全部资源列表-管理端
 * <AUTHOR>
 * @date 2025/5/24
 * @version V1.0
 */
@Service("basisStock.getManageAllStockByPage.1")
@ApiRequestObject(value = "全部资源列表-管理端", name = "getManageAllStockByPage", groups = {"管理端-基差交易-资源管理"}, params = {
        @ApiParamMeta(key = "pageParameter", desc = "分页参数", type = PageParameter.class),
        @ApiParamMeta(key = "reqVo", desc = "查询vo", type = ManageStockReqVo.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "pagination", desc = "查询分页列表", type = ManageStockResVo.class,pagination = true),
})
public class GetManageAllStockByPage implements IBusinessService {

    @Resource
    private BasisStockMapper basisStockMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        PageParameter pageParameter = context.getValueObject(PageParameter.class, "pageParameter");
        ManageStockReqVo reqVo = context.getValueObject(ManageStockReqVo.class, "reqVo");
        PipPagination<ManageStockResVo> pipPagination = new PipPagination<ManageStockResVo>(pageParameter);
        List<String> tradeStatusList = new ArrayList<String>();
        tradeStatusList.add(BasisCottonConstants.trade_status_2);
        reqVo.setTradeStatus(tradeStatusList);
        reqVo.setTradeMode(BasisCottonConstants.trade_mode_1);
        pipPagination = basisStockMapper.getManageStockByPage(pipPagination, reqVo);

        //创建响应实体
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("pagination", pipPagination);
    }

}
