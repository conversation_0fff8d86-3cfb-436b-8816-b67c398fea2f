package com.basiscotton.manager.resources.manage.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.sinosoft.dandelion.system.client.params.DataItem;
import com.sinosoft.dandelion.system.client.serializer.DataItemCodeDeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Title: MPreStockResVo.java
 * @Description: 预售预购商品响应vo
 * <AUTHOR>
 * @date 2025/5/28
 * @version V1.0
 */
@Data
public class MPreStockResVo implements Serializable {

    @ApiModelProperty(value="主键")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value="商品编码")
    private String stockCode;

    @ApiModelProperty(value = "商品来源: 1-预售 2-预购")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem preStockSource;

    @ApiModelProperty(value = "资源审核状态: 1-未提交 2-待审核 3-已上市 4-已取消")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem resourceAuditStatus;

    @ApiModelProperty(value = "报价方式1一口价2、买方点价3卖方点价4预售5预购")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem tradeQuoteType;

    @ApiModelProperty(value = "资源展示方式: 1-公开 2-指定")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem resourceDisplayType;

    @ApiModelProperty(value="企业代码")
    private String traderCode;

    @ApiModelProperty(value="企业名称")
    private String traderName;

    @ApiModelProperty(value = "预售预购重量")
    private BigDecimal preStockWeight;

    @ApiModelProperty(value="期货合约")
    private String futureCode;

    @ApiModelProperty(value = "基差价格")
    private String basisPriceStr;

    @ApiModelProperty(value = "点价有效期")
    private Date pricingValidTime;

    @ApiModelProperty(value = "预计交货时间")
    private Date deliveryTime;

    @ApiModelProperty(value = "基差价格")
    private BigDecimal minBasisPrice;

    @ApiModelProperty(value = "基差价格")
    private BigDecimal maxBasisPrice;

    @ApiModelProperty(value="最小颜色级")
    private BigDecimal minColorGrade;

    @ApiModelProperty(value="最大颜色级")
    private BigDecimal maxColorGrade;

    @ApiModelProperty(value="最小马值")
    private BigDecimal minMkl;

    @ApiModelProperty(value="最大马值")
    private BigDecimal maxMkl;

    @ApiModelProperty(value="最小长度")
    private BigDecimal minLength;

    @ApiModelProperty(value="最大长度")
    private BigDecimal maxLength;

    @ApiModelProperty(value="最小强力")
    private BigDecimal minBreak;

    @ApiModelProperty(value="最大强力")
    private BigDecimal maxBreak;

    @ApiModelProperty(value="最小长整")
    private BigDecimal minUniformityAverage;

    @ApiModelProperty(value="最大长整")
    private BigDecimal maxUniformityAverage;

    @ApiModelProperty(value="最小含杂")
    private BigDecimal minImpurity;

    @ApiModelProperty(value="最大含杂")
    private BigDecimal maxImpurity;

    @ApiModelProperty(value="最小回潮")
    private BigDecimal minMoisture;

    @ApiModelProperty(value="最大回潮")
    private BigDecimal maxMoisture;

    @ApiModelProperty(value="长度")
    private String lengthStr;

    @ApiModelProperty(value="强力")
    private String breakStr;

    @ApiModelProperty(value="长整")
    private String uniformityAverageStr;

    @ApiModelProperty(value="含杂")
    private String impurityStr;

    @ApiModelProperty(value="回潮")
    private String moistureStr;

    @ApiModelProperty(value="马值")
    private String mklStr;

    @ApiModelProperty(value="颜色级")
    private String colorGradeStr;

    @ApiModelProperty(value="产地编码：兵团-第二师")
    private String placeType;

    @ApiModelProperty(value="完整地址")
    private String intactPlace;

    @ApiModelProperty(value="采摘方式：1、手摘棉 2机采棉")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem whsPickMode;

    @ApiModelProperty(value="补充说明")
    private String supplementContent;

    @ApiModelProperty(value="备注")
    private String remark;

}
