package com.basiscotton.manager.resources.manage.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Title: MPreResourceReqVo.java
 * @Description: 预售预购资源查询vo
 * <AUTHOR>
 * @date 2025/5/30
 * @version V1.0
 */
@Data
public class MPreResourceReqVo implements Serializable {

    private String resourceAuditStatus;

    @ApiModelProperty(value = "商品来源: 1-预售 2-预购")
    private String preStockSource;

}
