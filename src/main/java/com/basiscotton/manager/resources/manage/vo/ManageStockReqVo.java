package com.basiscotton.manager.resources.manage.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Title: TraderAllStockReqVo.java
 * @Description: 交易商资源列表查询vo
 * <AUTHOR>
 * @date 2025/5/24
 * @version V1.0
 */
@Data
public class ManageStockReqVo implements Serializable {


	@ApiModelProperty(value = "资源审核状态: 1-未提交 2-待审核 3-已上市 4-已取消")
	private String resourceAuditStatus;
	private List<String> tradeStatus;

	@ApiModelProperty(value = "仓单号")
	private String warehouseReceiptNo;

	@ApiModelProperty(value = "批号")
	private String batchNo;

	@ApiModelProperty(value = "仓库名称")
	private String storageWhsName;

	@ApiModelProperty(value = "交易模式")
	private String tradeMode;

}

