package com.basiscotton.manager.resources.manage.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.sinosoft.dandelion.system.client.params.DataItem;
import com.sinosoft.dandelion.system.client.serializer.DataItemCodeDeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Title: TraderAllStockResVo.java
 * @Description: 全部资源列表 vo
 * <AUTHOR>
 * @date 2025/5/24
 * @version V1.0
 */
@Data
public class ManageStockResVo implements Serializable {

	@ApiModelProperty(value="主键")
	@JSONField(serializeUsing = ToStringSerializer.class)
	private Long id;

	@ApiModelProperty(value = "交易商代码")
	private String traderCode;

	@ApiModelProperty(value = "交易商名称")
	private String traderName;

	@ApiModelProperty(value = "商品码")
	private String stockCode;

	@ApiModelProperty(value = "资源审核状态: 1-未提交 2-待审核 3-已上市 4-已取消")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem resourceAuditStatus;

	@ApiModelProperty(value = "报价方式1锁基差")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem quoteType;

	@ApiModelProperty(value = "资源交易方式: 1-打捆 2-单批")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem resourceTradeType;

	@ApiModelProperty(value = "资源展示方式: 1-公开 2-指定")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem resourceDisplayType;

	@ApiModelProperty(value="期货合约")
	private String futureCode;

	@ApiModelProperty(value = "基差价格")
	private BigDecimal basisPrice;

	@ApiModelProperty(value = "点价有效期")
	private Date pricingValidTime;

	@ApiModelProperty(value = "利息成本")
	private BigDecimal interestCost;

	@ApiModelProperty(value = "仓单号")
	private String warehouseReceiptNo;

	@ApiModelProperty(value = "批号")
	private String batchNo;

	@ApiModelProperty(value = "件数")
	private Integer quantity;

	@ApiModelProperty(value = "唛头重量")
	private BigDecimal marksWeight;

	@ApiModelProperty(value = "公定重量")
	private BigDecimal conditionedWeight;

	@ApiModelProperty(value = "颜色级")
	private String colorGrade;

	@ApiModelProperty(value = "长度")
	private BigDecimal avgLength;

	@ApiModelProperty(value = "强力")
	private String breakValue;

	@ApiModelProperty(value = "长整")
	private String uniformityAverageValue;

	@ApiModelProperty(value = "含杂")
	private String impurityRate;

	@ApiModelProperty(value = "回潮")
	private String moistureRate;

	@ApiModelProperty(value = "马值")
	private BigDecimal avgMkl;

	@ApiModelProperty(value = "产地")
	private String intactPlace;

	@ApiModelProperty(value = "仓库")
	private String storageWhsName;

	@ApiModelProperty(value = "棉花年度")
	private String productYear;

	@ApiModelProperty(value = "资金监管状态：1、正常 2、三方监管-籽棉贷款 3、三方监管-皮棉调销 4、三方监管-融资监管状态 5、质押监管 6、质押及三方监管")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem whsSupervisionStatus;

	@ApiModelProperty(value = "监管机构名称")
	private String superviseName;

	@ApiModelProperty(value = "备注")
	private String remark;

    @ApiModelProperty(value = "运费补贴申领方")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem transportSubsidyApplyParty;
}

