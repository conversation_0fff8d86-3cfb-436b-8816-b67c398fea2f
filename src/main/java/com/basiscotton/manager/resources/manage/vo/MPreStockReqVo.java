package com.basiscotton.manager.resources.manage.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Title: MPreStockReqVo.java
 * @Description: 预售预购商品查询vo
 * <AUTHOR>
 * @date 2025/5/28
 * @version V1.0
 */
@Data
public class MPreStockReqVo implements Serializable {

    private String resourceAuditStatus;
    private List<String> tradeStatus;

    private String preStockSource;

    @ApiModelProperty(value="采摘方式：1、手摘棉 2机采棉")
    private String whsPickMode;

}
