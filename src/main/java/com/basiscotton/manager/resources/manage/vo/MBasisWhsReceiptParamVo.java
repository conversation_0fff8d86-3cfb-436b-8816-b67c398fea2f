package com.basiscotton.manager.resources.manage.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
public class MBasisWhsReceiptParamVo implements Serializable{

	@ApiModelProperty(value = "已选择仓单号集合")
	private String whsNoStr;
	@ApiModelProperty(value = "已选择仓单号集合")
	private List<String> whsNoList;

	@ApiModelProperty(value = "交易商代码")
	private String traderCode;

	@ApiModelProperty(value = "交易商名称")
	private String traderName;

	//仓单号
	@ApiModelProperty(value = "仓单号")
	private String warehouseReceiptNumber;

	//批号
	@ApiModelProperty(value = "批号")
	private String batchNumber;

	//批号集合
	@ApiModelProperty(value = "批号")
	private List<String> batchNumberList;

	//仓库代码
	@ApiModelProperty(value = "仓库代码")
	private String wareHouseCode;

	//批号
	@ApiModelProperty(value = "仓库名称")
	private String wareHouseName;




}
