package com.basiscotton.manager.resources.manage.service;

import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.mappers.BasisPreStockMapper;
import com.basiscotton.manager.resources.manage.vo.MPreStockReqVo;
import com.basiscotton.manager.resources.manage.vo.MPreStockResVo;
import org.harry.dandelion.framework.core.params.PageParameter;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.harry.dandelion.framework.rep.support.page.PipPagination;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.util.List;

/**
 * @Title: GetManagePreStockPage.java
 * @Description: 预购预售商品列表-管理端
 * <AUTHOR>
 * @date 2025/5/28
 * @version V1.0
 */
@Service("basisStock.getManagePreStockPage.1")
@ApiRequestObject(value = "预购预售商品列表-管理端", name = "getManagePreStockPage", groups = {"管理端-基差交易-资源管理"}, params = {
        @ApiParamMeta(key = "pageParameter", desc = "分页参数", type = PageParameter.class),
        @ApiParamMeta(key = "paramVo", desc = "查询vo", type = MPreStockReqVo.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "pagination", desc = "查询分页列表", type = MPreStockResVo.class,pagination = true),
})
public class GetManagePreStockPage implements IBusinessService {

    @Resource
    private BasisPreStockMapper basisPreStockMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        PageParameter pageParameter = context.getValueObject(PageParameter.class, "pageParameter");
        MPreStockReqVo paramVo = context.getValueObject(MPreStockReqVo.class, "paramVo");
        paramVo.setResourceAuditStatus(BasisCottonConstants.resource_audit_status_1);
        PipPagination<MPreStockResVo> pipPagination = new PipPagination<MPreStockResVo>(pageParameter);
        pipPagination = basisPreStockMapper.selectStockList(pipPagination, paramVo);
        List<MPreStockResVo> dataList = pipPagination.getResult();
        if(dataList != null && dataList.size() > 0 ){
            pipPagination.setResult(this.buildData(dataList));
        }
        //创建响应实体
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("pagination", pipPagination);
    }

    private List<MPreStockResVo> buildData(List<MPreStockResVo> dataList){
        for (MPreStockResVo resVo : dataList) {
            //基差
            if(StringUtil.isNotEmpty(resVo.getMinBasisPrice()) && StringUtil.isNotEmpty(resVo.getMaxBasisPrice())){
                resVo.setBasisPriceStr(resVo.getMinBasisPrice() + "~" + resVo.getMaxBasisPrice());
            }else if(StringUtil.isNotEmpty(resVo.getMinBasisPrice())){
                resVo.setBasisPriceStr("≥" + resVo.getMinBasisPrice());
            }else if(StringUtil.isNotEmpty(resVo.getMaxBasisPrice())){
                resVo.setBasisPriceStr("≤" + resVo.getMaxBasisPrice());
            }
            //长度
            if(StringUtil.isNotEmpty(resVo.getMinLength()) && StringUtil.isNotEmpty(resVo.getMaxLength())){
                resVo.setLengthStr(resVo.getMinLength().setScale(0, RoundingMode.DOWN) + "~" + resVo.getMaxLength().setScale(0,RoundingMode.DOWN));
            }else if(StringUtil.isNotEmpty(resVo.getMinLength())){
                resVo.setLengthStr("≥" + resVo.getMinLength().setScale(0,RoundingMode.DOWN));
            }else if(StringUtil.isNotEmpty(resVo.getMaxLength())){
                resVo.setLengthStr("≤" + resVo.getMaxLength().setScale(0,RoundingMode.DOWN));
            }
            //强力
            if(StringUtil.isNotEmpty(resVo.getMinBreak()) && StringUtil.isNotEmpty(resVo.getMaxBreak())){
                resVo.setBreakStr(resVo.getMinBreak().setScale(0,RoundingMode.DOWN) + "~" + resVo.getMaxBreak().setScale(0,RoundingMode.DOWN));
            }else if(StringUtil.isNotEmpty(resVo.getMinBreak())){
                resVo.setBreakStr("≥" + resVo.getMinBreak().setScale(0,RoundingMode.DOWN));
            }else if(StringUtil.isNotEmpty(resVo.getMaxBreak())){
                resVo.setBreakStr("≤" + resVo.getMaxBreak().setScale(0,RoundingMode.DOWN));
            }
            //长整
            if(StringUtil.isNotEmpty(resVo.getMinUniformityAverage()) && StringUtil.isNotEmpty(resVo.getMaxUniformityAverage())){
                resVo.setUniformityAverageStr(resVo.getMinUniformityAverage().setScale(0,RoundingMode.DOWN) + "~" + resVo.getMaxUniformityAverage().setScale(0,RoundingMode.DOWN));
            }else if(StringUtil.isNotEmpty(resVo.getMinUniformityAverage())){
                resVo.setUniformityAverageStr("≥" + resVo.getMinUniformityAverage());
            }else if(StringUtil.isNotEmpty(resVo.getMaxUniformityAverage().setScale(0,RoundingMode.DOWN))){
                resVo.setUniformityAverageStr("≤" + resVo.getMaxUniformityAverage().setScale(0,RoundingMode.DOWN));
            }
            //含杂
            if(StringUtil.isNotEmpty(resVo.getMinImpurity()) && StringUtil.isNotEmpty(resVo.getMaxImpurity())){
                resVo.setImpurityStr(resVo.getMinImpurity().setScale(1,RoundingMode.DOWN) + "~" + resVo.getMaxImpurity().setScale(1,RoundingMode.DOWN));
            }else if(StringUtil.isNotEmpty(resVo.getMinImpurity())){
                resVo.setImpurityStr("≥" + resVo.getMinImpurity().setScale(1,RoundingMode.DOWN));
            }else if(StringUtil.isNotEmpty(resVo.getMaxImpurity())){
                resVo.setImpurityStr("≤" + resVo.getMaxImpurity().setScale(1,RoundingMode.DOWN));
            }
            //回潮
            if(StringUtil.isNotEmpty(resVo.getMinMoisture()) && StringUtil.isNotEmpty(resVo.getMaxMoisture())){
                resVo.setMoistureStr(resVo.getMinMoisture().setScale(1,RoundingMode.DOWN) + "~" + resVo.getMaxMoisture().setScale(1,RoundingMode.DOWN));
            }else if(StringUtil.isNotEmpty(resVo.getMinMoisture())){
                resVo.setMoistureStr("≥" + resVo.getMinMoisture().setScale(1,RoundingMode.DOWN));
            }else if(StringUtil.isNotEmpty(resVo.getMaxMoisture())){
                resVo.setMoistureStr("≤" + resVo.getMaxMoisture().setScale(1,RoundingMode.DOWN));
            }
            //马值
            if(StringUtil.isNotEmpty(resVo.getMinMkl()) && StringUtil.isNotEmpty(resVo.getMaxMkl())){
                resVo.setMklStr(resVo.getMinMkl() + "~" + resVo.getMaxMkl());
            }else if(StringUtil.isNotEmpty(resVo.getMinMkl())){
                resVo.setMklStr("≥" + resVo.getMinMkl());
            }else if(StringUtil.isNotEmpty(resVo.getMaxMkl())){
                resVo.setMklStr("≤" + resVo.getMaxMkl());
            }
            //颜色级
            if(StringUtil.isNotEmpty(resVo.getMinColorGrade()) && StringUtil.isNotEmpty(resVo.getMaxColorGrade())){
                resVo.setColorGradeStr(resVo.getMinColorGrade() + "~" + resVo.getMaxColorGrade());
            }else if(StringUtil.isNotEmpty(resVo.getMinColorGrade())){
                resVo.setColorGradeStr("≥" + resVo.getMinColorGrade());
            }else if(StringUtil.isNotEmpty(resVo.getMaxColorGrade())){
                resVo.setColorGradeStr("≤" + resVo.getMaxColorGrade());
            }
        }
        return dataList;
    }
}
