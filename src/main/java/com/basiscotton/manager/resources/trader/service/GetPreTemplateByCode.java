package com.basiscotton.manager.resources.trader.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.basiscotton.base.entity.BasisPreStockTemplateEntity;
import com.basiscotton.base.mappers.BasisPreStockTemplateMapper;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Title: GetPreTemplateByCode.java
 * @Description: 获取售预购商品模板-交易商
 * <AUTHOR>
 * @date 2025/6/4
 * @version V1.0
 */
@Service("basisStock.getPreTemplateByCode.1")
@ApiRequestObject(value = "获取售预购商品模板-交易商", name = "getPreTemplateByCode", groups = {"交易商端-基差交易-资源管理"}, params = {

})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "templateList", desc = "模板列表", type = BasisPreStockTemplateEntity.class,pagination = true),
})
public class GetPreTemplateByCode implements IBusinessService {

    @Resource
    private BasisPreStockTemplateMapper basisPreStockTemplateMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        String traderCode = context.getCurrentUserCustomCode();

        List<BasisPreStockTemplateEntity> templateList = new ArrayList<BasisPreStockTemplateEntity>();
        if(StringUtil.isNotEmpty(traderCode)){
            LambdaQueryWrapper<BasisPreStockTemplateEntity> queryWrapper = new LambdaQueryWrapper<BasisPreStockTemplateEntity>();
            queryWrapper.eq(BasisPreStockTemplateEntity::getTraderCode, traderCode);
            queryWrapper.orderByDesc(BasisPreStockTemplateEntity::getCreateTime);
            templateList = basisPreStockTemplateMapper.selectList(queryWrapper);
        }
        //构造返回数据
        this.createSuccessResponse(context);
        context.getResponseBody().setData("templateList", templateList);
    }
}