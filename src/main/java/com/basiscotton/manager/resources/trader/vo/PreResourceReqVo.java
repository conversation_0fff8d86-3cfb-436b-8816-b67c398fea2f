package com.basiscotton.manager.resources.trader.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Title: PreResourceReqVo.java
 * @Description: 预售预购资源查询vo
 * <AUTHOR>
 * @date 2025/5/28
 * @version V1.0
 */
@Data
public class PreResourceReqVo implements Serializable {

    private String resourceAuditStatus;
    private String traderCode;

    @ApiModelProperty(value = "商品来源: 1-预售 2-预购")
    private String preStockSource;

}
