package com.basiscotton.manager.resources.trader.service;

import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.BasisPreStockTemplateEntity;
import com.basiscotton.base.mappers.BasisPreStockTemplateMapper;
import com.basiscotton.manager.resources.trader.vo.PreStockTemplateVo;
import com.sinosoft.dandelion.system.client.params.DataItem;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.harry.dandelion.framework.id.core.service.BizIdGenerator;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * @Title: AddPreStockTemplate.java
 * @Description: 添加预售预购商品模板-交易商
 * <AUTHOR>
 * @date 2025/6/4
 * @version V1.0
 */
@Service("basisStock.addPreStockTemplate.1")
@ApiRequestObject(value = "添加预售预购商品模板-交易商", name = "addPreStockTemplate", groups = {"交易商端-基差交易-资源管理"}, params = {
        @ApiParamMeta(key = "template", desc = "模板信息", type = PreStockTemplateVo.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "success", desc = "返回结果", type = boolean.class),
        @ApiParamMeta(key = "message", desc = "返回消息", type = String.class),
})
public class AddPreStockTemplate implements IBusinessService {

    @Resource
    private BasisPreStockTemplateMapper basisPreStockTemplateMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        PreStockTemplateVo entityVo = context.getValueObject(PreStockTemplateVo.class, "template");

        if(StringUtil.isNotEmpty(entityVo.getId())){
            BasisPreStockTemplateEntity entity = this.buildTemplateData(entityVo);
            entity.setId(Long.valueOf(entityVo.getId()));
            basisPreStockTemplateMapper.updateById(entity);
        }else{
            BasisPreStockTemplateEntity entity = this.buildTemplateData(entityVo);
            entity.setId(BizIdGenerator.getInstance().generateBizId());
            entity.setTraderCode(context.getCurrentUserCustomCode());
            entity.setTraderName(context.getCurrentUserCustomName());
            basisPreStockTemplateMapper.insert(entity);
        }
        //构造返回数据
        this.createSuccessResponse(context);
        context.getResponseBody().setData("success", true);
        context.getResponseBody().setData("message", "添加成功");
    }

    private BasisPreStockTemplateEntity buildTemplateData(PreStockTemplateVo entityVo){
        BasisPreStockTemplateEntity entity = new BasisPreStockTemplateEntity();
        entity.setTemplateName(entityVo.getTemplateName());
        entity.setPreStockSource(new DataItem(entityVo.getPreStockSource(),""));
        entity.setPreStockWeight(entityVo.getPreStockWeight());
        BigDecimal weight = entityVo.getPreStockWeight();
        BigDecimal quantity = weight.multiply(BasisCottonConstants.batch_weught).setScale(0, RoundingMode.UP);
        entity.setQuantity(quantity.intValue());
        entity.setPlaceType(entityVo.getPlaceType());
        entity.setIntactPlace(entityVo.getIntactPlace());
        entity.setWhsPickMode(new DataItem(entityVo.getWhsPickMode(),""));
        entity.setMinColorGrade(entityVo.getMinColorGrade());
        entity.setMinMkl(entityVo.getMinMkl());
        entity.setMaxMkl(entityVo.getMaxMkl());
        entity.setMinLength(entityVo.getMinLength());
        entity.setMaxLength(entityVo.getMaxLength());
        entity.setMinBreak(entityVo.getMinBreak());
        entity.setMaxBreak(entityVo.getMaxBreak());
        entity.setMinUniformityAverage(entityVo.getMinUniformityAverage());
        entity.setMinImpurity(entityVo.getMinImpurity());
        entity.setMinMoisture(entityVo.getMinMoisture());
        entity.setMaxMoisture(entityVo.getMaxMoisture());
        entity.setSupplementContent(entityVo.getSupplementContent());

        entity.setFutureCode(entityVo.getFutureCode());
        entity.setMinBasisPrice(entityVo.getMinBasisPrice());
        entity.setMaxBasisPrice(entityVo.getMaxBasisPrice());
        entity.setPricingValidTime(entityVo.getPricingValidTime());
        entity.setDeliveryTime(entityVo.getDeliveryTime());
        entity.setRemark(entityVo.getRemark());
        return entity;
    }
}
