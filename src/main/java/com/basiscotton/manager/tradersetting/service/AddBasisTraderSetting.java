package com.basiscotton.manager.tradersetting.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.basiscotton.base.entity.BasisTraderSettingEntity;
import com.basiscotton.base.mappers.BasisTraderSettingMapper;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.id.core.service.BizIdGenerator;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Title: AddBasisTraderSetting.java
 * @Description: 新增基差交易商设置
 * <AUTHOR>
 * @date 2025/6/5
 * @version V1.0
 * @Deprecated 已废弃
 */
@Deprecated
@Service("basisManage.addBasisTraderSetting.1")
@ApiRequestObject(
        value = "新增基差交易商设置",
        name = "addBasisTraderSetting",
        groups = {"管理端-基差交易-交易商设置"},
        params = {
                @ApiParamMeta(key = "traderSetting", desc = "基差交易商设置信息", type = BasisTraderSettingEntity.class)
        }
)
@ApiResponseObject(params = {
        @ApiParamMeta(key = "success", desc = "返回结果", type = boolean.class),
        @ApiParamMeta(key = "message", desc = "返回消息", type = String.class),
})
public class AddBasisTraderSetting implements IBusinessService {

    @Resource
    private BasisTraderSettingMapper basisTraderSettingMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {
        BasisTraderSettingEntity traderSetting = context.getValueObject(BasisTraderSettingEntity.class, "traderSetting");
        LambdaQueryWrapper<BasisTraderSettingEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BasisTraderSettingEntity::getTraderCode, traderSetting.getTraderCode());
        queryWrapper.eq(BasisTraderSettingEntity::getBasisSettingType, traderSetting.getBasisSettingType());
        if (basisTraderSettingMapper.selectCount(queryWrapper) > 0) {
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "无法重复添加交易商保证金、手续费设置");
        }
    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        // 获取参数
        BasisTraderSettingEntity traderSetting = context.getValueObject(BasisTraderSettingEntity.class, "traderSetting");

        // 自动生成主键id
        traderSetting.setId(BizIdGenerator.getInstance().generateBizId());
        basisTraderSettingMapper.insert(traderSetting);

        // 构造返回内容
        this.createSuccessResponse(context);
        context.getResponseBody().setData("success", true);
        context.getResponseBody().setData("message", "添加成功");
    }
}
