package com.basiscotton.manager.tradersetting.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.basiscotton.base.entity.BasisTraderSettingEntity;
import com.basiscotton.base.mappers.BasisTraderSettingMapper;
import org.harry.dandelion.framework.core.params.PageParameter;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.harry.dandelion.framework.rep.support.page.PipPagination;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Title: GetBasisTraderSetting.java
 * @Description: 基差交易商设置集合
 * <AUTHOR>
 * @date 2025/6/5
 * @version V1.0
 * @Deprecated 已废弃
 */
@Deprecated
@Service("basisManage.getBasisTraderSetting.1")
@ApiRequestObject(
        value = "基差交易商设置集合",
        name = "getBasisTraderSetting",
        groups = {"管理端-基差交易-交易商设置"},
        params = {
                @ApiParamMeta(key = "pageParameter", desc = "分页参数", type = PageParameter.class),
                @ApiParamMeta(key = "traderCode", desc = "交易商代码", type = String.class),
                @ApiParamMeta(key = "traderName", desc = "交易商名称", type = String.class),
        }
)
@ApiResponseObject(params = {
        @ApiParamMeta(key = "traderSettingList", desc = "基差交易商设置集合", type = BasisTraderSettingEntity.class, pagination = true),
})
public class GetBasisTraderSetting implements IBusinessService {

    @Resource
    private BasisTraderSettingMapper basisTraderSettingMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        PageParameter pageParameter = context.getValueObject(PageParameter.class, "pageParameter");
        String traderCode = context.getValueObject(String.class, "traderCode");
        String traderName = context.getValueObject(String.class, "traderName");

        LambdaQueryWrapper<BasisTraderSettingEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtil.isNotEmpty(traderCode)) {
            queryWrapper.like(BasisTraderSettingEntity::getTraderCode, traderCode);
        }
        if (StringUtil.isNotEmpty(traderName)) {
            queryWrapper.like(BasisTraderSettingEntity::getTraderName, traderName);
        }
        queryWrapper.orderByDesc(BasisTraderSettingEntity::getUpdateTime);
        PipPagination<BasisTraderSettingEntity> pipPagination = new PipPagination<BasisTraderSettingEntity>(pageParameter);
        pipPagination = basisTraderSettingMapper.selectPage(pipPagination, queryWrapper);
        //创建返回数据
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("pipPagination", pipPagination);
    }
}
