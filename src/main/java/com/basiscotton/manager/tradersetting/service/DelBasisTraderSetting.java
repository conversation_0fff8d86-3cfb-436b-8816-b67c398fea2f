package com.basiscotton.manager.tradersetting.service;

import com.basiscotton.base.mappers.BasisTraderSettingMapper;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Title: DelBasisTraderSetting.java
 * @Description: 删除交易商设置
 * <AUTHOR>
 * @date 2025/6/5
 * @version V1.0
 * @Deprecated已废弃
 */
@Deprecated
@Service("basisManage.delBasisTraderSetting.1")
@ApiRequestObject(value = "删除交易商设置", name = "delBasisTraderSetting", groups = {"管理端-基差交易-交易商设置"}, params = {
        @ApiParamMeta(key = "id", desc = "交易商ID", type = String.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "success", desc = "返回结果", type = boolean.class),
        @ApiParamMeta(key = "message", desc = "返回消息", type = String.class),
})
public class DelBasisTraderSetting implements IBusinessService {

    @Resource
    private BasisTraderSettingMapper basisTraderSettingMapper;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        // 获取参数
        String id = context.getValueObject(String.class, "id");
        basisTraderSettingMapper.deleteById(id);
        // 构造返回数据
        this.createSuccessResponse(context);
        context.getResponseBody().setData("success", true);
        context.getResponseBody().setData("message", "删除成功");
    }
}
