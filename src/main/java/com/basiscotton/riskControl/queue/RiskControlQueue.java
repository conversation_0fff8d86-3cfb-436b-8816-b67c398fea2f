package com.basiscotton.riskControl.queue;

import com.basiscotton.riskControl.vo.RiskControl;

import java.util.concurrent.LinkedBlockingDeque;

/**
 * @Title: RiskControlQueue.java
 * @Description: 追保强平队列
 * <AUTHOR>
 * @date 2025/8/20
 * @version V1.0
 */
public class RiskControlQueue {
    //无界链表队列，存放行情数据， 使用分离锁：头节点和尾节点操作可以并发进行
    private final LinkedBlockingDeque<RiskControl> rcQue = new LinkedBlockingDeque<>();

    //从头上添加数据
    public void addFirst(RiskControl riskControlVo) {
        rcQue.addFirst(riskControlVo);
    }

    public boolean remove(RiskControl riskControlVo) {
        return rcQue.remove(riskControlVo);
    }

    //从尾部取出并移除数据
    public RiskControl pollLast() {
        return rcQue.pollLast();
    }

    //从尾部取出不移除数据
    public RiskControl peekLast() {
        return rcQue.peekLast();
    }

}
