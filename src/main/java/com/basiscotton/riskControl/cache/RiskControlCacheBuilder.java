package com.basiscotton.riskControl.cache;

import com.basiscotton.riskControl.cache.constant.RiskControlCacheEnum;
import com.basiscotton.riskControl.vo.RiskControl;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * @Title: RiskControlCacheBuilder.java
 * @Description: 风险控制及锁缓存
 * <AUTHOR>
 * @date 2025/8/23
 * @version V1.0
 */
@Configuration
@Slf4j
public class RiskControlCacheBuilder {
    @Primary
    @Bean
    public Cache<String, RiskControl> pricingCommandCache(){
        Cache<String, RiskControl> cache = Caffeine.newBuilder()
                .initialCapacity(RiskControlCacheEnum.RiskControlCache.getInitialCapacity())
                .maximumSize(RiskControlCacheEnum.RiskControlCache.getMaximumSize())
                //不过期.expireAfterAccess(CacheEnum.FundsAccountLockCache.getExpires(), TimeUnit.SECONDS)
                .recordStats()
                .build();
        log.info("风险控制及锁缓存构建完成！");
        return cache;
    }
}
