package com.basiscotton.riskControl.cache.constant;

/**
 * @Title: RiskControlCacheConstants.java
 * @Description: 缓存配置项的常量类
 * <AUTHOR>
 * @date 2025/8/23
 * @version V1.0
 */
public class RiskControlCacheConstants {
    /**
     * 默认过期时间（配置类中我使用的时间单位是秒，所以这里如 3*60 为3分钟）
     */
    public static final int EXPIRES_2_MIN = 2 * 60;
    public static final int DEFAULT_EXPIRES = 3 * 60;
    public static final int EXPIRES_5_MIN = 5 * 60;
    public static final int EXPIRES_24_hour = 24 * 60 * 60;

    public static final int EXPIRES_NO = Integer.MAX_VALUE;

}
