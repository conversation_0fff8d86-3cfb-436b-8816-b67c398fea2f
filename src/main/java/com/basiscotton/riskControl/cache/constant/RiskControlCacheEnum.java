package com.basiscotton.riskControl.cache.constant;


/**
 * @Title: RiskControlCacheEnum.java
 * @Description: 缓存配置项的枚举类
 * <AUTHOR>
 * @date 2025/8/23
 * @version V1.0
 */
public enum RiskControlCacheEnum {
    //风险控制锁缓存
    RiskControlCache("riskControlCache", RiskControlCacheConstants.EXPIRES_NO,500,100000);


    private String name;
    private long expire;

    private int initialCapacity;

    private int maximumSize;

    RiskControlCacheEnum(String name, long expire, int initialCapacity, int maximumSize){
        this.name = name;
        this.expire = expire;
        this.initialCapacity = initialCapacity;
        this.maximumSize = maximumSize;
    }

     String getName() {
        return name;
    }

     public long getExpires() {
        return expire;
    }

    public int getInitialCapacity() {
        return initialCapacity;
    }

     public int getMaximumSize() {
        return maximumSize;
    }
}
