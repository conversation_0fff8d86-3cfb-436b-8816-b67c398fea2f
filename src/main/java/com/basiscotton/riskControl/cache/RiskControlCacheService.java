package com.basiscotton.riskControl.cache;

import com.basiscotton.riskControl.vo.RiskControl;
import com.github.benmanes.caffeine.cache.Cache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Title: RiskControlCacheService.java
 * @Description: 风险控制缓存服务
 * <AUTHOR>
 * @date 2025/8/23
 * @version V1.0
 */
@Component
@Slf4j
public class RiskControlCacheService {

    @Resource
    Cache<String, RiskControl> riskControlVoCache;

    /**
     * 添加缓存
     */
    public void addCache(String key, RiskControl riskControlVo) {
        riskControlVoCache.get(key, k -> riskControlVo);
    }
    /**
     * 获取缓存中的某个值
     */
    public RiskControl getCache(String key) {
        return riskControlVoCache.getIfPresent(key);
    }
    /**
     * 获取缓存中的所有值
     */
    public List<RiskControl> getAllCache() {
        List<RiskControl> riskControlList = new ArrayList<RiskControl>(riskControlVoCache.asMap().values());
        return riskControlList;
    }
    /**
     * 返回RiskControlCache的引用
     */
    public Cache<String, RiskControl> getRiskControlCache() {
        return riskControlVoCache;
    }
    /**
     * 删除缓存
     */
    public void removeCache(String key) {
        riskControlVoCache.invalidate(key);
    }

    /**
     * 初始化缓存
     */
    public void initCache(ArrayList<RiskControl> list) {
        list.stream().forEach(riskControlVo -> {
            try {
                riskControlVoCache.put(riskControlVo.getRiskControlId(), riskControlVo);
            } catch (Exception e) {
                log.error("初始化缓存失败。", e);
                throw new RuntimeException("初始化缓存失败。");
            }
        });

    }

}
