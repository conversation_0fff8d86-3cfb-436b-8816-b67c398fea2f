package com.basiscotton.riskControl.threads;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Title: RiskControlThreadHandler.java
 * @Description: 风险控制线程处理器
 * <AUTHOR>
 * @date 2025/8/23
 * @version V1.0
 */
@Component
@Slf4j
public class RiskControlThreadHandler implements CommandLineRunner, DisposableBean {

//    @Resource
//    private CommonRiskControlServiceImpl commonRiskControlServiceImpl;
//    @Resource
//    private BuyerRiskControlService buyerRiskControlService;
//    @Resource
//    private SellerRiskControlService sellerRiskControlService;


    private volatile boolean running = true;
    private Thread handlerThread;

    @Override
    public void run(String... args) throws Exception {
        //启动点价指令处理线程
        handlerThread = new Thread(() -> {
            while (running) {
            try {
                //买方风险控制处理
                //卖方风险控制处理
                Thread.sleep(5000);
                } catch (Exception e) {
                    log.error("风险控制处理线程异常", e);
                }
            }
            log.debug("风险控制处理线程已退出");
        }, "risk-control-handler-thread");

        handlerThread.start();
    }

    @Override
    public void destroy() throws Exception {
        log.info("正在关闭风险控制处理线程...");
        running = false;           // 通知线程退出
        if (handlerThread != null && handlerThread.isAlive()) {// 中断 sleep 状态
            handlerThread.interrupt();
        }
    }
}
