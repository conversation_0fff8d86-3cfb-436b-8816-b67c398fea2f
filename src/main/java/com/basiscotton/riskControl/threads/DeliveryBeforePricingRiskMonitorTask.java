package com.basiscotton.riskControl.threads;

import com.basiscotton.riskControl.business.DeliveryBeforePricingRiskMonitor;
import lombok.extern.slf4j.Slf4j;
import org.harry.dandelion.framework.scheduler.quartz.job.DandelionJob;
import org.quartz.JobExecutionContext;

import javax.annotation.Resource;

/**
 * @Title: DeliveryBeforePricingRiskMonitorTask.java
 * @Description: 先交割后点价的定时任务 定时执行,作用于先交割后点价批次。
 * <AUTHOR>
 * @date 2025/8/26
 * @version V1.0
 */
@Slf4j
public class DeliveryBeforePricingRiskMonitorTask extends DandelionJob {
    @Resource
    private DeliveryBeforePricingRiskMonitor deliveryBeforePricingRiskMonitor;
    @Override
    public void process(JobExecutionContext context) throws Exception {
        //买方追保
        deliveryBeforePricingRiskMonitor.buyerPricingPriceRisk_marginCall();
        //买方强平
        deliveryBeforePricingRiskMonitor.buyerPricingPriceRisk_liquidation();
        //卖方强平
        deliveryBeforePricingRiskMonitor.sellerPricingPriceRisk_liquidation();

    }
}
