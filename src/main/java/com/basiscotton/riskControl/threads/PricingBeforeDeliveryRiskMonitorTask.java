package com.basiscotton.riskControl.threads;

import com.basiscotton.riskControl.business.PricingBeforeDeliveryRiskMonitor;
import lombok.extern.slf4j.Slf4j;
import org.harry.dandelion.framework.scheduler.quartz.job.DandelionJob;
import org.quartz.JobExecutionContext;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: PricingCommandExpireTask.java
 * @Description: 先点价后交割的定时任务 于15:05执行;日盘结束后判定基差波动范围,作用于先点价后交割批次。
 * @date 2025/8/22 23:11
 */
@Slf4j
public class PricingBeforeDeliveryRiskMonitorTask extends DandelionJob {
    @Resource
    private PricingBeforeDeliveryRiskMonitor pricingBeforeDeliveryRiskMonitor;
    @Override
    public void process(JobExecutionContext context) throws Exception {
        //
        pricingBeforeDeliveryRiskMonitor.basisPriceRiskAssessment();

    }
}
