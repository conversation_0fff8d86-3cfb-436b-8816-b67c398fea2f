package com.basiscotton.riskControl.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.concurrent.locks.ReentrantLock;

/**
 * @Title: RiskControlVo.java
 * @Description: 风险控制vo
 * <AUTHOR>
 * @date 2025/8/23
 * @version V1.0
 */
@Data
public class RiskControl implements Serializable {

    /**风控ID*/
    private String riskControlId;

    /**交割标的ID*/
    private String targetId;

    /**期货合约代码*/
    private String futureCode;

    /**批号*/
    private String batchNo;

    /**交割方式：1先点价后交割2先交割后点价*/
    private String deliveryType;

    /**追保线*/
    private BigDecimal marginCallStandard;

    /**强平线*/
    private BigDecimal liquidationPrice;

    /**追保强平状态1盯市中2追保中3已追保4已强平*/
    private String riskControlStatus;

    /**锁对象,公平锁(同时避免等待线程同时唤醒),禁止序列化;*/
    public ReentrantLock riskControlLock = new ReentrantLock(true);

}

