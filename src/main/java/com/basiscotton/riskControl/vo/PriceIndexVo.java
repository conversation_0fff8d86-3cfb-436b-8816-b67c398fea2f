package com.basiscotton.riskControl.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: PriceIndexVo.java
 * @Description: 监控价格指数
 * @date 2025/8/23 20:56
 */
@ApiModel(value = "监控价格指数")
@Data
public class PriceIndexVo {

    @Column(name ="COTTON_INDEX_NAME")
    @ApiModelProperty(value = "价格指数名称")
    private String cottonIndexName;

    @Column(name ="COTTON_INDEX_PRICE")
    @ApiModelProperty(value = "价格指数价格")
    private BigDecimal cottonIndexPrice;

    @Column(name ="RELEASE_DATA")
    @ApiModelProperty(value = "发布时间")
    private Date releaseData;
}
