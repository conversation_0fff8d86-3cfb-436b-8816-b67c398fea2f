package com.basiscotton.riskControl.reentrantlocks;

import com.basiscotton.riskControl.vo.RiskControl;
import com.github.benmanes.caffeine.cache.Cache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * @Title: RiskControlLockService.java
 * @Description: 风险控制锁服务
 * <AUTHOR>
 * @date 2025/8/23
 * @version V1.0
 */
@Component
@Slf4j
public class RiskControlLockService {

    @Resource
    Cache<String, RiskControl> riskControlCache;

    /**
     *  对单一点价指令加锁，线程安全
     *  加锁实现,后面要紧跟try块，及临界区代码
     */
    public void lockRiskControl(String riskControlId) {
        RiskControl riskControl = riskControlCache.getIfPresent(riskControlId);
        if(riskControl!=null){
           riskControl.riskControlLock.lock();
        }else {
            log.error("加锁失败，风险控制不存在，指令ID：{}", riskControl.getRiskControlId());
            throw new RuntimeException("风险控制不存在。");
        }
    }

    /**
     *  对单一风险控制解锁，线程安全
     */
    public void unlockRiskControl(String riskControlId) {
        RiskControl riskControl = riskControlCache.getIfPresent(riskControlId);
        if(riskControl!=null){
            // 解锁实现,要求在业务代码的finally块中调用。
            // 注意：解锁操作必须执行，否则线程会一直持有锁
            // 加锁的代码和对应的解锁的代码必须定义在同一个类中
            // 防御性编码，防止lock前出现异常(缓存)，finally块中解锁失败；这样lock在try块前最后行或块中第一行都可以
            if (riskControl.riskControlLock.isHeldByCurrentThread()) {
                riskControl.riskControlLock.unlock();
            }
        }else {
            log.error("解锁失败，风险控制不存在，指令ID：{}", riskControl.getRiskControlId());
            throw new RuntimeException("风险控制不存在。");
        }
    }

    /**
     * 对单一风险控制限时加锁,基本用不到
     */
    public boolean lockRiskControlWithTimeOut(String riskControlId, long timeout, TimeUnit timeUnit) {
        boolean lockAcquired = false;
        RiskControl riskControl = riskControlCache.getIfPresent(riskControlId);
        try {
            lockAcquired = riskControl.riskControlLock.tryLock(timeout, timeUnit);
        }catch (InterruptedException e) {
            log.error("风险控制不存在加锁失败：{}", riskControl);
            throw new RuntimeException("风险控制不存在加锁失败。");
        }
        return lockAcquired;
    }
}
