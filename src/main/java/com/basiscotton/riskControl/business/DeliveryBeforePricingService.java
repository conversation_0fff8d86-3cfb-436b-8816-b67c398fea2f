package com.basiscotton.riskControl.business;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.BasisCreditBusinessEntity;
import com.basiscotton.base.entity.BasisDeliveryTargetEntity;
import com.basiscotton.base.entity.BasisRiskControlEntity;
import com.basiscotton.base.mappers.BasisCreditBusinessMapper;
import com.basiscotton.base.mappers.BasisDeliveryTargetMapper;
import com.basiscotton.base.mappers.BasisRiskControlMapper;
import com.basiscotton.futurequotes.vo.RealTimeQuoteVo;
import com.github.benmanes.caffeine.cache.Cache;
import lombok.extern.slf4j.Slf4j;
import org.harry.dandelion.framework.common.utils.DateUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Title: PricingBeforeDeliveryService.java
 * @Description: 先交割后点价-风险仓单服务
 * 先交割后点价处理服务
 *  买方追保-资金处理
 *      修改追保风险控制状态
 *      修改交割标的风险控制状态、保证金相关字段，提高追保险、强平线
 *  买方追保-仓单抵免处理
 *      修改追保风险控制状态
 *      修改交割标的风险控制状态、保证金相关字段，提高追保险、强平线
 *  买方强平-资金处理
 *      修改追保风险控制状态、增加强平风险控制数据
 *      修改交割标的风险控制状态、保证金相关字段
 *  卖方强平-资金处理
 *      修改追保风险控制状态、增加强平风险控制数据
 *      修改交割标的风险控制状态、保证金相关字段
 *  资金替换仓单抵免处理
 *      修改交割标的保证金相关字段，修改仓单冻结字段
 *
 *  问题点
 *      强平是否存在指令中数据，是否存在指令锁控制？风险数据是否锁定？
 *      追保中数据这时触发强平，是修改追保数据增加强平数据？还是原追保数据修改？
 *
 * <AUTHOR>
 * @date 2025/8/26
 * @version V1.0
 */
@Service
@Slf4j
public class DeliveryBeforePricingService {

    @Resource
    private BasisRiskControlMapper basisRiskControlMapper;

    @Resource
    private BasisDeliveryTargetMapper basisDeliveryTargetMapper;

    @Resource
    private BasisCreditBusinessMapper basisCreditBusinessMapper;

    @Resource
    private Cache<String, RealTimeQuoteVo> quotesCache;

    //买方追保-资金处理
    public void buyerMarginCallCapitalHandle(String targetId) {
        log.debug("先交割后点价-开始执行买方追保-资金处理。交割标的ID{}", targetId);
        BasisDeliveryTargetEntity target = basisDeliveryTargetMapper.selectById(targetId);

        LambdaQueryWrapper<BasisRiskControlEntity> queryRiskControl = new LambdaQueryWrapper<BasisRiskControlEntity>();
        queryRiskControl.eq(BasisRiskControlEntity::getTargetId, targetId);
        queryRiskControl.eq(BasisRiskControlEntity::getDeliveryType, BasisCottonConstants.DELIVERY_TYPE_2);
        queryRiskControl.eq(BasisRiskControlEntity::getFundsType, BasisCottonConstants.funds_type_2);
        queryRiskControl.eq(BasisRiskControlEntity::getRiskControlStatus, BasisCottonConstants.risk_control_status_1);
        BasisRiskControlEntity riskControl = basisRiskControlMapper.selectOne(queryRiskControl);
        //修改风险控制状态
        LambdaUpdateWrapper<BasisRiskControlEntity> updateWrapper = new LambdaUpdateWrapper<BasisRiskControlEntity>();
        updateWrapper.eq(BasisRiskControlEntity::getId, riskControl.getId());
        updateWrapper.set(BasisRiskControlEntity::getRiskControlStatus, BasisCottonConstants.risk_control_status_2);
        basisRiskControlMapper.update(null, updateWrapper);
        //修改交割标的风险控制状态、保证金相关字段
        BigDecimal marginCallLine = target.getBuyerPricingMarginCallLine();
        BigDecimal marginCallStandard = target.getBuyerPricingMarginCallStandard();
        BigDecimal marginCallAmount = target.getBuyerPricingMarginAmount();
        BigDecimal liquidationLine = target.getBuyerPricingForcedLiquidationLine();

        BigDecimal newMarginCallLine = marginCallLine.add(marginCallStandard);
        BigDecimal newMarginCallAmount = marginCallAmount.add(riskControl.getMarginCallAmount());
        BigDecimal newLiquidationLine = liquidationLine.add(marginCallStandard);

        LambdaUpdateWrapper<BasisDeliveryTargetEntity> updateTarget = new LambdaUpdateWrapper<BasisDeliveryTargetEntity>();
        updateTarget.eq(BasisDeliveryTargetEntity::getId, riskControl.getTargetId());
        updateTarget.set(BasisDeliveryTargetEntity::getTargetRiskControlType, BasisCottonConstants.target_risk_control_type_1);
        updateTarget.set(BasisDeliveryTargetEntity::getBasisRiskControlStatus, BasisCottonConstants.basis_risk_control_status_1);
        updateTarget.set(BasisDeliveryTargetEntity::getPricingRiskControlStatus, BasisCottonConstants.pricing_risk_control_status_4);
        updateTarget.set(BasisDeliveryTargetEntity::getRiskControlTime, DateUtils.getDate());
        updateTarget.set(BasisDeliveryTargetEntity::getBuyerPricingMarginCallLine, newMarginCallLine);
        updateTarget.set(BasisDeliveryTargetEntity::getBuyerPricingMarginAmount, newMarginCallAmount);
        updateTarget.set(BasisDeliveryTargetEntity::getBuyerPricingForcedLiquidationLine, newLiquidationLine);
        basisDeliveryTargetMapper.update(null, updateTarget);
        log.debug("先交割后点价-结束执行买方追保处理。");
    }

    //买方追保-仓单抵免处理
    public void buyerMarginCallWarehouseReceiptHandle(String targetId, String whsNo, Integer creditNum) {
        log.debug("先交割后点价-开始执行买方追保-仓单抵免处理。交割标的ID{}，仓单号{}，抵免次数{}", targetId, whsNo, creditNum);
        BasisDeliveryTargetEntity target = basisDeliveryTargetMapper.selectById(targetId);

        LambdaQueryWrapper<BasisRiskControlEntity> queryRiskControl = new LambdaQueryWrapper<BasisRiskControlEntity>();
        queryRiskControl.eq(BasisRiskControlEntity::getTargetId, targetId);
        queryRiskControl.eq(BasisRiskControlEntity::getDeliveryType, BasisCottonConstants.DELIVERY_TYPE_2);
        queryRiskControl.eq(BasisRiskControlEntity::getFundsType, BasisCottonConstants.funds_type_2);
        queryRiskControl.eq(BasisRiskControlEntity::getRiskControlStatus, BasisCottonConstants.risk_control_status_1);
        BasisRiskControlEntity riskControl = basisRiskControlMapper.selectOne(queryRiskControl);

        //修改风险控制状态
        LambdaUpdateWrapper<BasisRiskControlEntity> updateWrapper = new LambdaUpdateWrapper<BasisRiskControlEntity>();
        updateWrapper.eq(BasisRiskControlEntity::getId, riskControl.getId());
        updateWrapper.set(BasisRiskControlEntity::getRiskControlStatus, BasisCottonConstants.risk_control_status_2);
        updateWrapper.set(BasisRiskControlEntity::getMarginCallTime, DateUtils.getDate());
        basisRiskControlMapper.update(null, updateWrapper);
        //修改交割标的风险控制状态、保证金相关字段
        BigDecimal marginCallLine = target.getBuyerPricingMarginCallLine();
        BigDecimal marginCallStandard = target.getBuyerPricingMarginCallStandard();
        BigDecimal liquidationLine = target.getBuyerPricingForcedLiquidationLine();
        Integer targetCreditNum = target.getBuyerCreditNum();
        BigDecimal creditAmount = target.getBuyerCreditAmount();

        BigDecimal newMarginCallLine = marginCallLine.add(marginCallStandard);
        BigDecimal newLiquidationLine = liquidationLine.add(marginCallStandard);
        Integer newCreditNum = targetCreditNum+creditNum;
        BigDecimal newCreditAmount = creditAmount.add(riskControl.getMarginCallAmount());

        LambdaUpdateWrapper<BasisDeliveryTargetEntity> updateTarget = new LambdaUpdateWrapper<BasisDeliveryTargetEntity>();
        updateTarget.eq(BasisDeliveryTargetEntity::getId, riskControl.getTargetId());
        updateTarget.set(BasisDeliveryTargetEntity::getTargetRiskControlType, BasisCottonConstants.target_risk_control_type_1);
        updateTarget.set(BasisDeliveryTargetEntity::getBasisRiskControlStatus, BasisCottonConstants.basis_risk_control_status_1);
        updateTarget.set(BasisDeliveryTargetEntity::getPricingRiskControlStatus, BasisCottonConstants.pricing_risk_control_status_4);
        updateTarget.set(BasisDeliveryTargetEntity::getRiskControlTime, DateUtils.getDate());
        updateTarget.set(BasisDeliveryTargetEntity::getBuyerPricingMarginCallLine, newMarginCallLine);
        updateTarget.set(BasisDeliveryTargetEntity::getBuyerPricingForcedLiquidationLine, newLiquidationLine);
        updateTarget.set(BasisDeliveryTargetEntity::getBuyerCreditNum, newCreditNum);
        updateTarget.set(BasisDeliveryTargetEntity::getBuyerCreditAmount, newCreditAmount);
        basisDeliveryTargetMapper.update(null, updateTarget);
        log.debug("先交割后点价-结束执行买方追保-仓单抵免处理。");
    }

    //取消买方仓单抵免次数处理
    public void canalBuyerWarehouseReceiptCreditHandle(String targetId) {
        log.debug("开始执行取消买方仓单抵免次数处理。交割标的id{}", targetId);
        //修改交割标的风险控制状态、保证金相关字段
        BasisDeliveryTargetEntity target = basisDeliveryTargetMapper.selectById(targetId);

        BigDecimal buyerCreditAmount = target.getBuyerCreditAmount();
        BigDecimal marginCallAmount = target.getBuyerPricingMarginAmount();

        BigDecimal newMarginCallAmount = marginCallAmount.add(buyerCreditAmount);

        LambdaUpdateWrapper<BasisDeliveryTargetEntity> updateTarget = new LambdaUpdateWrapper<BasisDeliveryTargetEntity>();
        updateTarget.eq(BasisDeliveryTargetEntity::getId, targetId);
        updateTarget.set(BasisDeliveryTargetEntity::getBuyerCreditNum, 0);
        updateTarget.set(BasisDeliveryTargetEntity::getBuyerCreditAmount, BigDecimal.ZERO);
        updateTarget.set(BasisDeliveryTargetEntity::getBuyerPricingMarginAmount, newMarginCallAmount);
        basisDeliveryTargetMapper.update(null, updateTarget);

        LambdaQueryWrapper<BasisCreditBusinessEntity> queryWrapper = new LambdaQueryWrapper<BasisCreditBusinessEntity>();
        queryWrapper.eq(BasisCreditBusinessEntity::getBusinessId, targetId);
        queryWrapper.eq(BasisCreditBusinessEntity::getCreditType, BasisCottonConstants.credit_type_2);
        queryWrapper.eq(BasisCreditBusinessEntity::getFundsType, BasisCottonConstants.funds_type_2);
        queryWrapper.eq(BasisCreditBusinessEntity::getBusinessCreditStatus, BasisCottonConstants.business_credit_status_1);
        List<BasisCreditBusinessEntity> creditBusinessList = basisCreditBusinessMapper.selectList(queryWrapper);

        List<Long> creditBusinessIdList = creditBusinessList.stream().map(BasisCreditBusinessEntity::getId).collect(Collectors.toList());
        LambdaUpdateWrapper<BasisCreditBusinessEntity> updateCredit = new LambdaUpdateWrapper<BasisCreditBusinessEntity>();
        updateCredit.in(BasisCreditBusinessEntity::getId, creditBusinessIdList);
        updateCredit.set(BasisCreditBusinessEntity::getBusinessCreditStatus, BasisCottonConstants.business_credit_status_2);
        basisCreditBusinessMapper.update(null, updateCredit);
        log.debug("结束执行取消买方仓单抵免次数处理。");
    }

}
