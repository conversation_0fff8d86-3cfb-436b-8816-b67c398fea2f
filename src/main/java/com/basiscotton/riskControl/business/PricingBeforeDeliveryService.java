package com.basiscotton.riskControl.business;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.BasisCreditBusinessEntity;
import com.basiscotton.base.entity.BasisDeliveryTargetEntity;
import com.basiscotton.base.entity.BasisRiskControlEntity;
import com.basiscotton.base.mappers.BasisCreditBusinessMapper;
import com.basiscotton.base.mappers.BasisDeliveryTargetMapper;
import com.basiscotton.base.mappers.BasisRiskControlMapper;
import lombok.extern.slf4j.Slf4j;
import org.harry.dandelion.framework.common.utils.DateUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Title: PricingBeforeDeliveryService.java
 * @Description: 先点价后交割-风险仓单服务
 * 先点价后交割处理服务
 *  买方追保-资金处理
 *      修改追保风险控制状态
 *      修改交割标的风险控制状态、保证金相关字段，提高追保险
 *  买方追保-仓单抵免处理
 *      修改追保风险控制状态
 *      修改交割标的风险控制状态、保证金相关字段，提高追保险
 *  资金替换仓单抵免处理
 *      修改交割标的保证金相关字段，修改仓单冻结字段
 * <AUTHOR>
 * @date 2025/8/26
 * @version V1.0
 */
@Service
@Slf4j
public class PricingBeforeDeliveryService {

    @Resource
    private BasisRiskControlMapper basisRiskControlMapper;

    @Resource
    private BasisDeliveryTargetMapper basisDeliveryTargetMapper;

    @Resource
    private BasisCreditBusinessMapper basisCreditBusinessMapper;

    //买方追保-资金处理
    public void buyerMarginCallCapitalHandle(String targetId) {
        log.debug("先点价后交割-开始执行买方追保-资金处理。交割标的ID{}", targetId);
        BasisDeliveryTargetEntity target = basisDeliveryTargetMapper.selectById(targetId);

        LambdaQueryWrapper<BasisRiskControlEntity> queryRiskControl = new LambdaQueryWrapper<BasisRiskControlEntity>();
        queryRiskControl.eq(BasisRiskControlEntity::getTargetId, targetId);
        queryRiskControl.eq(BasisRiskControlEntity::getDeliveryType, BasisCottonConstants.DELIVERY_TYPE_2);
        queryRiskControl.eq(BasisRiskControlEntity::getFundsType, BasisCottonConstants.funds_type_2);
        queryRiskControl.eq(BasisRiskControlEntity::getRiskControlStatus, BasisCottonConstants.risk_control_status_1);
        BasisRiskControlEntity riskControl = basisRiskControlMapper.selectOne(queryRiskControl);
        //修改风险控制状态
        LambdaUpdateWrapper<BasisRiskControlEntity> updateWrapper = new LambdaUpdateWrapper<BasisRiskControlEntity>();
        updateWrapper.eq(BasisRiskControlEntity::getId, riskControl.getId());
        updateWrapper.set(BasisRiskControlEntity::getRiskControlStatus, BasisCottonConstants.risk_control_status_2);
        updateWrapper.set(BasisRiskControlEntity::getMarginCallTime, DateUtils.getDate());
        basisRiskControlMapper.update(null, updateWrapper);
        //修改交割标的风险控制状态、保证金相关字段
        BigDecimal marginCallLine = target.getBuyerBasisMarginCallLine();
        BigDecimal marginCallStandard = target.getBuyerBasisMarginCallStandard();
        BigDecimal marginCallAmount = target.getBuyerBasisMarginCallAmount();

        BigDecimal newMarginCallLine = marginCallLine.add(marginCallStandard);
        BigDecimal newMarginCallAmount = marginCallAmount.add(riskControl.getMarginCallAmount());

        LambdaUpdateWrapper<BasisDeliveryTargetEntity> updateTarget = new LambdaUpdateWrapper<BasisDeliveryTargetEntity>();
        updateTarget.eq(BasisDeliveryTargetEntity::getId, riskControl.getTargetId());
        updateTarget.set(BasisDeliveryTargetEntity::getTargetRiskControlType, BasisCottonConstants.target_risk_control_type_1);
        updateTarget.set(BasisDeliveryTargetEntity::getBasisRiskControlStatus, BasisCottonConstants.basis_risk_control_status_1);
        updateTarget.set(BasisDeliveryTargetEntity::getPricingRiskControlStatus, BasisCottonConstants.pricing_risk_control_status_4);
        updateTarget.set(BasisDeliveryTargetEntity::getRiskControlTime, DateUtils.getDate());
        updateTarget.set(BasisDeliveryTargetEntity::getBuyerBasisMarginCallLine, newMarginCallLine);
        updateTarget.set(BasisDeliveryTargetEntity::getBuyerBasisMarginCallAmount, newMarginCallAmount);
        basisDeliveryTargetMapper.update(null, updateTarget);
        log.debug("先点价后交割-结束执行买方追保-资金处理。");
    }

    //买方追保-仓单抵免处理
    public void buyerMarginCallWarehouseReceiptHandle(String targetId, String whsNo, Integer creditNum) {
        log.debug("先点价后交割-开始执行买方追保-仓单抵免处理。交割标的ID{}，仓单号{}，抵免次数{}", targetId, whsNo, creditNum);
        BasisDeliveryTargetEntity target = basisDeliveryTargetMapper.selectById(targetId);

        LambdaQueryWrapper<BasisRiskControlEntity> queryRiskControl = new LambdaQueryWrapper<BasisRiskControlEntity>();
        queryRiskControl.eq(BasisRiskControlEntity::getTargetId, targetId);
        queryRiskControl.eq(BasisRiskControlEntity::getDeliveryType, BasisCottonConstants.DELIVERY_TYPE_2);
        queryRiskControl.eq(BasisRiskControlEntity::getFundsType, BasisCottonConstants.funds_type_2);
        queryRiskControl.eq(BasisRiskControlEntity::getRiskControlStatus, BasisCottonConstants.risk_control_status_1);
        BasisRiskControlEntity riskControl = basisRiskControlMapper.selectOne(queryRiskControl);
        //修改风险控制状态
        LambdaUpdateWrapper<BasisRiskControlEntity> updateWrapper = new LambdaUpdateWrapper<BasisRiskControlEntity>();
        updateWrapper.eq(BasisRiskControlEntity::getId, riskControl.getId());
        updateWrapper.set(BasisRiskControlEntity::getRiskControlStatus, BasisCottonConstants.risk_control_status_2);
        updateWrapper.set(BasisRiskControlEntity::getMarginCallTime, DateUtils.getDate());
        basisRiskControlMapper.update(null, updateWrapper);
        //修改交割标的风险控制状态、保证金相关字段
        BigDecimal marginCallLine = target.getBuyerBasisMarginCallLine();
        BigDecimal marginCallStandard = target.getBuyerBasisMarginCallStandard();
        Integer targetCreditNum = target.getBuyerCreditNum();
        BigDecimal creditAmount = target.getBuyerCreditAmount();

        BigDecimal newMarginCallLine = marginCallLine.add(marginCallStandard);
        Integer newCreditNum = targetCreditNum+creditNum;
        BigDecimal newCreditAmount = creditAmount.add(riskControl.getMarginCallAmount());

        LambdaUpdateWrapper<BasisDeliveryTargetEntity> updateTarget = new LambdaUpdateWrapper<BasisDeliveryTargetEntity>();
        updateTarget.eq(BasisDeliveryTargetEntity::getId, riskControl.getTargetId());
        updateTarget.set(BasisDeliveryTargetEntity::getTargetRiskControlType, BasisCottonConstants.target_risk_control_type_1);
        updateTarget.set(BasisDeliveryTargetEntity::getBasisRiskControlStatus, BasisCottonConstants.basis_risk_control_status_1);
        updateTarget.set(BasisDeliveryTargetEntity::getPricingRiskControlStatus, BasisCottonConstants.pricing_risk_control_status_4);
        updateTarget.set(BasisDeliveryTargetEntity::getRiskControlTime, DateUtils.getDate());
        updateTarget.set(BasisDeliveryTargetEntity::getBuyerBasisMarginCallLine, newMarginCallLine);
        updateTarget.set(BasisDeliveryTargetEntity::getBuyerCreditNum, newCreditNum);
        updateTarget.set(BasisDeliveryTargetEntity::getBuyerCreditAmount, newCreditAmount);
        basisDeliveryTargetMapper.update(null, updateTarget);
        log.debug("先点价后交割-结束执行买方追保-仓单抵免处理。");
    }

    //取消买方仓单抵免次数处理
    public void canalBuyerWarehouseReceiptCreditHandle(String targetId) {
        log.debug("先点价后交割-开始执行取消买方仓单抵免次数处理。交割标的id{}", targetId);
        //修改交割标的风险控制状态、保证金相关字段
        BasisDeliveryTargetEntity target = basisDeliveryTargetMapper.selectById(targetId);

        BigDecimal buyerCreditAmount = target.getBuyerCreditAmount();
        BigDecimal marginCallAmount = target.getBuyerBasisMarginCallAmount();

        BigDecimal newMarginCallAmount = marginCallAmount.add(buyerCreditAmount);

        LambdaUpdateWrapper<BasisDeliveryTargetEntity> updateTarget = new LambdaUpdateWrapper<BasisDeliveryTargetEntity>();
        updateTarget.eq(BasisDeliveryTargetEntity::getId, targetId);
        updateTarget.set(BasisDeliveryTargetEntity::getBuyerCreditNum, 0);
        updateTarget.set(BasisDeliveryTargetEntity::getBuyerCreditAmount, BigDecimal.ZERO);
        updateTarget.set(BasisDeliveryTargetEntity::getBuyerBasisMarginCallAmount, newMarginCallAmount);
        basisDeliveryTargetMapper.update(null, updateTarget);

        LambdaQueryWrapper<BasisCreditBusinessEntity> queryWrapper = new LambdaQueryWrapper<BasisCreditBusinessEntity>();
        queryWrapper.eq(BasisCreditBusinessEntity::getBusinessId, targetId);
        queryWrapper.eq(BasisCreditBusinessEntity::getCreditType, BasisCottonConstants.credit_type_2);
        queryWrapper.eq(BasisCreditBusinessEntity::getFundsType, BasisCottonConstants.funds_type_2);
        queryWrapper.eq(BasisCreditBusinessEntity::getBusinessCreditStatus, BasisCottonConstants.business_credit_status_1);
        List<BasisCreditBusinessEntity> creditBusinessList = basisCreditBusinessMapper.selectList(queryWrapper);

        List<Long> creditBusinessIdList = creditBusinessList.stream().map(BasisCreditBusinessEntity::getId).collect(Collectors.toList());
        LambdaUpdateWrapper<BasisCreditBusinessEntity> updateCredit = new LambdaUpdateWrapper<BasisCreditBusinessEntity>();
        updateCredit.in(BasisCreditBusinessEntity::getId, creditBusinessIdList);
        updateCredit.set(BasisCreditBusinessEntity::getBusinessCreditStatus, BasisCottonConstants.business_credit_status_2);
        basisCreditBusinessMapper.update(null, updateCredit);
        log.debug("先点价后交割-结束执行取消买方仓单抵免次数处理。");
    }

}
