package com.basiscotton.riskControl.business;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.BasisDeliveryTargetEntity;
import com.basiscotton.base.entity.BasisRiskControlEntity;
import com.basiscotton.base.mappers.BasisDeliveryTargetMapper;
import com.basiscotton.base.mappers.BasisRiskControlMapper;
import com.basiscotton.futurequotes.Queues.RiskControlFutureQuoteQueue;
import com.basiscotton.futurequotes.vo.RealTimeQuoteVo;
import com.sinosoft.dandelion.system.client.params.DataItem;
import lombok.extern.slf4j.Slf4j;
import org.harry.dandelion.framework.common.utils.DateUtils;
import org.harry.dandelion.framework.id.core.service.BizIdGenerator;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Title: DeliveryBeforePricingRiskMonitor.java
 * @Description:
 * 先交割后点价风控监视类
 *  问题点
 *      强平是否存在指令中数据，是否存在指令锁控制？风险数据是否锁定？
 *      追保中数据这时触发强平，是修改追保数据增加强平数据？还是原追保数据修改？
 * <AUTHOR>
 * @date 2025/8/25
 * @version V1.0
 */
@Service
@Slf4j
public class DeliveryBeforePricingRiskMonitor {
    @Resource
    private RiskControlFutureQuoteQueue riskControlFutureQuoteQueue;
    @Resource
    private BasisDeliveryTargetMapper deliveryTargetMapper;
    @Resource
    private BasisRiskControlMapper basisRiskControlMapper;

    /**
     *  买方追保
     *
     *
     */
    public void buyerPricingPriceRisk_marginCall() {
        try {
            log.debug("开始执行点价价格风控-追保");
            //从数据库取数 条件为未点价完成,不违约的先交割后点价标的
            LambdaQueryWrapper<BasisDeliveryTargetEntity> queryWrapper = new LambdaQueryWrapper<BasisDeliveryTargetEntity>();
            queryWrapper.notIn(BasisDeliveryTargetEntity::getBatchDeliveryStatus, BasisCottonConstants.delivery_status_5, BasisCottonConstants.delivery_status_6);
            queryWrapper.ne(BasisDeliveryTargetEntity::getPricingStatus, BasisCottonConstants.pricing_status_3);
            queryWrapper.eq(BasisDeliveryTargetEntity::getDeliveryType, BasisCottonConstants.DELIVERY_TYPE_2);
            List<BasisDeliveryTargetEntity> deliveryTargetList = deliveryTargetMapper.selectList(queryWrapper);
            Map<String, List<BasisDeliveryTargetEntity>> deliveryTargetMap = deliveryTargetList.stream().collect(Collectors.groupingBy(BasisDeliveryTargetEntity::getFutureCode));
            while (true) {
                // 从尾部取数据，如果队列为空立即返回null
                RealTimeQuoteVo realTimeQuote = riskControlFutureQuoteQueue.pollLast();
                String futureCode = realTimeQuote.getFutureCode();
                //获取对应期货合约标的数据
                List<BasisDeliveryTargetEntity> currTargetList = deliveryTargetMap.get(futureCode);
                if (currTargetList != null && currTargetList.size() > 0) {
                    for (BasisDeliveryTargetEntity deliveryTarget : deliveryTargetList) {
                        BigDecimal marginCallLine = deliveryTarget.getBuyerPricingMarginCallLine();
                        BigDecimal marginCallStandard = deliveryTarget.getBuyerPricingMarginCallStandard();
                        //当前期货价格  > 买方追保线
                        BigDecimal difference = realTimeQuote.getLastPrice().subtract(marginCallLine);
                        log.info("{} 的当前期货价格：{}，暂定期货价格：{}，差额{}", futureCode, realTimeQuote.getLastPrice(),marginCallLine,difference);
                        if(difference.compareTo(BigDecimal.ZERO)>0){
                            LambdaUpdateWrapper<BasisDeliveryTargetEntity> targetUpdateWrapper = new LambdaUpdateWrapper<BasisDeliveryTargetEntity>();
                            targetUpdateWrapper.eq(BasisDeliveryTargetEntity::getId, deliveryTarget.getId());
                            targetUpdateWrapper.set(BasisDeliveryTargetEntity::getTargetRiskControlType, BasisCottonConstants.target_risk_control_type_2);
                            targetUpdateWrapper.set(BasisDeliveryTargetEntity::getBasisRiskControlStatus, BasisCottonConstants.basis_risk_control_status_3);
                            targetUpdateWrapper.set(BasisDeliveryTargetEntity::getPricingRiskControlStatus, BasisCottonConstants.pricing_risk_control_status_4);
                            targetUpdateWrapper.set(BasisDeliveryTargetEntity::getRiskControlTime, DateUtils.getDate());
                            deliveryTargetMapper.update(null, targetUpdateWrapper);
                            //产生风控记录
                            BigDecimal marginCallStandardAmount = marginCallLine.multiply(marginCallStandard.divide(BigDecimal.valueOf(100))).setScale(2, RoundingMode.HALF_UP);

                            BasisRiskControlEntity riskControlEntity = new BasisRiskControlEntity();
                            Long riskControlId = BizIdGenerator.getInstance().generateBizId();
                            riskControlEntity.setId(riskControlId);
                            riskControlEntity.setTransactionNo(deliveryTarget.getTransactionNo());
                            riskControlEntity.setContractId(deliveryTarget.getContractId());
                            riskControlEntity.setTargetId(deliveryTarget.getId());
                            riskControlEntity.setStockId(deliveryTarget.getStockId());
                            riskControlEntity.setStockBaseInfoId(deliveryTarget.getStockBaseInfoId());
                            riskControlEntity.setWarehouseReceiptNo(deliveryTarget.getWarehouseReceiptNo());
                            riskControlEntity.setBatchNo(deliveryTarget.getBatchNo());
                            riskControlEntity.setDeliveryType(deliveryTarget.getDeliveryType());
                            riskControlEntity.setFundsType(new DataItem(BasisCottonConstants.funds_type_2,""));
                            riskControlEntity.setPaymentTraderCode(deliveryTarget.getBuyerTraderCode());
                            riskControlEntity.setPaymentTraderName(deliveryTarget.getBuyerTraderName());
                            riskControlEntity.setMarginCallStandard(marginCallStandardAmount);
                            riskControlEntity.setMarginCallAmount(marginCallStandardAmount.multiply(deliveryTarget.getTradeWeight()).setScale(2, RoundingMode.HALF_UP));
                            riskControlEntity.setRiskControlStatus(new DataItem(BasisCottonConstants.risk_control_status_1,""));
                            basisRiskControlMapper.insert(riskControlEntity);
                        }
                    }
                }
            }
        }catch (Exception e){
            log.error("点价价格风险测算异常-追保", e);
        }
        log.debug("结束执行点价价格风控-追保");
    }


    /**
     *  买方强平
     *
     *
     */
    public void buyerPricingPriceRisk_liquidation() {
        try {
            log.debug("开始执行点价价格风控-买方强平");
            //从数据库取数 条件为未点价完成,不违约的先交割后点价标的
            LambdaQueryWrapper<BasisDeliveryTargetEntity> queryWrapper = new LambdaQueryWrapper<BasisDeliveryTargetEntity>();
            queryWrapper.notIn(BasisDeliveryTargetEntity::getBatchDeliveryStatus, BasisCottonConstants.delivery_status_5, BasisCottonConstants.delivery_status_6);
            queryWrapper.ne(BasisDeliveryTargetEntity::getPricingStatus, BasisCottonConstants.pricing_status_3);
            queryWrapper.eq(BasisDeliveryTargetEntity::getDeliveryType, BasisCottonConstants.DELIVERY_TYPE_2);
            List<BasisDeliveryTargetEntity> deliveryTargetList = deliveryTargetMapper.selectList(queryWrapper);
            Map<String, List<BasisDeliveryTargetEntity>> deliveryTargetMap = deliveryTargetList.stream().collect(Collectors.groupingBy(BasisDeliveryTargetEntity::getFutureCode));
            while (true) {
                // 从尾部取数据，如果队列为空立即返回null
                RealTimeQuoteVo realTimeQuote = riskControlFutureQuoteQueue.pollLast();
                String futureCode = realTimeQuote.getFutureCode();
                //获取对应期货合约标的数据
                List<BasisDeliveryTargetEntity> currTargetList = deliveryTargetMap.get(futureCode);
                if (currTargetList != null && currTargetList.size() > 0) {
                    for (BasisDeliveryTargetEntity deliveryTarget : deliveryTargetList) {
                        BigDecimal liquidationLine = deliveryTarget.getBuyerPricingForcedLiquidationLine();
                        //当前期货价格 > 买方强平线
                        BigDecimal difference = realTimeQuote.getLastPrice().subtract(liquidationLine);
                        log.info("{} 的当前期货价格：{}，买方强平线价格：{}，差额{}", futureCode, realTimeQuote.getLastPrice(),liquidationLine,difference);
                        if(difference.compareTo(BigDecimal.ZERO)>0){
                            LambdaQueryWrapper<BasisRiskControlEntity> queryRiskControl = new LambdaQueryWrapper<BasisRiskControlEntity>();
                            queryRiskControl.eq(BasisRiskControlEntity::getTargetId, deliveryTarget.getId());
                            queryRiskControl.eq(BasisRiskControlEntity::getDeliveryType, BasisCottonConstants.DELIVERY_TYPE_2);
                            queryRiskControl.eq(BasisRiskControlEntity::getFundsType, BasisCottonConstants.funds_type_2);
                            queryRiskControl.eq(BasisRiskControlEntity::getRiskControlStatus, BasisCottonConstants.risk_control_status_1);
                            BasisRiskControlEntity riskControl = basisRiskControlMapper.selectOne(queryRiskControl);

                            //修改风险控制状态
                            LambdaUpdateWrapper<BasisRiskControlEntity> updateWrapper = new LambdaUpdateWrapper<BasisRiskControlEntity>();
                            updateWrapper.eq(BasisRiskControlEntity::getId, riskControl.getId());
                            updateWrapper.set(BasisRiskControlEntity::getRiskControlStatus, BasisCottonConstants.risk_control_status_3);
                            updateWrapper.set(BasisRiskControlEntity::getLiquidationPrice, realTimeQuote.getLastPrice());
                            updateWrapper.set(BasisRiskControlEntity::getLiquidationTime, DateUtils.getDate());
                            basisRiskControlMapper.update(null, updateWrapper);

                            //修改交割标的风险控制状态、保证金相关字段
                            LambdaUpdateWrapper<BasisDeliveryTargetEntity> updateTarget = new LambdaUpdateWrapper<BasisDeliveryTargetEntity>();
                            updateTarget.eq(BasisDeliveryTargetEntity::getId, deliveryTarget.getId());
                            updateTarget.set(BasisDeliveryTargetEntity::getPricingStatus, BasisCottonConstants.pricing_status_3);
                            updateTarget.set(BasisDeliveryTargetEntity::getPricingFrom, BasisCottonConstants.pricing_From_4);
                            updateTarget.set(BasisDeliveryTargetEntity::getPricingTime, DateUtils.getDate());
                            updateTarget.set(BasisDeliveryTargetEntity::getFinalSettlementPrice, realTimeQuote.getLastPrice());
                            updateTarget.set(BasisDeliveryTargetEntity::getTargetRiskControlType, BasisCottonConstants.target_risk_control_type_1);
                            updateTarget.set(BasisDeliveryTargetEntity::getBasisRiskControlStatus, BasisCottonConstants.basis_risk_control_status_1);
                            updateTarget.set(BasisDeliveryTargetEntity::getPricingRiskControlStatus, BasisCottonConstants.pricing_risk_control_status_5);
                            updateTarget.set(BasisDeliveryTargetEntity::getRiskControlTime, DateUtils.getDate());
                            deliveryTargetMapper.update(null, updateTarget);
                        }
                    }
                }
            }
        }catch (Exception e){
            log.error("点价价格风险测算异常-买方强平", e);
        }
        log.debug("结束执行点价价格风控-买方强平");
    }

    /**
     *  卖方强平
     *
     *
     */
    public void sellerPricingPriceRisk_liquidation() {
        try {
            log.debug("开始执行点价价格风控-卖方强平");
            //从数据库取数 条件为未点价完成,不违约的先交割后点价标的
            LambdaQueryWrapper<BasisDeliveryTargetEntity> queryWrapper = new LambdaQueryWrapper<BasisDeliveryTargetEntity>();
            queryWrapper.notIn(BasisDeliveryTargetEntity::getBatchDeliveryStatus, BasisCottonConstants.delivery_status_5, BasisCottonConstants.delivery_status_6);
            queryWrapper.ne(BasisDeliveryTargetEntity::getPricingStatus, BasisCottonConstants.pricing_status_3);
            queryWrapper.eq(BasisDeliveryTargetEntity::getDeliveryType, BasisCottonConstants.DELIVERY_TYPE_2);
            List<BasisDeliveryTargetEntity> deliveryTargetList = deliveryTargetMapper.selectList(queryWrapper);
            Map<String, List<BasisDeliveryTargetEntity>> deliveryTargetMap = deliveryTargetList.stream().collect(Collectors.groupingBy(BasisDeliveryTargetEntity::getFutureCode));
            while (true) {
                // 从尾部取数据，如果队列为空立即返回null
                RealTimeQuoteVo realTimeQuote = riskControlFutureQuoteQueue.pollLast();
                String futureCode = realTimeQuote.getFutureCode();
                //获取对应期货合约标的数据
                List<BasisDeliveryTargetEntity> currTargetList = deliveryTargetMap.get(futureCode);
                if (currTargetList != null && currTargetList.size() > 0) {
                    for (BasisDeliveryTargetEntity deliveryTarget : deliveryTargetList) {
                        BigDecimal liquidationLine = deliveryTarget.getSellerPricingForcedLiquidationLine();
                        //买方强平线 > 当前期货价格
                        BigDecimal difference = liquidationLine.subtract(realTimeQuote.getLastPrice());
                        log.info("{} 的当前期货价格：{}，卖方强平线价格：{}，差额{}", futureCode, realTimeQuote.getLastPrice(),liquidationLine,difference);
                        if(difference.compareTo(BigDecimal.ZERO)>0){
                            LambdaQueryWrapper<BasisRiskControlEntity> queryRiskControl = new LambdaQueryWrapper<BasisRiskControlEntity>();
                            queryRiskControl.eq(BasisRiskControlEntity::getTargetId, deliveryTarget.getId());
                            queryRiskControl.eq(BasisRiskControlEntity::getDeliveryType, BasisCottonConstants.DELIVERY_TYPE_2);
                            queryRiskControl.eq(BasisRiskControlEntity::getFundsType, BasisCottonConstants.funds_type_2);
                            queryRiskControl.eq(BasisRiskControlEntity::getRiskControlStatus, BasisCottonConstants.risk_control_status_1);
                            BasisRiskControlEntity riskControl = basisRiskControlMapper.selectOne(queryRiskControl);
                            //修改风险控制状态
                            LambdaUpdateWrapper<BasisRiskControlEntity> updateWrapper = new LambdaUpdateWrapper<BasisRiskControlEntity>();
                            updateWrapper.eq(BasisRiskControlEntity::getId, riskControl.getId());
                            updateWrapper.set(BasisRiskControlEntity::getRiskControlStatus, BasisCottonConstants.risk_control_status_3);
                            updateWrapper.set(BasisRiskControlEntity::getLiquidationPrice, realTimeQuote.getLastPrice());
                            updateWrapper.set(BasisRiskControlEntity::getLiquidationTime, DateUtils.getDate());
                            basisRiskControlMapper.update(null, updateWrapper);
                            //修改交割标的风险控制状态、保证金相关字段
                            LambdaUpdateWrapper<BasisDeliveryTargetEntity> updateTarget = new LambdaUpdateWrapper<BasisDeliveryTargetEntity>();
                            updateTarget.eq(BasisDeliveryTargetEntity::getId, deliveryTarget.getId());
                            updateTarget.set(BasisDeliveryTargetEntity::getPricingStatus, BasisCottonConstants.pricing_status_3);
                            updateTarget.set(BasisDeliveryTargetEntity::getPricingFrom, BasisCottonConstants.pricing_From_4);
                            updateTarget.set(BasisDeliveryTargetEntity::getPricingTime, DateUtils.getDate());
                            updateTarget.set(BasisDeliveryTargetEntity::getFinalSettlementPrice, realTimeQuote.getLastPrice());
                            updateTarget.set(BasisDeliveryTargetEntity::getTargetRiskControlType, BasisCottonConstants.target_risk_control_type_1);
                            updateTarget.set(BasisDeliveryTargetEntity::getBasisRiskControlStatus, BasisCottonConstants.basis_risk_control_status_1);
                            updateTarget.set(BasisDeliveryTargetEntity::getPricingRiskControlStatus, BasisCottonConstants.pricing_risk_control_status_3);
                            updateTarget.set(BasisDeliveryTargetEntity::getRiskControlTime, DateUtils.getDate());
                            deliveryTargetMapper.update(null, updateTarget);
                        }
                    }
                }
            }
        }catch (Exception e){
            log.error("点价价格风险测算异常-卖方强平", e);
        }
        log.debug("结束执行点价价格风控-卖方强平");
    }
}
