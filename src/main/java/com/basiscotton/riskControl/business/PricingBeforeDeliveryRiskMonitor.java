package com.basiscotton.riskControl.business;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.BasisDeliveryTargetEntity;
import com.basiscotton.base.entity.BasisRiskControlEntity;
import com.basiscotton.base.mappers.BasisDeliveryTargetMapper;
import com.basiscotton.base.mappers.BasisMarketSettingMapper;
import com.basiscotton.base.mappers.BasisRiskControlMapper;
import com.basiscotton.futurequotes.vo.RealTimeQuoteVo;
import com.basiscotton.riskControl.vo.PriceIndexVo;
import com.github.benmanes.caffeine.cache.Cache;
import com.sinosoft.dandelion.system.client.params.DataItem;
import lombok.extern.slf4j.Slf4j;
import org.harry.dandelion.framework.common.utils.DateUtils;
import org.harry.dandelion.framework.id.core.service.BizIdGenerator;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: PricingBeforeDeliveryRiskMonitor.java
 * @Description: 先点价后交割风控监视类
 * @date 2025/8/23 17:56
 */
@Service
@Slf4j
public class PricingBeforeDeliveryRiskMonitor {
    @Resource
    private BasisDeliveryTargetMapper deliveryTargetMapper;
    @Resource
    private BasisMarketSettingMapper basisMarketSettingMapper;
    @Resource
    private Cache<String, RealTimeQuoteVo> futureQuotesCache;
    @Resource
    private BasisRiskControlMapper basisRiskControlMapper;

    /**
     * 针对买方：基差价格风险测算--先点价后交割，以现货稳定为基础假设，期货上涨时，基差缩小，买方基差价格风险出现。
     * 针对未点价完成、未违约的交割标的，以基差成交日为起点，监测每批对应期货品种的当日结算价与3128B的细绒棉现货价格的差值，如果差值超过阈值，则进行预警。
     * 当前阈值为700；
     * 如果补充了保证金，如何能在下次判断中忽略该标的？看5日内已有对应的风控记录产生？或者补充了保证金，在数值上对比大于当前基准基差（现减期）
     */
    public void basisPriceRiskAssessment() {

        try {
            log.info("开始执行基差价格风控");
            // 获取价格指数
            PriceIndexVo priceIndexVo = basisMarketSettingMapper.selectPriceIndex();
            // 计算出6个期货品种的当前基差
            Map<String, BigDecimal> basisPriceMap = new HashMap<>();
            // 循环futureQuotesCache
            for (Map.Entry<String, RealTimeQuoteVo> entry : futureQuotesCache.asMap().entrySet()) {
                RealTimeQuoteVo quote = entry.getValue();
                BigDecimal basisPrice = quote.getOpenPrice().subtract(priceIndexVo.getCottonIndexPrice());
                basisPriceMap.put(quote.getFutureCode(), basisPrice);
                log.info("{} 的当前基差基准为：{}", quote.getFutureCode(), basisPrice);
            }

            //从数据库取数 条件为未点价完成,不违约的先点价后交割标的
            LambdaQueryWrapper<BasisDeliveryTargetEntity> queryWrapper = new LambdaQueryWrapper<BasisDeliveryTargetEntity>();
            queryWrapper.in(BasisDeliveryTargetEntity::getPricingStatus, BasisCottonConstants.pricing_status_1, BasisCottonConstants.pricing_status_2);
            queryWrapper.ne(BasisDeliveryTargetEntity::getPricingStatus, BasisCottonConstants.pricing_status_3);
            queryWrapper.eq(BasisDeliveryTargetEntity::getDeliveryType, BasisCottonConstants.DELIVERY_TYPE_1);
            List<BasisDeliveryTargetEntity> deliveryTargetList = deliveryTargetMapper.selectList(queryWrapper);
            for (BasisDeliveryTargetEntity deliveryTarget : deliveryTargetList) {
                BigDecimal marginCallLine = deliveryTarget.getBuyerBasisMarginCallLine();
                BigDecimal marginCallStandard = deliveryTarget.getBuyerBasisMarginCallStandard();
                //细绒棉3128B最新价格指数 - 每批关联合约的结算价  > 批次买方基差追保线
                BigDecimal difference = basisPriceMap.get(deliveryTarget.getFutureCode()).subtract(marginCallLine);
                if(difference.compareTo(BigDecimal.ZERO)>0){
                    LambdaUpdateWrapper<BasisDeliveryTargetEntity> targetUpdateWrapper = new LambdaUpdateWrapper<BasisDeliveryTargetEntity>();
                    targetUpdateWrapper.eq(BasisDeliveryTargetEntity::getId, deliveryTarget.getId());
                    targetUpdateWrapper.set(BasisDeliveryTargetEntity::getTargetRiskControlType, BasisCottonConstants.target_risk_control_type_1);
                    targetUpdateWrapper.set(BasisDeliveryTargetEntity::getBasisRiskControlStatus, BasisCottonConstants.basis_risk_control_status_3);
                    targetUpdateWrapper.set(BasisDeliveryTargetEntity::getPricingRiskControlStatus, BasisCottonConstants.pricing_risk_control_status_4);
                    targetUpdateWrapper.set(BasisDeliveryTargetEntity::getRiskControlTime, DateUtils.getDate());
                    deliveryTargetMapper.update(null, targetUpdateWrapper);
                    //产生风控记录
                    BasisRiskControlEntity riskControlEntity = new BasisRiskControlEntity();
                    Long riskControlId = BizIdGenerator.getInstance().generateBizId();
                    riskControlEntity.setId(riskControlId);
                    riskControlEntity.setTransactionNo(deliveryTarget.getTransactionNo());
                    riskControlEntity.setContractId(deliveryTarget.getContractId());
                    riskControlEntity.setTargetId(deliveryTarget.getId());
                    riskControlEntity.setStockId(deliveryTarget.getStockId());
                    riskControlEntity.setStockBaseInfoId(deliveryTarget.getStockBaseInfoId());
                    riskControlEntity.setWarehouseReceiptNo(deliveryTarget.getWarehouseReceiptNo());
                    riskControlEntity.setBatchNo(deliveryTarget.getBatchNo());
                    riskControlEntity.setDeliveryType(deliveryTarget.getDeliveryType());
                    riskControlEntity.setFundsType(new DataItem(BasisCottonConstants.funds_type_1,""));
                    riskControlEntity.setPaymentTraderCode(deliveryTarget.getBuyerTraderCode());
                    riskControlEntity.setPaymentTraderName(deliveryTarget.getBuyerTraderName());
                    riskControlEntity.setMarginCallStandard(marginCallStandard);
                    riskControlEntity.setMarginCallAmount(marginCallStandard.multiply(deliveryTarget.getTradeWeight()).setScale(2, RoundingMode.HALF_UP));
                    riskControlEntity.setRiskControlStatus(new DataItem(BasisCottonConstants.risk_control_status_1,""));
                    basisRiskControlMapper.insert(riskControlEntity);
                }

            }
        }catch (Exception e){
            log.error("基差价格风险测算异常", e);
        }
    }

}
