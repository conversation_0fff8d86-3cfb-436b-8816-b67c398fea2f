package com.basiscotton.tradingcore.dto;

import com.basiscotton.base.enums.TradeMode;
import lombok.Builder;
import lombok.Value;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Objects;

@Value
@Builder
public class ChangeStockPriceDTO implements TradingDTO, QuoteDTO {

    String stockId;

    String traderCode;

    String traderName;

    BigDecimal stockPrice;

    LocalDate validityEnd;

    TradeMode tradeMode;

    @Override
    public void check() {
        Objects.requireNonNull(stockId, "商品 ID 不能为空");
        Objects.requireNonNull(traderCode, "交易员代码不能为空");
        Objects.requireNonNull(stockPrice, "商品价格不能为空");
        Objects.requireNonNull(validityEnd, "商品有效期不能为空");
        Objects.requireNonNull(tradeMode, "交易模式不能为空");
    }

    @Override
    public BigDecimal getQuotePrice() {
        return stockPrice;
    }
}
