package com.basiscotton.tradingcore.dto;

import com.basiscotton.base.enums.TradeMode;
import lombok.Builder;
import lombok.Value;

import java.util.Objects;

@Value
@Builder
public class BuyerCancelQuoteDTO implements TradingDTO {
    /**
     * 商品 ID
     */
    String stockId;
    /**
     * 报价 ID
     */
    String quoteId;
    /**
     * 买家客户代码
     */
    String buyerCustomerCode;
    /**
     * 买家客户名称
     */
    String buyerCustomerName;

    /**
     * 交易模式
     */
    TradeMode tradeMode;

    @Override
    public void check() {
        Objects.requireNonNull(stockId, "商品 ID 不能为空");
        Objects.requireNonNull(quoteId, "报价 ID 不能为空");
        Objects.requireNonNull(buyerCustomerCode, "买家客户代码不能为空");
        Objects.requireNonNull(tradeMode, "交易模式不能为空");
    }

    @Override
    public Boolean validWarehouseReceiptStatus() {
        return false;
    }
}
