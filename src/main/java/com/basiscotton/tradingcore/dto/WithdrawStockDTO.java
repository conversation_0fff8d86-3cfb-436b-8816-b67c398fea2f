package com.basiscotton.tradingcore.dto;

import com.basiscotton.base.enums.StockCancelType;
import com.basiscotton.base.enums.TradeMode;
import lombok.Builder;
import lombok.Value;

import java.util.Objects;

@Value
@Builder
public class WithdrawStockDTO implements TradingDTO {
    /**
     * 商品 ID
     */
    String stockId;
    /**
     * 操作员 ID
     */
    String operatorId;
    /**
     * 操作员名称
     */
    String operatorName;

    /**
     * 撤销类型
     */
    StockCancelType cancelType;

    /**
     * 交易模式
     */
    TradeMode tradeMode;

    @Override
    public void check() {
        Objects.requireNonNull(stockId, "商品 ID 不能为空");
        Objects.requireNonNull(cancelType, "撤销类型不能为空");
        Objects.requireNonNull(tradeMode, "交易模式不能为空");
    }

    @Override
    public Boolean validWarehouseReceiptStatus() {
        return false;
    }
}
