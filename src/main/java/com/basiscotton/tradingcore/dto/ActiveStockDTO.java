package com.basiscotton.tradingcore.dto;

import com.basiscotton.base.enums.TradeMode;
import lombok.Builder;
import lombok.Value;

import java.util.Objects;

@Value
@Builder
public class ActiveStockDTO implements TradingDTO {

    String stockId;
    String operatorId;
    String operatorName;
    TradeMode tradeMode;

    @Override
    public void check() {
        Objects.requireNonNull(stockId, "商品 ID 不能为空");
    }
}
