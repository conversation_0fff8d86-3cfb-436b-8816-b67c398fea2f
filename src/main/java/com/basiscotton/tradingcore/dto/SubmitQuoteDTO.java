package com.basiscotton.tradingcore.dto;

import com.basiscotton.base.enums.TradeMode;
import lombok.Builder;
import lombok.Value;

import java.math.BigDecimal;
import java.util.Objects;

@Value
@Builder
public class SubmitQuoteDTO implements TradingDTO, QuoteDTO {

    /**
     * 商品id
     */
    String stockId;

    /**
     * 买家客户代码
     */
    String buyerCustomerCode;

    /**
     * 买家客户名称
     */
    String buyerCustomerName;

    /**
     * 报价价格
     */
    BigDecimal quotePrice;

    /**
     * 交易模式
     */
    TradeMode tradeMode;

    @Override
    public void check() {
        Objects.requireNonNull(stockId, "商品 ID 不能为空");
        Objects.requireNonNull(buyerCustomerCode, "买家客户代码不能为空");
        Objects.requireNonNull(quotePrice, "报价价格不能为空");
        Objects.requireNonNull(tradeMode, "交易模式不能为空");
    }
}
