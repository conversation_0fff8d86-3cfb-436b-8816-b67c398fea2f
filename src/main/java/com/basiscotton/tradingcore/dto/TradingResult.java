package com.basiscotton.tradingcore.dto;

import com.basiscotton.base.entity.BasisDeliveryTargetEntity;
import lombok.Builder;
import lombok.Value;

/**
 * 交易结果
 */
@Value
@Builder
public class TradingResult {
    /**
     * 交易结果
     */
    boolean success;

    /**
     * 撮合成功
     */
    boolean matched;

    /**
     * 是否取消
     */
    boolean canceled;

    /**
     * 系统异常
     */
    boolean systemException;

    /**
     * 撮合成功时, 生成的合同和标的数据
     */
    BasisDeliveryTargetEntity deliveryTarget;

    /**
     * 消息
     */
    String message;

    /**
     * 创建成功结果, 但撮合失败
     *
     * @param message 消息
     */
    public static TradingResult success(String message) {
        return TradingResult.builder()
                .success(true)
                .matched(false)
                .message(message)
                .build();
    }

    /**
     * 操作成功并撮合成功
     *
     * @param message 消息
     * @param matched 是否撮合成功, false 失败, true 成功
     */
    public static TradingResult success(String message, Boolean matched) {
        return TradingResult.builder()
                .success(true)
                .matched(matched)
                .message(message)
                .build();
    }

    /**
     * 操作失败, 业务异常
     *
     * @param message 错误信息
     */
    public static TradingResult failure(String message) {
        return TradingResult.builder()
                .success(false)
                .message(message)
                .build();
    }

    /**
     * 操作失败, 系统异常
     *
     * @param message 错误信息
     */
    public static TradingResult unKnownFailure(String message) {
        return TradingResult.builder()
                .success(false)
                .systemException(true)
                .message(message)
                .build();
    }

    public static TradingResult.TradingResultBuilder buildWith(TradingResult result) {
        return TradingResult.builder()
                .success(result.isSuccess())
                .matched(result.isMatched())
                .systemException(result.isSystemException())
                .deliveryTarget(result.getDeliveryTarget())
                .message(result.getMessage());
    }

    public String getMessage() {
        if (matched) {
            return "摘牌成功";
        }
        return message;
    }
}
