package com.basiscotton.tradingcore.business;

import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.BasisSequenceContants;
import com.basiscotton.base.entity.*;
import com.basiscotton.base.enums.ContractStatus;
import com.basiscotton.base.enums.QuoteCancelType;
import com.basiscotton.base.enums.TradeMode;
import com.basiscotton.base.mappers.BasisContractMapper;
import com.basiscotton.base.mappers.BasisDeliveryTargetMapper;
import com.basiscotton.base.mappers.BasisStockBaseInfoMapper;
import com.basiscotton.base.service.BasisQuoteService;
import com.basiscotton.common.apiservice.IBasisTradeSettlementService;
import com.basiscotton.common.apiservice.dto.HandleMarginDTO;
import lombok.RequiredArgsConstructor;
import org.harry.dandelion.framework.common.utils.DateUtils;
import org.harry.dandelion.framework.sequence.ISequenceFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class MatchProcessor {

    private final BasisQuoteService basisQuoteService;

    private final ISequenceFactory sequenceFactory;

    private final IBasisTradeSettlementService basisTradeSettlementService;

    private final BasisStockBaseInfoMapper basisStockBaseInfoMapper;

    private final BasisContractMapper basisContractMapper;

    private final BasisDeliveryTargetMapper basisDeliveryTargetMapper;

    private static final String TRANSACTION_NO_SEQ = BasisSequenceContants.SEQ_GPSPM;

    public BasisDeliveryTargetEntity processMatch(@Nonnull BasisStockEntity stock, @Nonnull BasisQuoteEntity winQuote,
                                                  @Nonnull QuoteCancelType quoteCancelType, @Nonnull TradeMode tradeMode) {
        // 生成合同和标的数据
        BasisDeliveryTargetEntity target =  generateContractAndTargetData(stock, winQuote, tradeMode);

        // 更新为撤销
        List<BasisQuoteEntity> disposed = basisQuoteService.disposeBuyerQuote(stock.getId().toString(),
                winQuote.getId(),
                quoteCancelType);

        // 释放其它买家交易保证金及手续费
        List<HandleMarginDTO> handleMarginDTOList = disposed.stream()
                .map(HandleMarginDTO.quoteToHandleMarginDTO(tradeMode))
                .collect(Collectors.toList());
        List<HandleMarginDTO> handleTradeFeeDTOList = disposed.stream()
                .map(HandleMarginDTO.quoteToHandleMarginDTOTradeFee(tradeMode))
                .collect(Collectors.toList());
        handleMarginDTOList.addAll(handleTradeFeeDTOList);
        basisTradeSettlementService.freeMargin(handleMarginDTOList);

        return target;
    }

    protected BasisDeliveryTargetEntity generateContractAndTargetData(@Nonnull BasisStockEntity stock, @Nonnull BasisQuoteEntity quote, @Nonnull TradeMode tradeMode) {

        String transactionNo = sequenceFactory.create(TRANSACTION_NO_SEQ).next();
        // 生成合同数据
        Long contractId = null;

        // 只有基差交易保存合同信息
        if (tradeMode == TradeMode.BASIS_PRICING) {
            BasisContractEntity contract = generateContract(stock, quote, transactionNo);
            basisContractMapper.insert(contract);
            contractId = contract.getId();
        }

        // 生成标的数据
        BasisDeliveryTargetEntity target = generateTarget(stock, quote, contractId, transactionNo);
        basisDeliveryTargetMapper.insert(target);
        return target;
    }

    /**
     * 生成合同
     *
     * @param stock 商品信息
     * @param quote 报价信息
     * @return 生成的合同信息
     */
    protected BasisContractEntity generateContract(@Nonnull BasisStockEntity stock, @Nonnull BasisQuoteEntity quote,
                                                   @Nonnull String transactionNo) {
        BasisContractEntity contract = new BasisContractEntity();
        contract.setTransactionNo(transactionNo);
        contract.setSellerTraderCode(stock.getTraderCode());
        contract.setSellerTraderName(stock.getTraderName());
        contract.setBuyerTraderCode(quote.getTraderCode());
        contract.setBuyerTraderName(quote.getTraderName());
        contract.setContractStatus(ContractStatus.UNFINISHED.toDataItem());
        return contract;
    }

    protected BasisDeliveryTargetEntity generateTarget(@Nonnull BasisStockEntity stock, @Nonnull BasisQuoteEntity quote,
                                                       @Nullable Long contractId, @Nonnull String transactionNo) {
        BasisStockBaseInfoEntity baseInfo = basisStockBaseInfoMapper.selectById(stock.getStockBaseInfoId());

        BasisDeliveryTargetEntity target = new BasisDeliveryTargetEntity();
        target.setTransactionNo(transactionNo);
        target.setStockId(stock.getId());
        target.setStockCode(stock.getStockCode());
        target.setContractId(contractId);
        target.setStockBaseInfoId(stock.getStockBaseInfoId());
        target.setQuoteId(quote.getId());
        target.setWarehouseReceiptNo(stock.getWarehouseReceiptNo());
        target.setBatchNo(stock.getBatchNo());
        if(stock.getStockSource().equals(BasisCottonConstants.stock_source_1)){
            target.setSellerBasisMarginAmount(BigDecimal.ZERO);
        }
        if(stock.getStockSource().equals(BasisCottonConstants.stock_source_2)){
            target.setSellerBasisMarginAmount(stock.getSellerFreezeMarginAmount());
        }
        target.setSellerTradeFeeAmount(stock.getSellerTradeFeeStandard().multiply(stock.getStockWeight()));
        target.setSellerTradeFeeType(stock.getSellerTradeFeeType());
        target.setSellerDeliveryFeeAmount(stock.getSellerDeliveryFeeStandard().multiply(stock.getStockWeight()));
        target.setSellerDeliveryFeeType(stock.getSellerDeliveryFeeType());
        target.setSellerTradeFeeCollectPhase(stock.getSellerTradeFeeCollectPhase());
        target.setSellerTraderCode(stock.getTraderCode());
        target.setSellerTraderName(stock.getTraderName());
        target.setStorageWhsCode(baseInfo.getStorageWhsCode());
        target.setStorageWhsName(baseInfo.getStorageWhsName());
        target.setStorageStatus(baseInfo.getStorageStatus());
        target.setWhsSupervisionStatus(baseInfo.getWhsSupervisionStatus());
        target.setSuperviseCode(baseInfo.getSuperviseCode());
        target.setSuperviseName(baseInfo.getSuperviseName());
        target.setWhsPledgeChannel(baseInfo.getWhsPledgeChannel());
        target.setQuoteId(quote.getId());
        target.setBuyerTraderCode(quote.getTraderCode());
        target.setBuyerTraderName(quote.getTraderName());
        target.setBuyerBasisMarginAmount(quote.getBuyerBasisMarginAmount());
        target.setBuyerTradeFeeAmount(quote.getBuyerTradeFeeStandard().multiply(stock.getStockWeight()));
        target.setBuyerTradeFeeType(quote.getBuyerTradeFeeType());
        target.setBuyerDeliveryFeeAmount(quote.getBuyerDeliveryFeeStandard().multiply(stock.getStockWeight()));
        target.setBuyerDeliveryFeeType(quote.getBuyerDeliveryFeeType());
        target.setQuoteType(Integer.parseInt(BasisCottonConstants.quote_type_1));
        target.setFutureCode(stock.getFutureCode());
        target.setTradeBasisPrice(stock.getStockPrice());
        target.setTradeWeight(stock.getStockWeight());
        target.setTradeDate(DateUtils.getDate());
        target.setPricingValidEndTime(stock.getPricingValidTime());
        target.setTradeMode(stock.getTradeMode());
        target.setTransportSubsidyApplyParty(stock.getTransportSubsidyApplyParty());
        return target;
    }
}
