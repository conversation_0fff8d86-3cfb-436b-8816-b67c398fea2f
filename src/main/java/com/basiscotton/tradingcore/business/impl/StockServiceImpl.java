package com.basiscotton.tradingcore.business.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.BasisDeliveryTargetEntity;
import com.basiscotton.base.entity.BasisQuoteEntity;
import com.basiscotton.base.entity.BasisStockEntity;
import com.basiscotton.base.enums.QuoteCancelType;
import com.basiscotton.base.enums.StockCancelType;
import com.basiscotton.base.enums.TradeMode;
import com.basiscotton.base.service.BasisQuoteService;
import com.basiscotton.base.service.BasisStockService;
import com.basiscotton.common.apiservice.IBasisTradeSettlementService;
import com.basiscotton.common.apiservice.IBasisTradeStorageService;
import com.basiscotton.common.apiservice.dto.HandleMarginDTO;
import com.basiscotton.tradingcore.business.*;
import com.basiscotton.tradingcore.dto.*;
import com.cottoneasy.storage.trade.base.vo.response.BaseResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class StockServiceImpl implements StockService {

    private final TradingOperationProcessor tradingOperationProcessor;

    private final BasisStockService basisStockService;

    private final BasisQuoteService basisQuoteService;

    private final IBasisTradeSettlementService basisTradeSettlementService;

    private final IBasisTradeStorageService basisTradeStorageService;

    private final MatchProcessor matchProcessor;

    private final TradingStatusHelper tradingStatusHelper;

    @Override
    public TradingResult activeStock(@Nonnull ActiveStockDTO activeStockDTO) {
        Objects.requireNonNull(activeStockDTO, "商品激活参数不能为空");
        LambdaUpdateWrapper<BasisStockEntity> update = Wrappers.lambdaUpdate();
        update.eq(BasisStockEntity::getId, activeStockDTO.getStockId());
        update.set(BasisStockEntity::getResourceAuditStatus, BasisCottonConstants.resource_audit_status_2);
        update.set(BasisStockEntity::getTradeStatus, BasisCottonConstants.trade_status_2);
        basisStockService.update(new BasisStockEntity(), update);
        log.info("商品 ID: {}, 激活成功", activeStockDTO.getStockId());
        return TradingResult.success("商品激活成功");
    }

    @Override
    public TradingResult cancelStock(@Nonnull WithdrawStockDTO withdrawStockDTO) {
        Objects.requireNonNull(withdrawStockDTO, "卖家撤销商品参数不能为空");
        TradingOperation operation = () -> doCancelStock(withdrawStockDTO);
        return tradingOperationProcessor.process(withdrawStockDTO, operation, "卖家撤销商品");
    }

    @Override
    public TradingResult changeStockPrice(@Nonnull ChangeStockPriceDTO dto) {
        Objects.requireNonNull(dto, "修改商品价格参数不能为空");
        TradingOperation operation = () -> doChangeStockPrice(dto);
        return tradingOperationProcessor.process(dto, operation, "修改商品价格");
    }

    private TradingResult doChangeStockPrice(ChangeStockPriceDTO dto) {
        String stockId = dto.getStockId();
        BasisStockEntity stock = basisStockService.getStockAndCheckTradingStatus(stockId);

        // 仅能修改自己的商品
        if (!dto.getTraderCode().equals(stock.getTraderCode())) {
            throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, "仅能修改自己的商品");
        }

        stock = basisStockService.updateStockPriceAndValidityEnd(dto);

        List<BasisQuoteEntity> pendingQuotes = basisQuoteService.getPendingQuoteByStockId(stockId);

        BigDecimal newestStockPrice = dto.getStockPrice();

        boolean canQuoteMatch = false;
        BasisDeliveryTargetEntity target = null;
        if (!pendingQuotes.isEmpty()) {
            // 排序, 价格降序, 时间升序
            final Comparator<BasisQuoteEntity> priceTimeComparator =
                    Comparator.comparing(BasisQuoteEntity::getQuotePrice, Comparator.reverseOrder())
                            .thenComparing(BasisQuoteEntity::getUpdateTime, Comparator.naturalOrder());

            pendingQuotes.sort(priceTimeComparator);
            BasisQuoteEntity bestQuote = pendingQuotes.get(0);

            // 验证报价状态是否可操作
            tradingStatusHelper.validateQuoteStatus(bestQuote.getQuoteStatus(), "撮合报价");

            if (tradingStatusHelper.canMatch(bestQuote.getQuotePrice(), newestStockPrice)) {
                canQuoteMatch = true;
                // 更新报价状态
                Long winQuoteId = bestQuote.getId();
                basisQuoteService.updateQuoteMatch(winQuoteId);

                target = matchProcessor.processMatch(stock, bestQuote, QuoteCancelType.OTHER_MATCH, dto.getTradeMode());
            }
        }

        if (canQuoteMatch) {
            // 更新商品状态
            basisStockService.updateStockMatched(stockId, dto);
        }

        log.info("商品 ID: {}, 卖方修改报价成功", stockId);
        if (canQuoteMatch) {
            log.info("商品 ID: {}, 卖家修改后撮合成功, 成交价格: {}", stockId, newestStockPrice);
        }
        return TradingResult.builder()
                .success(true)
                .matched(canQuoteMatch)
                .deliveryTarget(target)
                .message("卖方修改报价成功")
                .build();
    }

    /**
     * 执行卖家撤销商品的业务逻辑，不涉及事务管理，便于单元测试
     *
     * @param withdrawStockDTO 卖家撤销商品参数
     * @return 交易结果
     */
    protected TradingResult doCancelStock(@Nonnull WithdrawStockDTO withdrawStockDTO) {
        String stockId = withdrawStockDTO.getStockId();
        // 获取商品并检查状态
        BasisStockEntity stock = basisStockService.getStockAndCheckTradingStatus(stockId);
        // 更新商品状态（线程安全：创建新的商品状态对象）
        basisStockService.updateStockWithdraw(withdrawStockDTO);

        QuoteCancelType quoteCancelType = QuoteCancelType.SELLER_CANCEL;
        if (withdrawStockDTO.getCancelType() == StockCancelType.OTHER_BUSINESS) {
            quoteCancelType = QuoteCancelType.SYSTEM_CANCEL;
        }

        // 处理商品下的所有未成交的报价
        List<BasisQuoteEntity> disposed = basisQuoteService.disposeBuyerQuote(stockId, null, quoteCancelType);
        // 释放保证金
        List<HandleMarginDTO> handleMarginDTOList = disposed.stream()
                .map(HandleMarginDTO.quoteToHandleMarginDTO(withdrawStockDTO.getTradeMode()))
                .collect(Collectors.toList());
        basisTradeSettlementService.freeMargin(handleMarginDTOList);

        // 现货挂牌仓单资源和基差点价自主撤销时更新仓单状态为正常
        if ((withdrawStockDTO.getTradeMode() == TradeMode.SPOT_LISTING && Objects.equals(BasisCottonConstants.stock_source_1, stock.getStockSource().getCode()))
                || (withdrawStockDTO.getTradeMode() == TradeMode.BASIS_PRICING && quoteCancelType == QuoteCancelType.SELLER_CANCEL)) {
            // 修改仓单状态为正常
            List<BaseResponse> baseResponses = basisTradeStorageService.updateWarehouseReceiptStatus(stock.getWarehouseReceiptNo(),
                    BasisCottonConstants.TRANSACTION_STATUS_2,
                    null,
                    null);
            if (baseResponses == null || baseResponses.isEmpty()) {
                log.warn("卖方撤销商品, 修改仓单状态为正常失败, 商品 ID: {}", stockId);
                throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, "修改仓单状态失败");
            }

            String changeWarehouseReceiptStausErrorMsg = baseResponses.stream()
                    .filter(r -> !r.isSuccess())
                    .map(BaseResponse::getMsg)
                    .collect(Collectors.joining(";"));
            if (!changeWarehouseReceiptStausErrorMsg.isEmpty()) {
                log.warn("商品 ID: {}, 卖家撤销商品失败, 错误信息: {}", stockId, changeWarehouseReceiptStausErrorMsg);
                throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, "修改仓单状态失败, " + changeWarehouseReceiptStausErrorMsg);
            }
        }

        TradeMode tradeMode = TradeMode.parseDataItem(stock.getTradeMode());

        if (stock.getSellerFreezeMarginAmount().compareTo(BigDecimal.ZERO) > 0) {
            HandleMarginDTO marginDTO = HandleMarginDTO.quoteToHandleMarginDTOFromStock(tradeMode).apply(stock);
            basisTradeSettlementService.freeMargin(Collections.singletonList(marginDTO));
        }

        if (stock.getSellerFreezeTradeFeeAmount().compareTo(BigDecimal.ZERO) > 0) {
            HandleMarginDTO marginDTO = HandleMarginDTO.quoteToHandleMarginDTOFromStockTradFee(tradeMode).apply(stock);
            basisTradeSettlementService.freeMargin(Collections.singletonList(marginDTO));
        }

        log.info("商品 ID: {}, 卖家撤销商品成功", stockId);
        return TradingResult.builder()
                .success(true)
                .canceled(true)
                .message("卖家撤销商品成功")
                .build();
    }
}
