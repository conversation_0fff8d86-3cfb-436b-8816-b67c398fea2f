package com.basiscotton.tradingcore.business.impl;

import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.*;
import com.basiscotton.base.enums.QuoteCancelType;
import com.basiscotton.base.enums.TradeMode;
import com.basiscotton.base.service.BasisQuoteService;
import com.basiscotton.base.service.BasisStockService;
import com.basiscotton.common.apiservice.IBasisTradeSettlementService;
import com.basiscotton.common.apiservice.IBasisTradeStorageService;
import com.basiscotton.common.apiservice.dto.HandleMarginDTO;
import com.basiscotton.tradingcore.business.*;
import com.basiscotton.tradingcore.dto.*;
import com.cottoneasy.storage.trade.base.vo.response.BaseResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class QuoteServiceImpl implements QuoteService {


    private final TradingOperationProcessor tradingOperationProcessor;

    private final TradingStatusHelper tradingStatusHelper;

    private final BasisQuoteService basisQuoteService;

    private final BasisStockService basisStockService;

    private final IBasisTradeSettlementService basisTradeSettlementService;

    private final IBasisTradeStorageService basisTradeStorageService;

    private final MatchProcessor matchProcessor;

    /**
     * 提交报价, 报价成功冻结买方保证金, 保证金从报价时的系统设置获取
     * <p>如果以存在报价, 则直接抛出异常</p>
     * <p>如果是该商品第一次提交报价, 则修改仓储状态</p>
     *
     * @param dto 提交报价参数
     * @return 提交报价结果
     */
    @Override
    public TradingResult submitBuyerQuote(@Nonnull SubmitQuoteDTO dto) {
        Objects.requireNonNull(dto, "提交报价参数不能为空");
        TradingOperation operation = () -> doSubmitBuyerQuote(dto);
        return tradingOperationProcessor.process(dto, operation, "提交报价");
    }

    @Override
    public TradingResult buyerChangeQuote(BuyerChangeQuoteDTO dto) {
        Objects.requireNonNull(dto, "修改报价参数不能为空");
        TradingOperation operation = () -> doChangeBuyerQuote(dto);
        return tradingOperationProcessor.process(dto, operation, "修改报价");
    }

    @Override
    public TradingResult buyerCancelQuote(BuyerCancelQuoteDTO buyerCancelQuoteDTO) {
        Objects.requireNonNull(buyerCancelQuoteDTO, "买方取消报价参数不能为空");
        TradingOperation operation = () -> doBuyerCancelQuote(buyerCancelQuoteDTO);
        return tradingOperationProcessor.process(buyerCancelQuoteDTO, operation, "取消报价");
    }

    /**
     * 执行提交报价的业务逻辑，不涉及事务管理，便于单元测试
     *
     * @param dto 提交报价参数
     * @return 交易结果
     */
    protected TradingResult doSubmitBuyerQuote(@Nonnull SubmitQuoteDTO dto) {
        String stockId = dto.getStockId();
        // 获取商品并检查商品状态
        BasisStockEntity stock = basisStockService.getStockAndCheckTradingStatus(stockId);

        List<BasisQuoteEntity> allQuote = basisQuoteService.getAllQuoteByStockId(stockId);

        // 判断该用户是否存在未成交的报价记录
        boolean hasPendingQuote = Boolean.TRUE.equals(
                basisQuoteService.hasBuyerPendingQuoteForStock(stockId, dto.getBuyerCustomerCode())
        );

        // 不能对自己的商品报价
        if (dto.getBuyerCustomerCode().equals(stock.getTraderCode())) {
            throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, "不能对自己的商品报价");
        }

        if (hasPendingQuote) {
            throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, "该用户已存在未处理的报价，请先处理未完成的报价");
        }

        // 判断该商品是否是第一次提交
        if (dto.getTradeMode() == TradeMode.BASIS_PRICING) {
            boolean isFirstQuote = allQuote.isEmpty();
            if (isFirstQuote) {
                updateWarehouseReceiptStatus(stock, BasisCottonConstants.TRANSACTION_STATUS_4, "买家提交报价");
            }
        }

        Integer negotiable = stock.getNegotiable();
        if (Objects.equals(negotiable, 0) && stock.getStockPrice().compareTo(dto.getQuotePrice()) != 0) {
            throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, "该商品不支持议价，请重新提交报价");
        }

        // 判断报价价格能否成交
        boolean canQuoteMatch = tradingStatusHelper.canMatch(dto.getQuotePrice(), stock.getStockPrice());

        // 提交新报价
        BasisQuoteEntity newQuote = basisQuoteService.submitBuyerQuote(dto, stock, canQuoteMatch);

        // 报价成功后更新商品状态
        if (canQuoteMatch) {
            basisStockService.updateStockMatched(stockId, dto);
        }

        BasisDeliveryTargetEntity target = null;
        if (canQuoteMatch) {
            target = matchProcessor.processMatch(stock, newQuote, QuoteCancelType.OTHER_MATCH, dto.getTradeMode());
        }

        // 冻结交易保证金 + 交易手续费
        HandleMarginDTO handleMarginDTO = HandleMarginDTO.quoteToHandleMarginDTO(dto.getTradeMode()).apply(newQuote);
        HandleMarginDTO handleTradeFeeDTO = HandleMarginDTO.quoteToHandleMarginDTOTradeFee(dto.getTradeMode()).apply(newQuote);
        basisTradeSettlementService.freezeMargin(Arrays.asList(handleMarginDTO, handleTradeFeeDTO));

        log.info("商品 ID: {}, 报价成功, 报价 ID: {}", stockId, newQuote.getId());
        return TradingResult.builder()
                .success(true)
                .matched(canQuoteMatch)
                .deliveryTarget(target)
                .message("报价成功")
                .build();
    }

    /**
     * 执行修改买方报价的业务逻辑
     * <p>
     * 此方法包含具体的业务逻辑，不涉及事务管理，便于单元测试
     * </p>
     *
     * @param dto 修改报价参数
     * @return 交易结果
     */
    protected TradingResult doChangeBuyerQuote(@Nonnull BuyerChangeQuoteDTO dto) {
        String stockId = dto.getStockId();

        BasisStockEntity stock = basisStockService.getStockAndCheckTradingStatus(stockId);

        BasisQuoteEntity quote = basisQuoteService.getById(dto.getQuoteId());

        tradingStatusHelper.checkQuoteCanBeModified(quote);

        if (!quote.getTraderCode().equals(dto.getBuyerCustomerCode())) {
            throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, "该用户没有该报价");
        }

        // 验证报价状态是否可修改
        tradingStatusHelper.validateQuoteStatus(quote.getQuoteStatus(), "修改报价");
        // 判断能否撮合
        boolean canQuoteMatch = tradingStatusHelper.canMatch(dto.getQuotePrice(), stock.getStockPrice());
        // 更新报价信息
        basisQuoteService.changeBuyerQuote(dto, canQuoteMatch);
        if (canQuoteMatch) {
            // 更新商品状态
            basisStockService.updateStockMatched(stockId, dto);
        }

        BasisDeliveryTargetEntity target = null;
        if (canQuoteMatch) {
            target = matchProcessor.processMatch(stock, quote, QuoteCancelType.OTHER_MATCH, dto.getTradeMode());
        }

        log.info("商品 ID: {}, 修改报价成功, 报价 ID: {}", stockId, quote.getId());
        return TradingResult.builder()
                .success(true)
                .matched(canQuoteMatch)
                .deliveryTarget(target)
                .message("修改报价成功")
                .build();
    }


    /**
     * 执行买方取消报价的业务逻辑，不涉及事务管理，便于单元测试
     *
     * @param dto 取消报价参数
     * @return 交易结果
     */
    protected TradingResult doBuyerCancelQuote(@Nonnull BuyerCancelQuoteDTO dto) {
        String stockId = dto.getStockId();
        BasisStockEntity stock = basisStockService.getStockAndCheckTradingStatus(stockId);

        String buyerCustomerCode = dto.getBuyerCustomerCode();
        BasisQuoteEntity quote = basisQuoteService.getById(dto.getQuoteId());

        if (quote == null) {
            log.warn("该商品不存在用户报价, 商品 ID: {}, 客户代码: {}", stockId, buyerCustomerCode);
            return TradingResult.failure("该商品不存在用户报价");
        }

        if (!quote.getTraderCode().equals(dto.getBuyerCustomerCode())) {
            throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, "该用户没有该报价");
        }

        // 验证报价状态是否可取消
        tradingStatusHelper.validateQuoteStatus(quote.getQuoteStatus(), "取消报价");

        // 取消报价
        basisQuoteService.cancelBuyerQuote(dto, QuoteCancelType.BUYER_CANCEL);

        HandleMarginDTO handleMarginDTO = HandleMarginDTO.quoteToHandleMarginDTO(dto.getTradeMode()).apply(quote);
        HandleMarginDTO handleTradeFeeDTO = HandleMarginDTO.quoteToHandleMarginDTOTradeFee(dto.getTradeMode()).apply(quote);

        // 释放保证金
        basisTradeSettlementService.freeMargin(Arrays.asList(handleMarginDTO, handleTradeFeeDTO));

        if (dto.getTradeMode() == TradeMode.BASIS_PRICING) {
            // 获取待处理的报价
            List<BasisQuoteEntity> pendingQuoteByStockId = basisQuoteService.getPendingQuoteByStockId(stockId);
            // 如果没有待处理的报价，则更新仓单状态为正常
            if (pendingQuoteByStockId.isEmpty()) {
                // 更新仓储状态为正常
                updateWarehouseReceiptStatus(stock, BasisCottonConstants.TRANSACTION_STATUS_2, "买家取消报价");
            }
        }

        log.info("商品 ID: {}, 取消报价成功, 报价 ID: {}", stockId, quote.getId());
        return TradingResult.builder()
                .success(true)
                .message("取消报价成功")
                .build();
    }

    /**
     * 更新仓单状态
     *
     * @param stock     商品信息
     * @param status    仓单状态
     * @param operation 操作
     */
    protected void updateWarehouseReceiptStatus(@Nonnull BasisStockEntity stock, @Nonnull Integer status, String operation) {
        Objects.requireNonNull(stock, "商品信息不能为空");
        Objects.requireNonNull(stock.getWarehouseReceiptNo(), "商品仓单号不能为空");
        Objects.requireNonNull(status, "仓单状态不能为空");

        List<BaseResponse> baseResponses = basisTradeStorageService.updateWarehouseReceiptStatus(stock.getWarehouseReceiptNo(),
                status,
                null, null);

        if (baseResponses == null || baseResponses.isEmpty()) {
            log.warn("{}, 修改仓单状态为正常失败, 商品 ID: {}", operation, stock.getId());
            throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, "修改仓单状态失败");
        }
        String changeWarehouseReceiptStausErrorMsg = baseResponses.stream()
                .filter(r -> !r.isSuccess())
                .map(BaseResponse::getMsg)
                .collect(Collectors.joining(";"));
        if (!changeWarehouseReceiptStausErrorMsg.isEmpty()) {
            log.warn("{}, 商品 ID: {}, 卖家撤销商品失败, 错误信息: {}", operation, stock.getId(), changeWarehouseReceiptStausErrorMsg);
            throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, "修改仓单状态失败, " + changeWarehouseReceiptStausErrorMsg);
        }
    }
}
