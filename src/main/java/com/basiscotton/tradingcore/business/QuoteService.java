package com.basiscotton.tradingcore.business;

import com.basiscotton.tradingcore.dto.*;


/**
 * 报价服务
 */
public interface QuoteService {

    /**
     * 买家提交报价
     */
    TradingResult submitBuyerQuote(SubmitQuoteDTO submitQuoteDTO);

    /**
     * 买家修改报价
     */
    TradingResult buyerChangeQuote(BuyerChangeQuoteDTO buyerChangeQuoteDTO);

    /**
     * 买家取消报价
     */
    TradingResult buyerCancelQuote(BuyerCancelQuoteDTO buyerCancelQuoteDTO);
}
