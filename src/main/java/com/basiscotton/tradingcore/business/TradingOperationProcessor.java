package com.basiscotton.tradingcore.business;

import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.BasisDeliveryTargetEntity;
import com.basiscotton.base.entity.BasisStockEntity;
import com.basiscotton.base.enums.TradeMode;
import com.basiscotton.base.mappers.BasisDeliveryTargetMapper;
import com.basiscotton.base.service.BasisStockService;
import com.basiscotton.common.apiservice.IBasisTradeSettlementService;
import com.basiscotton.common.apiservice.dto.HandleTradeFeeDTO;
import com.basiscotton.common.tradetime.service.BasisTradeTimeWindowService;
import com.basiscotton.common.tradetime.service.SpotTradeTimeWindowService;
import com.basiscotton.tradingcore.dto.TradingResult;
import com.basiscotton.tradingcore.dto.TradingDTO;
import com.basiscotton.tradingcore.reentrantlocks.BasisStockLockService;
import com.sinosoft.cnce.sc.api.service.TXCodeService;
import com.sinosoft.cnce.sc.base.enumeration.BusinessModuleEnum;
import com.sinosoft.cnce.sc.base.enumeration.FinancialEntityAccountTypeEnum;
import com.sinosoft.cnce.sc.base.vo.SCResponseVO;
import com.sinosoft.dandelion.system.client.params.DataItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.common.TransactionRoutingManager;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.springframework.stereotype.Component;

import javax.annotation.Nonnull;
import java.math.BigDecimal;
import java.util.Objects;

import static com.basiscotton.base.BasisCottonConstants.*;

@Component
@RequiredArgsConstructor
@Slf4j
public class TradingOperationProcessor {

    private static final String SETTLE_SUCCESS = "00000";

    /**
     * 分布式锁工具类，用于保证交易操作的原子性
     */
    private final BasisStockLockService basisStockLockService;

    /**
     * 事务路由管理器，用于管理分布式事务
     */
    private final TransactionRoutingManager transactionRoutingManager;

    /**
     * 商品基础服务
     */
    private final BasisStockService basisStockService;

    private final BasisDeliveryTargetMapper basisDeliveryTargetMapper;

    /**
     * 交易时间窗口服务
     */
    private final BasisTradeTimeWindowService basisTradeTimeWindowService;

    private final SpotTradeTimeWindowService spotTradeTimeWindowService;

    private final IBasisTradeSettlementService basisTradeSettlementService;

    private final TXCodeService txCodeService;

    /**
     * 事务执行模板方法
     * <p>
     * 统一处理交易操作的事务管理，包括:
     * 1. 交易时间校验
     * 2. 参数校验
     * 3. 分布式锁加锁
     * 4. 事务路由管理
     * 5. 异常处理与事务回滚
     * </p>
     *
     * @param dto           交易DTO
     * @param operation     交易操作
     * @param operationName 操作名称（用于日志）
     * @return 交易结果
     */
    public TradingResult process(TradingDTO dto, TradingOperation operation, String operationName) {
        String stockId = dto.getStockId();
        boolean isRemoveLockCache = false;
        preProcessCheck(stockId, dto);

        try {
            // 加锁
            basisStockLockService.lockBasisStock(stockId);
            // 开启事务
            transactionRoutingManager.assertOpen();
            TradingResult result = operation.execute();
            if (result.isSuccess()) {
                transactionRoutingManager.assertCommitAll();
                log.info("商品 ID: {}, {}成功", stockId, operationName);
            } else {
                // 回滚事务
                transactionRoutingManager.assertRollbackAll();
                log.warn("商品 ID: {}, {}失败: {}", stockId, operationName, result.getMessage());
            }

            // 如果成交切标的不为空, 处理手续费
            if (result.isMatched() && Objects.nonNull(result.getDeliveryTarget())) {
                result = handleTradeFees(result.getDeliveryTarget(), result);
            }

            if (result.isMatched() || result.isCanceled()) {
                // 商品成交了, 释放锁时移除锁缓存
                isRemoveLockCache = true;
            }

            return result;
        } catch (BusinessException e) {
            // 回滚事务
            transactionRoutingManager.assertRollbackAll();
            return TradingResult.failure("业务异常: " + e.getMessage());
        } catch (Exception e) {
            // 回滚事务
            transactionRoutingManager.assertRollbackAll();
            log.warn("商品 ID: {}, {}未知异常", stockId, operationName, e);
            return TradingResult.unKnownFailure("系统异常: " + e.getMessage());
        } finally {
            // 释放锁
            basisStockLockService.unlockBasisStock(stockId, isRemoveLockCache);
        }
    }

    protected void preProcessCheck(String stockId, TradingDTO dto) {

        String timeVerifyResult;
        if (dto.getTradeMode() == TradeMode.BASIS_PRICING) {
            // 交易时间校验
            timeVerifyResult = basisTradeTimeWindowService.verifyTradeTimeWindow();
        } else {
            timeVerifyResult = spotTradeTimeWindowService.verifyTradeTimeWindow();
        }
        if (!Objects.equals(BasisCottonConstants.TRADE_NORMAL, timeVerifyResult)) {
            log.warn("非交易时间，无法进行操作, 商品 ID: {}, 时间窗口校验结果: {}", stockId, timeVerifyResult);
            throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, "非交易时间，无法进行操作");
        }

        // 参数校验
        dto.check();

        // 商品状态校验
        BasisStockEntity stock = basisStockService.getStockAndCheckTradingStatus(stockId);

        // 是否是市场仓单资源
        boolean isWareResource = Objects.equals(stock_source_1, stock.getStockSource().getCode());
        // 仓单状态校验
        if (isWareResource && Boolean.TRUE.equals(dto.validWarehouseReceiptStatus())) {
            Boolean stockValid = basisStockService.isStockValid(stock);
            if (Boolean.FALSE.equals(stockValid)) {
                throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, "商品状态异常");
            }
        }
    }

    protected TradingResult handleTradeFees(@Nonnull BasisDeliveryTargetEntity deliveryTarget, @Nonnull TradingResult result) {
        try {
            // 加锁

            // 处理买方交易手续费

            TradingResult tradingResult = processSinglePartTradeFee(deliveryTarget, result, true);

            if (Objects.equals(SELLER_TRADE_FEE_COLLECT_PHASE_2, deliveryTarget.getSellerTradeFeeCollectPhase().getCode())) {
                return tradingResult;
            }

            tradingResult = processSinglePartTradeFee(deliveryTarget, tradingResult, false);

            return tradingResult;

        } catch (Exception e) {
            log.warn("处理交易手续费系统异常, 商品 ID: {}", deliveryTarget.getStockId(), e);
            transactionRoutingManager.assertRollbackAll();
            return TradingResult.buildWith(result)
                    .message("报价成交，但收取手续费失败")
                    .build();
        }
    }

    protected TradingResult processSinglePartTradeFee(@Nonnull BasisDeliveryTargetEntity deliveryTarget, @Nonnull TradingResult result,
                                                      boolean isBuyer) {
        String tradeFeeType = isBuyer ? deliveryTarget.getBuyerTradeFeeType().getCode() : deliveryTarget.getSellerTradeFeeType().getCode();
        BigDecimal tradeFee = isBuyer ? deliveryTarget.getBuyerTradeFeeAmount() : deliveryTarget.getSellerTradeFeeAmount();
        String traderCode = isBuyer ? deliveryTarget.getBuyerTraderCode() : deliveryTarget.getSellerTraderCode();

        // 申请交易码并保存（独立事务）
        transactionRoutingManager.assertOpen();
        Long txCode = txCodeService.getTXCode();
        if (isBuyer) {
            basisDeliveryTargetMapper.updateBuyerTxCode(deliveryTarget.getId(), txCode);
        } else {
            basisDeliveryTargetMapper.updateSellerTxCode(deliveryTarget.getId(), txCode);
        }
        transactionRoutingManager.assertCommitAll();

        FinancialEntityAccountTypeEnum financialEntityAccount;
        switch (tradeFeeType) {
            case "1":
                financialEntityAccount = FinancialEntityAccountTypeEnum.FINANCIAL_ENTITY_ACCOUNT_TYPE_1;
                break;
            case "2":
                financialEntityAccount = FinancialEntityAccountTypeEnum.FINANCIAL_ENTITY_ACCOUNT_TYPE_2;
                break;
            case "3":
                financialEntityAccount = FinancialEntityAccountTypeEnum.FINANCIAL_ENTITY_ACCOUNT_TYPE_301;
                break;
            default:
                log.warn("非法的手续费支付类型, 支付类型: {}", tradeFeeType);
                throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, "非法的手续费支付类型");
        }

        BusinessModuleEnum businessModule = getBusinessModule(deliveryTarget.getTradeMode());

        // 调用结算服务处理手续费
        HandleTradeFeeDTO handleTradeFeeDTO = HandleTradeFeeDTO.builder()
                .tradeMode(TradeMode.getByValue(deliveryTarget.getTradeMode().getCode()))
                .tradeFee(tradeFee)
                .traderCode(traderCode)
                .stockCode(deliveryTarget.getStockCode())
                .financialEntityAccount(financialEntityAccount)
                .businessModule(businessModule)
                .build();
        // 结算结果
        SCResponseVO scResponseVO = basisTradeSettlementService.settleTradeFee(handleTradeFeeDTO, txCode);

        // 根据结算结果更新状态（独立事务）
        transactionRoutingManager.assertOpen();
        if (Objects.isNull(scResponseVO) || !Objects.equals(SETTLE_SUCCESS, scResponseVO.getCode())) {
            log.warn("处理交易手续费结算异常, 商品 ID: {}, 结算结果: {}", deliveryTarget.getStockId(), scResponseVO);
            // 如果结算失败, 修改结算状态并置空交易码
            if (isBuyer) {
                basisDeliveryTargetMapper.updateBuyerTradeFeeStaus(deliveryTarget.getId(), TRADE_FEE_STATUS_4);
                basisDeliveryTargetMapper.updateBuyerTxCode(deliveryTarget.getId(), null);
            } else {
                basisDeliveryTargetMapper.updateSellerTradeFeeStaus(deliveryTarget.getId(), TRADE_FEE_STATUS_4);
                basisDeliveryTargetMapper.updateSellerTxCode(deliveryTarget.getId(), null);
            }
            transactionRoutingManager.assertCommitAll();

            String tradeParty = isBuyer ? "买家" : "卖家";
            return TradingResult.buildWith(result)
                    .message("报价成交，但 " + tradeParty + " 收取手续费失败")
                    .build();
        }

        if (isBuyer) {
            basisDeliveryTargetMapper.updateBuyerTradeFeeStaus(deliveryTarget.getId(), TRADE_FEE_STATUS_2);
        } else {
            basisDeliveryTargetMapper.updateSellerTradeFeeStaus(deliveryTarget.getId(), TRADE_FEE_STATUS_2);
        }
        transactionRoutingManager.assertCommitAll();

        return result;
    }

    protected BusinessModuleEnum getBusinessModule(DataItem dataItem) {
        TradeMode tradeMode = TradeMode.parseDataItem(dataItem);
        if (TradeMode.SPOT_LISTING == tradeMode) {
            return BusinessModuleEnum.SPOT_LISTING;
        } else if (TradeMode.BASIS_PRICING == tradeMode) {
            return BusinessModuleEnum.WAREHOUSE_RECEIPT_TRADE;
        } else {
            throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, "未知的交易模式");
        }
    }
}
