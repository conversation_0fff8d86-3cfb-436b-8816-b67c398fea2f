package com.basiscotton.tradingcore.business;

import com.basiscotton.tradingcore.dto.ActiveStockDTO;
import com.basiscotton.tradingcore.dto.ChangeStockPriceDTO;
import com.basiscotton.tradingcore.dto.TradingResult;
import com.basiscotton.tradingcore.dto.WithdrawStockDTO;

public interface StockService {

    /**
     * 商品生效, 审核或自动审核后使商品生效, 只能对生效的商品报价
     */
    TradingResult activeStock(ActiveStockDTO activeStockDTO);

    /**
     * 卖家取消商品
     */
    TradingResult cancelStock(WithdrawStockDTO withdrawStockDTO);

    TradingResult changeStockPrice(ChangeStockPriceDTO dto);

}
