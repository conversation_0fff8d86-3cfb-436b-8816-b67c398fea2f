package com.basiscotton.tradingcore.business;

import com.basiscotton.base.entity.BasisQuoteEntity;
import com.sinosoft.dandelion.system.client.params.DataItem;
import lombok.extern.slf4j.Slf4j;
import org.harry.dandelion.framework.core.common.Constants;
import org.springframework.stereotype.Component;
import org.harry.dandelion.framework.core.exception.BusinessException;

import javax.annotation.Nonnull;
import java.math.BigDecimal;
import java.util.Objects;

import static com.basiscotton.base.BasisCottonConstants.*;

/**
 * 交易状态判断工具类
 *
 * <p>功能概述：
 * 集中管理交易相关的状态判断逻辑，提供统一的状态验证方法。
 * 避免在业务代码中散落各种状态判断，提高代码可维护性。
 *
 * <p>设计目标：
 * <ul>
 *   <li>统一状态判断逻辑</li>
 *   <li>减少重复代码</li>
 *   <li>提高代码可读性</li>
 *   <li>便于单元测试</li>
 * </ul>
 */
@Component
@Slf4j
public class TradingStatusHelper {

    // region 报价状态相关

    /**
     * 检查报价是否可以修改
     *
     * @param quote 报价信息
     */
    public void checkQuoteCanBeModified(@Nonnull BasisQuoteEntity quote) {
        Objects.requireNonNull(quote, "要检查的报价不能为空");
        if (quote.getQuoteStatus() == null) {
            log.warn("报价状态异常, 报价状态为空, 商品 ID: {}, 客户代码: {}, 报价 ID: {}", quote.getStockId(),
                    quote.getTraderCode(),
                    quote.getId());
            throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, "报价状态异常, 未查询到报价状态");
        }

        // 只有未成交的报价可以修改
        boolean statusMatch = isStatusMatch(quote.getQuoteStatus(), quote_status_1);
        if (!statusMatch) {
            log.warn("报价状态异常, 报价状态为空, 商品 ID: {}, 客户代码: {}, 报价 ID: {}", quote.getStockId(),
                    quote.getTraderCode(),
                    quote.getId());
            throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, "报价状态异常, 只有未成交的报价可以修改");
        }
    }

    /**
     * 判断报价是否已成交
     *
     * @param quoteStatus 报价状态
     * @return true-已成交，false-未成交
     */
    public boolean isQuoteMatched(DataItem quoteStatus) {
        return isStatusMatch(quoteStatus, quote_status_2);
    }

    /**
     * 判断报价是否已失效
     *
     * @param quoteStatus 报价状态
     * @return true-已失效，false-未失效
     */
    public boolean isQuoteInvalid(DataItem quoteStatus) {
        return isStatusMatch(quoteStatus, quote_status_3);
    }

    /**
     * 验证报价状态是否有效
     *
     * @param quoteStatus 报价状态
     * @param operation   操作名称（用于异常信息）
     * @throws BusinessException 当状态无效时抛出异常
     */
    public void validateQuoteStatus(DataItem quoteStatus, String operation) {
        if (quoteStatus == null) {
            throw new BusinessException(
                    org.harry.dandelion.framework.core.common.Constants.SERVICE_DOWORK_EXCEPTION_CODE,
                    "报价状态异常，无法" + operation);
        }

        if (isQuoteMatched(quoteStatus)) {
            throw new BusinessException(
                    org.harry.dandelion.framework.core.common.Constants.SERVICE_DOWORK_EXCEPTION_CODE,
                    "报价已成交，无法" + operation);
        }

        if (isQuoteInvalid(quoteStatus)) {
            throw new BusinessException(
                    org.harry.dandelion.framework.core.common.Constants.SERVICE_DOWORK_EXCEPTION_CODE,
                    "报价已失效，无法" + operation);
        }
    }
    // endregion

    // region 商品状态相关

    /**
     * 判断商品是否可交易
     *
     * @param tradeStatus 交易状态
     * @return true-可交易，false-不可交易
     */
    public boolean isStockTradable(DataItem tradeStatus) {
        if (tradeStatus == null) {
            return false;
        }

        String status = tradeStatus.getCode();
        // 未成交和已交易的商品可以继续交易
        return Objects.equals(trade_status_2, status);
    }

    /**
     * 判断商品是否已上市
     *
     * @param auditStatus 审核状态
     * @return true-已上市，false-未上市
     */
    public boolean isStockListed(DataItem auditStatus) {
        return isStatusMatch(auditStatus, resource_audit_status_2);
    }

    /**
     * 验证商品状态是否可交易
     *
     * @param auditStatus 审核状态
     * @param tradeStatus 交易状态
     * @throws BusinessException 当状态不可交易时抛出异常
     */
    public void validateStockTradable(DataItem auditStatus, DataItem tradeStatus) {
        if (!isStockListed(auditStatus)) {
            log.warn("商品未上市，不可交易");
            throw new BusinessException(
                    org.harry.dandelion.framework.core.common.Constants.SERVICE_DOWORK_EXCEPTION_CODE,
                    "商品未上市，不可交易");
        }

        if (!isStockTradable(tradeStatus)) {
            log.warn("商品状态异常，不可交易");
            throw new BusinessException(
                    org.harry.dandelion.framework.core.common.Constants.SERVICE_DOWORK_EXCEPTION_CODE,
                    "商品状态异常，不可交易");
        }
    }
    // endregion

    // region 撮合相关

    /**
     * 判断报价是否满足撮合条件
     *
     * @param quotePrice  买方报价
     * @param sellerPrice 卖方价格
     * @return true-可以撮合，false-不可撮合
     */
    public boolean canMatch(BigDecimal quotePrice, BigDecimal sellerPrice) {
        if (quotePrice == null || sellerPrice == null) {
            return false;
        }
        return quotePrice.compareTo(sellerPrice) >= 0;
    }

    // endregion

    // region 状态转换

    /**
     * 获取报价成交后的状态
     *
     * @return 成交状态
     */
    public DataItem getQuoteMatchedStatus() {
        return new DataItem(quote_status_2,null);
    }

    /**
     * 获取报价撤销状态
     *
     * @return 报价撤销
     */
    public DataItem getQuoteCancelStatus() {
        return new DataItem(quote_status_3, null);
    }

    /**
     * 报价成功, 但不满足成交条件
     *
     * @return 未成交
     */
    public DataItem getQuotePendingStatus() {
        return new DataItem(quote_status_1, null);
    }

    /**
     * 获取商品成交后的状态
     *
     * @return 成交状态
     */
    public DataItem getStockPendingStatus() {
        return new DataItem(trade_status_2, null);
    }
    /**
     * 获取商品成交后的状态
     *
     * @return 成交状态
     */
    public DataItem getStockMatchedStatus() {
        return new DataItem(trade_status_3, null);
    }

    // endregion

    // region 辅助方法

    /**
     * 检查价格是否有效
     *
     * @param price 价格
     * @return true-有效，false-无效
     */
    public boolean isValidPrice(BigDecimal price) {
        return price != null && price.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 验证价格有效性
     *
     * @param price     价格
     * @param fieldName 字段名称
     * @throws BusinessException 当价格无效时抛出异常
     */
    public void validatePrice(BigDecimal price, String fieldName) {
        if (!isValidPrice(price)) {
            log.warn("{}价格异常", fieldName);
            throw new BusinessException(
                    org.harry.dandelion.framework.core.common.Constants.SERVICE_DOWORK_EXCEPTION_CODE,
                    fieldName + "价格异常");
        }
    }

    /**
     * 检查状态是否匹配
     *
     * @param status 状态
     * @param expectedCode 期望的状态码
     * @return true-匹配，false-不匹配
     */
    private boolean isStatusMatch(DataItem status, String expectedCode) {
        return status != null && Objects.equals(expectedCode, status.getCode());
    }
    // endregion
}
