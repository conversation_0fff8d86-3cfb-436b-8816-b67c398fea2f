package com.basiscotton.tradingcore.cache;

import com.basiscotton.base.mappers.EnterpriseClientAccountCodeMapper;
import com.github.benmanes.caffeine.cache.Cache;
import org.springframework.stereotype.Component;

import java.util.function.UnaryOperator;

@Component
public class SettleCodeCacheService {

    private final Cache<String, String> settleCodeCache;

    private final UnaryOperator<String> loader;

    public SettleCodeCacheService(Cache<String, String> settleCodeCache,
                                  EnterpriseClientAccountCodeMapper enterpriseClientAccountCodeMapper) {
        this.settleCodeCache = settleCodeCache;
        this.loader = enterpriseClientAccountCodeMapper::getSettleCodeByTradeCode;
    }

    public String get(String traderCode) {

        return settleCodeCache.get(traderCode, loader);
    }
}
