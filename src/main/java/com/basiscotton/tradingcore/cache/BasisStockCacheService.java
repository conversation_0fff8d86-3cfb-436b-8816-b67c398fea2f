package com.basiscotton.tradingcore.cache;

import com.basiscotton.base.entity.BasisStockEntity;
import com.basiscotton.base.service.BasisStockService;
import com.basiscotton.tradingcore.cache.bo.StockLocker;
import com.github.benmanes.caffeine.cache.Cache;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: BasisStockCacheService.java
 * @Description: 商品缓存服务类
 * @date 2025/8/23 15:32
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class BasisStockCacheService implements CommandLineRunner {

    private final Cache<String, StockLocker> basisStockCache;

    private final BasisStockService basisStockService;

    /**
     * 添加缓存
     */
    public void addCache(String key, StockLocker stockLocker) {
        basisStockCache.get(key, k -> stockLocker);
    }

    /**
     * 获取缓存中的某个值
     */
    public StockLocker getCache(String key) {
        return basisStockCache.getIfPresent(key);
    }

    /**
     * 获取缓存中的所有值
     */
    public List<StockLocker> getAllCache() {
        return new ArrayList<>(basisStockCache.asMap().values());
    }

    /**
     * 返回pricingCommandCache的引用
     */
    public Cache<String, StockLocker> getPricingCommandCache() {
        return basisStockCache;
    }

    /**
     * 删除缓存
     */
    public void removeCache(String key) {
        basisStockCache.invalidate(key);
    }

    /**
     * 初始化缓存
     */
    public void initCache(List<StockLocker> list) {
        list.forEach(stock -> {
            try {
                addCache(stock.getStockId(), stock);
            } catch (Exception e) {
                log.error("初始化商品锁缓存失败。", e);
                throw e;
            }
        });
    }

    @Override
    public void run(String... args) {
        List<BasisStockEntity> allListingStock = basisStockService.getAllListingStock();
        if (Objects.isNull(allListingStock) || allListingStock.isEmpty()) {
            return;
        }

        List<StockLocker> lockers = allListingStock.stream()
                .map(stock -> new StockLocker(stock.getId().toString()))
                .collect(Collectors.toList());
        initCache(lockers);
    }
}
