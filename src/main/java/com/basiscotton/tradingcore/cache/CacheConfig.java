package com.basiscotton.tradingcore.cache;

import com.basiscotton.tradingcore.cache.constant.CacheEnum;
import com.basiscotton.tradingcore.cache.bo.StockLocker;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: BasisStockCacheBuilder.java
 * @Description: 商品缓存构造类
 * @date 2025/8/23 15:32
 */
@Configuration
@Slf4j
public class CacheConfig {
    @Primary
    @Bean
    public Cache<String, StockLocker> stockCache(){
        Cache<String, StockLocker> cache = Caffeine.newBuilder()
                .initialCapacity(CacheEnum.BASIS_STOCK_CACHE.getInitialCapacity())
                .maximumSize(CacheEnum.BASIS_STOCK_CACHE.getMaximumSize())
                //不过期.expireAfterAccess(CacheEnum.FundsAccountLockCache.getExpires(), TimeUnit.SECONDS)
                .recordStats()
                .build();
        log.info("基差商品及锁缓存构建完成！");
        return cache;
    }

    @Bean
    public Cache<String, String> settleCodeCache(){
        Cache<String, String> cache = Caffeine.newBuilder()
                .initialCapacity(CacheEnum.SETTLE_CODE_CACHE.getInitialCapacity())
                .maximumSize(CacheEnum.SETTLE_CODE_CACHE.getMaximumSize())
                .recordStats()
                .build();
        log.info("结算码缓存构建完成");
        return cache;
    }
}
