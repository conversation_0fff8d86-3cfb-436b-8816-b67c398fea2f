package com.basiscotton.tradingcore.cache.bo;

import lombok.Value;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: BasisStock.java
 * @Description: 商品对象
 * @date 2025/8/23 15:23
 */
@Value
@Slf4j
public class StockLocker {
    /**
     * 商品 ID
     */
    String stockId;

    /**
     * 锁对象,公平锁(同时避免等待线程同时唤醒),禁止序列化;
     */
    ReentrantLock stockLock = new ReentrantLock(true);

    public void lock() {
        try {
            stockLock.lock();
            log.info("加锁成功，商品 ID：{}", stockId);
        } catch (Exception e) {
            log.error("加锁异常，商品 ID：{}", stockId);
            throw e;
        }
    }

    public boolean lock(long timeout, TimeUnit timeUnit ) throws InterruptedException {
        try {
            return stockLock.tryLock(timeout, timeUnit);
        } catch (Exception e) {
            log.error("加锁异常，商品 ID：{}", stockId);
            throw e;
        }
    }

    public void unlock() {
        if (stockLock.isHeldByCurrentThread()) {
            stockLock.unlock();
            log.info("解锁成功，商品代码：{}", stockId);
        } else {
            log.error("解锁失败，商品代码：{}", stockId);
        }
    }
}
