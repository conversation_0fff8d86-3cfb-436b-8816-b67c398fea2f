package com.basiscotton.tradingcore.cache.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: CacheEnum.java
 * @Description: 缓存配置项的枚举类
 * @date 2025/8/23 15:29
 */
@Getter
public enum CacheEnum {

    //点价指令锁缓存
    BASIS_STOCK_CACHE("basisStockCache", CacheConstants.EXPIRES_NO, 500, 100000),
    SETTLE_CODE_CACHE("basisStockCache", CacheConstants.EXPIRES_5_MIN, 500, 100000);

    private final String name;

    private final long expire;

    private final int initialCapacity;

    private final int maximumSize;

    CacheEnum(String name, long expire, int initialCapacity, int maximumSize) {
        this.name = name;
        this.expire = expire;
        this.initialCapacity = initialCapacity;
        this.maximumSize = maximumSize;
    }
}
