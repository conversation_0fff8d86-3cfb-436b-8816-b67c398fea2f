package com.basiscotton.tradingcore.cache.constant;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: CacheConstants.java
 * @Description: 缓存配置项的常量类
 * @date 2025/8/23 15:28
 */
public class CacheConstants {

    private CacheConstants() {}

    /**
     * 默认过期时间（配置类中我使用的时间单位是秒，所以这里如 3*60 为3分钟）
     */
    public static final int EXPIRES_2_MIN = 2 * 60;
    public static final int DEFAULT_EXPIRES = 3 * 60;
    public static final int EXPIRES_5_MIN = 5 * 60;
    public static final int EXPIRES_24_HOUR = 24 * 60 * 60;

    public static final int EXPIRES_NO = Integer.MAX_VALUE;
}
