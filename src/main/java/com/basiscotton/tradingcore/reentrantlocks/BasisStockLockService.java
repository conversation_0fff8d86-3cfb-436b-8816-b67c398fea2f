package com.basiscotton.tradingcore.reentrantlocks;

import com.basiscotton.tradingcore.cache.bo.StockLocker;
import com.github.benmanes.caffeine.cache.Cache;
import lombok.extern.slf4j.Slf4j;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: BasisStockLockService.java
 * @Description: 商品锁服务
 * @date 2025/8/23 15:27
 */
@Component
@Slf4j
public class BasisStockLockService {
    @Resource
    Cache<String, StockLocker> basisStockCache;

    private final Function<String, StockLocker> ifNullThrowException = stockId -> {
        throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, "基差商品不存在: " + stockId);
    };

    /**
     * 对单一基差商品加锁，线程安全
     * 加锁实现,后面要紧跟try块，及临界区代码
     */
    public void lockBasisStock(String stockId) {


        StockLocker stockLocker = basisStockCache.get(stockId, ifNullThrowException);
        assert stockLocker != null;
        stockLocker.lock();
    }

    /**
     * q
     * 对单一基差商品解锁，线程安全
     */
    public void unlockBasisStock(String stockId) {
        this.unlockBasisStock(stockId, false);
    }

    /**
     * q
     * 对单一基差商品解锁，线程安全
     */
    public void unlockBasisStock(String stockId, boolean removeCache) {
        StockLocker stockLocker = basisStockCache.get(stockId, ifNullThrowException);
        assert stockLocker != null;
        stockLocker.unlock();
        if (removeCache) {
            basisStockCache.invalidate(stockId);
        }
    }

    /**
     * 对单一基差商品限时加锁,基本用不到
     */
    public boolean lockBasisStockWithTimeOut(String stockCode, long timeout, TimeUnit timeUnit) throws InterruptedException {
        StockLocker stockLocker = basisStockCache.getIfPresent(stockCode);
        assert stockLocker != null;
        return stockLocker.lock(timeout, timeUnit);
    }
}
