package com.basiscotton.base.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinosoft.dandelion.system.client.params.DataItem;
import com.sinosoft.dandelion.system.client.serializer.DataItemCodeDeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


@ApiModel("客户冻结资金记录表")
@TableName("sc_client_frozen_funds_flow_info")
@Data
public class ClientFrozenFundsFlowInfoEntity implements Serializable {

    private static final long serialVersionUID = 419880126L;

    /**
     * 资金冻结
     */
    public static final String OPERATION_FROZE = "1";

    /**
     * 资金释放
     */
    public static final String OPERATION_RELEASE = "2";

    /**
     * 主键
     */
    @TableId("ID")
    @ApiModelProperty(value = "主键")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    /**
     * 客户资金账户代码
     */
    @TableField("CLIENT_ACCOUNT_CODE")
    @ApiModelProperty(value = "客户资金账户代码")
    private String clientAccountCode;

    /**
     * 客户账户代码
     */
    @TableField("CLIENT_CODE")
    @ApiModelProperty(value = "客户账户代码")
    private String clientCode;

    /**
     * 结算码
     */
    @TableField("SETTLE_CODE")
    @ApiModelProperty(value = "结算码")
    private String settleCode;

    /**
     * 交易码
     */
    @TableField("TX_CODE")
    @ApiModelProperty(value = "交易码")
    private Long txCode;

    /**
     * 报文编号
     */
    @TableField("MSG_ID")
    @ApiModelProperty(value = "报文编号")
    private String msgId;

    /**
     * 财务主体资金账户代码
     */
    @TableField("FE_ACCOUNT_CODE")
    @ApiModelProperty(value = "财务主体资金账户代码")
    private String feAccountCode;

    /**
     * 业务模式ID
     */
    @TableField("BUSINESS_MODULE_ID")
    @ApiModelProperty(value = "业务模式ID")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem businessModuleId;

    /**
     * 业务流水号
     */
    @TableField("BUSINESS_ID")
    @ApiModelProperty(value = "业务流水号")
    private String businessId;

    /**
     * 类型1:冻结frozen   2:释放 release
     */
    @TableField("CLIENT_FROZEN_FUNDS_FLOW_TYPE")
    @ApiModelProperty(value = "类型1:冻结frozen   2:释放 release")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem clientFrozenFundsFlowType;

    /**
     * 发生额
     */
    @TableField("AMOUNT")
    @ApiModelProperty(value = "发生额")
    private BigDecimal amount;

    /**
     * 冻结资金总额
     */
    @TableField("FROZEN_FUNDS")
    @ApiModelProperty(value = "冻结资金总额")
    private BigDecimal frozenFunds;

    /**
     * 附加说明
     */
    @TableField("NOTE")
    @ApiModelProperty(value = "附加说明")
    private String note;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 模块冻结金额
     */
    private BigDecimal moduleBalance;


}
