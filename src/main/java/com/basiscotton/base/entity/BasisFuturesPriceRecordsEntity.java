package com.basiscotton.base.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
* 期货价格记录表
* @TableName trd_basis_futures_price_records
*/
@ApiModel(description="期货价格记录表")
@Data
@TableName(value = "trd_basis_futures_price_records")
public class BasisFuturesPriceRecordsEntity implements Serializable {

    /**
    * 主键
    */
    @TableId(value = "id")
    @ApiModelProperty(value="主键")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;
    /**
    * 期货合约
    */
    @TableField(value = "future_code")
    @ApiModelProperty("期货合约")
    private String futureCode;
    /**
    * 最新价格
    */
    @TableField(value = "last_price")
    @ApiModelProperty("最新价格")
    private BigDecimal lastPrice;
    /**
    * 结算价格
    */
    @TableField(value = "clear_price")
    @ApiModelProperty("结算价格")
    private BigDecimal clearPrice;
    /**
    * 涨停板
    */
    @TableField(value = "high_limit")
    @ApiModelProperty("涨停板")
    private BigDecimal highLimit;
    /**
    * 跌停板
    */
    @TableField(value = "low_limit")
    @ApiModelProperty("跌停板")
    private BigDecimal lowLimit;
    /**
    * 最高价
    */
    @TableField(value = "high_price")
    @ApiModelProperty("最高价")
    private BigDecimal highPrice;
    /**
    * 最低价格
    */
    @TableField(value = "low_price")
    @ApiModelProperty("最低价格")
    private BigDecimal lowPrice;
    /**
    * 期货价格时间
    */
    @TableField(value = "future_time")
    @ApiModelProperty("期货价格时间")
    private Date futureTime;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty("创建时间")
    private Date createTime;
    /**
     * 创建人ID
     */
    @TableField(value = "create_user_id")
    @ApiModelProperty("创建人ID")
    private String createUserId;
    /**
     * 创建人
     */
    @TableField(value = "create_user_name")
    @ApiModelProperty("创建人")
    private String createUserName;

}
