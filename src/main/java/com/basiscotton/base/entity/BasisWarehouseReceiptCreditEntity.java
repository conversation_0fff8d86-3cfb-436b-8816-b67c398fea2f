package com.basiscotton.base.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinosoft.dandelion.system.client.params.DataItem;
import com.sinosoft.dandelion.system.client.serializer.DataItemCodeDeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* 仓单抵免批次表
* @TableName trd_basis_warehouse_receipt_credit
*/
@ApiModel(description="仓单抵免批次表")
@Data
@TableName(value = "trd_basis_warehouse_receipt_credit")
public class BasisWarehouseReceiptCreditEntity implements Serializable {

    /**
    * 主键
    */
    @TableId(value = "id")
    @ApiModelProperty(value="主键")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;
    /**
    * 抵免id
    */
    @TableField(value = "credit_id")
    @ApiModelProperty("抵免id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long creditId;
    /**
    * 仓单号
    */
    @TableField(value = "warehouse_receipt_no")
    @ApiModelProperty("仓单号")
    private String warehouseReceiptNo;
    /**
    * 批号
    */
    @TableField(value = "batch_no")
    @ApiModelProperty("批号")
    private String batchNo;
    /**
    * 抵免状态：1未抵免2部分抵免3全部抵免4已换批5已作废
    */
    @TableField(value = "credit_status")
    @ApiModelProperty("抵免状态：1未抵免2部分抵免3全部抵免4已换批5已作废")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem creditStatus;
    /**
    * 创建时间
    */
    @TableField(value = "create_time")
    @ApiModelProperty("创建时间")
    private Date createTime;
    /**
    * 创建人id
    */
    @TableField(value = "create_user_id")
    @ApiModelProperty("创建人id")
    private String createUserId;
    /**
    * 创建人
    */
    @TableField(value = "create_user_name")
    @ApiModelProperty("创建人")
    private String createUserName;
    /**
    * 更新时间
    */
    @TableField(value = "update_time")
    @ApiModelProperty("更新时间")
    private Date updateTime;
    /**
    * 更新人id
    */
    @TableField(value = "update_user_id")
    @ApiModelProperty("更新人id")
    private String updateUserId;
    /**
    * 更新人
    */
    @TableField(value = "update_user_name")
    @ApiModelProperty("更新人")
    private String updateUserName;

}
