package com.basiscotton.base.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

 /**
 * <AUTHOR>
 * @version V1.0
 * @Title: SettlementClientFunds.java
 * @Description: ${DESCRIPTION}
 * @date 2025/9/1 14:17
 */
@ApiModel(description="v_spot_funds")
@Data
@TableName(value = "v_spot_funds")
public class SpotFundsEntity {
    /**
     * 客户账户代码
     */
    @TableField(value = "CLIENT_CODE")
    @ApiModelProperty(value="客户账户代码")
    private String clientCode;

    /**
     * 余额
     */
    @TableField(value = "BALANCE")
    @ApiModelProperty(value="余额")
    private BigDecimal balance;

    /**
     * 可用余额
     */
    @TableField(value = "AVAILABLE_BALANCE")
    @ApiModelProperty(value="可用余额")
    private BigDecimal availableBalance;

    /**
     * 所有冻结资金
     */
    @TableField(value = "FROZEN_FUNDS")
    @ApiModelProperty(value="所有冻结资金")
    private BigDecimal frozenFunds;

    /**
     * 其它冻结
     */
    @TableField(value = "OTHER_FROZEN_FUNDS")
    @ApiModelProperty(value="其它冻结")
    private BigDecimal otherFrozenFunds;

    /**
     * 挂牌冻结
     */
    @TableField(value = "SPOT_FROZEN_FUNDS")
    @ApiModelProperty(value="挂牌冻结")
    private BigDecimal spotFrozenFunds;
}