package com.basiscotton.base.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinosoft.dandelion.system.client.params.DataItem;
import com.sinosoft.dandelion.system.client.serializer.DataItemCodeDeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Title: BasisTradeOrderEntity.java
 * @Description: 基差交易订单表
 * <AUTHOR>
 * @date 2025/5/24
 * @version V1.0
 */
/**
 * @Deprecated 暂时不使用
 */
@Deprecated
@ApiModel(description="基差交易订单表")
@Data
@TableName(value = "trd_basis_order")
public class BasisOrderEntity implements Serializable {

	/**
	* 主键
	*/
	@TableId(value = "id")
	@ApiModelProperty(value="主键")
	@JSONField(serializeUsing = ToStringSerializer.class)
	private Long id;

	/**
	 * 合同id
	 */
	@TableField(value = "contract_id")
	@ApiModelProperty(value="合同id")
	@JSONField(serializeUsing = ToStringSerializer.class)
	private Long contractId;

	/**
	 * 商品id
	 */
	@TableField(value = "stock_id")
	@ApiModelProperty(value="商品id")
	@JSONField(serializeUsing = ToStringSerializer.class)
	private Long stockId;

	/**
	* 商品编码
	*/
	@TableField(value = "stock_code")
	@ApiModelProperty(value="商品编码")
	private String stockCode;

	/**
	 * 商品基础信息id
	 */
	@TableField(value = "stock_base_info_id")
	@ApiModelProperty(value="商品基础信息ID")
	@JSONField(serializeUsing = ToStringSerializer.class)
	private Long stockBaseInfoId;
	/**
	 * 委托表id
	 */
	@TableField(value = "quote_id")
	@ApiModelProperty(value="委托表id")
	@JSONField(serializeUsing = ToStringSerializer.class)
	private Long quoteId;

	/**
	* 卖方交易商代码
	*/
	@TableField(value = "seller_custom_code")
	@ApiModelProperty(value="卖方交易商代码")
	private String sellerCustomCode;

	/**
	 * 卖方交易商名称
	 */
	@TableField(value = "seller_custom_name")
	@ApiModelProperty(value="卖方交易商名称")
	private String sellerCustomName;

	/**
	 * 买方交易商代码
	 */
	@TableField(value = "buyer_custom_code")
	@ApiModelProperty(value="买方交易商代码")
	private String buyerCustomCode;

	/**
	 * 买方交易商名称
	 */
	@TableField(value = "buyer_custom_name")
	@ApiModelProperty(value="买方交易商名称")
	private String buyerCustomName;

	/**
	 *成交价格
	 */
	@TableField("trade_price")
	@ApiModelProperty(value = "成交价格")
	private BigDecimal tradePrice;

	/**
	 *成交数量
	 */
	@TableField("trade_quantity")
	@ApiModelProperty(value = "成交数量")
	private BigDecimal tradeQuantity;

	/**
	 *成交时间
	 */
	@TableField("trade_date")
	@ApiModelProperty(value = "成交时间")
	private Date tradeDate;

	/**
	 *卖方保证金标准
	 */
	@TableField("seller_margin_standard")
	@ApiModelProperty(value = "卖方保证金标准")
	private BigDecimal sellerMarginStandard;

	/**
	 *卖方保证金金额
	 */
	@TableField("seller_margin_amount")
	@ApiModelProperty(value = "卖方保证金金额")
	private BigDecimal sellerMarginAmount;

	/**
	 *卖方手续费金额
	 */
	@TableField("seller_trade_fee_amount")
	@ApiModelProperty(value = "卖方手续费金额")
	private BigDecimal sellerTradeFeeAmount;

	/**
	 *卖方手续费标准
	 */
	@TableField("seller_trade_fee_standard")
	@ApiModelProperty(value = "卖方手续费标准")
	private BigDecimal sellerTradeFeeStandard;

	/**
	 *卖方手续费金额
	 */
	@TableField("seller_delivery_fee_amount")
	@ApiModelProperty(value = "卖方手续费金额")
	private BigDecimal sellerDeliveryFeeAmount;

	/**
	 *卖方手续费标准
	 */
	@TableField("seller_delivery_fee_standard")
	@ApiModelProperty(value = "卖方手续费标准")
	private BigDecimal sellerDeliveryFeeStandard;

	/**
	 *买方保证金收取方式: 1-集团账户 2-电商
	 */
	@TableField("basis_seller_margin_type")
	@ApiModelProperty(value = "卖方保证金收取方式: 1-集团账户 2-电商")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem basisSellerMarginType;

	@TableField("basis_seller_trade_fee_type")
	@ApiModelProperty(value = "卖方交易手续费收取方式: 1-集团账户 2-电商")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem basisSellerTradeFeeType;

	@TableField("basis_seller_delivery_fee_type")
	@ApiModelProperty(value = "卖方交割手续费收取方式: 1-集团账户 2-电商")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem basisSellerDeliveryFeeType;

	/**
	 *买方保证金标准
	 */
	@TableField("buyer_margin_standard")
	@ApiModelProperty(value = "买方保证金标准")
	private BigDecimal buyerMarginStandard;

	/**
	 *买方保证金金额
	 */
	@TableField("buyer_margin_amount")
	@ApiModelProperty(value = "买方保证金金额")
	private BigDecimal buyerMarginAmount;

	/**
	 *买方手续费金额
	 */
	@TableField("buyer_trade_fee_amount")
	@ApiModelProperty(value = "买方手续费金额")
	private BigDecimal buyerTradeFeeAmount;

	/**
	 *买方手续费标准
	 */
	@TableField("buyer_trade_fee_standard")
	@ApiModelProperty(value = "买方手续费标准")
	private BigDecimal buyerTradeFeeStandard;

	/**
	 *买方手续费金额
	 */
	@TableField("buyer_delivery_fee_amount")
	@ApiModelProperty(value = "买方手续费金额")
	private BigDecimal buyerDeliveryFeeAmount;

	/**
	 *买方手续费标准
	 */
	@TableField("buyer_delivery_fee_standard")
	@ApiModelProperty(value = "买方手续费标准")
	private BigDecimal buyerDeliveryFeeStandard;

	/**
	 *买方保证金收取方式: 1-集团账户 2-电商
	 */
	@TableField("basis_buyer_margin_type")
	@ApiModelProperty(value = "买方保证金收取方式: 1-集团账户 2-电商")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem basisBuyerMarginType;

	/**
	 *买方交易手续费收取方式: 1-集团账户 2-电商
	 */
	@TableField("basis_buyer_trade_fee_type")
	@ApiModelProperty(value = "买方交易手续费收取方式: 1-集团账户 2-电商")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem basisBuyerTradeFeeType;

	/**
	 *买方交易手续费收取方式: 1-集团账户 2-电商
	 */
	@TableField("basis_buyer_delivery_fee_type")
	@ApiModelProperty(value = "买方交割手续费收取方式: 1-集团账户 2-电商")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem basisBuyerDeliveryFeeType;

	/**
	 *报价方式: 1-一口价 2-锁基差 3-即时点价
	 */
	@TableField("quote_type")
	@ApiModelProperty(value = "报价方式: 1-一口价 2-锁基差 3-即时点价")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem quoteType;

	/**
	 *点价方: 1-买方 2-卖方
	 */
	@TableField("pricing_party")
	@ApiModelProperty(value = "点价方: 1-买方 2-卖方")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem pricingParty;

	/**
	 * 期货合约
	 */
	@TableField(value = "future_code")
	@ApiModelProperty(value="期货合约")
	private String futureCode;

	/**
	 *一口价
	 */
	@TableField("fixed_price")
	@ApiModelProperty(value = "一口价")
	private BigDecimal fixedPrice;

	/**
	 *基差价格
	 */
	@TableField("basis_price")
	@ApiModelProperty(value = "基差价格")
	private BigDecimal basisPrice;

	/**
	 *基差价格范围
	 */
	@TableField("basis_price_range")
	@ApiModelProperty(value = "基差价格范围")
	private String basisPriceRange;

	/**
	 *点价价格
	 */
	@TableField("pricing_price")
	@ApiModelProperty(value = "点价价格")
	private BigDecimal pricingPrice;

	/**
	 *点价有效期
	 */
	@TableField("pricing_valid_time")
	@ApiModelProperty(value = "点价有效期")
	private Date pricingValidTime;

	/**
	 *资金监管状态：1、正常 2、三方监管-籽棉贷款 3、三方监管-皮棉调销 4、三方监管-融资监管状态
	 */
	@TableField("whs_supervision_status")
	@ApiModelProperty(value = "资金监管状态：1、正常 2、三方监管-籽棉贷款 3、三方监管-皮棉调销 4、三方监管-融资监管状态 5、质押监管 6、质押及三方监管")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem whsSupervisionStatus;

	/**
	 * 监管机构代码
	 */
	@TableField("supervise_code")
	@ApiModelProperty(value = "监管机构代码")
	private String superviseCode;

	/**
	 * 监管机构名称
	 */
	@TableField("supervise_name")
	@ApiModelProperty(value = "监管机构名称")
	private String superviseName;

	/**
	 *金融业务渠道
	 */
	@TableField("whs_pledge_channel")
	@ApiModelProperty(value = "金融渠道：1正常、2购销、3金益棉、4棉贸通、5E棉通、6棉E通、7民生银行、8中国银行、9农商银行")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem whsPledgeChannel;

}