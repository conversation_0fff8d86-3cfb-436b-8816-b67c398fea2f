package com.basiscotton.base.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinosoft.dandelion.system.client.params.DataItem;
import com.sinosoft.dandelion.system.client.serializer.DataItemCodeDeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

/**
 * @Deprecated 暂时不使用
 */
@Deprecated
@Entity
@TableName("trd_basis_notice")
@Data
public class BasisNoticeEntity implements Serializable {

	/**
	 *主键
	 */
	@TableId("ID")
	@ApiModelProperty(value = "主键")
	@JSONField(serializeUsing = ToStringSerializer.class)
	private Long id;
	
	/**
	 *标题
	 */
	@TableField("TITLE")
	@ApiModelProperty(value = "标题")
	private String title;
	
	/**
	 *内容
	 */
	@TableField("CONTENT")
	@ApiModelProperty(value = "内容")
	private String content;
	
	/**
	 *创建人
	 */
	@TableField("CREATE_USER_NAME")
	@ApiModelProperty(value = "创建人")
	private String createUserName;
	
	/**
	 *创建人
	 */
	@TableField("CREATE_USER_ID")
	@ApiModelProperty(value = "创建人ID")
	private String createUserId;
	
	/**
	 *创建时间
	 */
	@TableField("CREATE_TIME")
	@ApiModelProperty(value = "创建时间")
	private Date createTime;
	
	/**
	 *修改人
	 */
	@TableField("UPDATE_USER_NAME")
	@ApiModelProperty(value = "修改人")
	private String updateUserName;
	
	/**
	 *修改人id
	 */
	@TableField("UPDATE_USER_ID")
	@ApiModelProperty(value = "修改人id")
	private String updateUserId;
	
	/**
	 *修改时间
	 */
	@TableField("UPDATE_TIME")
	@ApiModelProperty(value = "修改时间")
	private Date updateTime;
	
	/**
     * 消息类型：1广播消息，2通知消息
     */
    @TableField("NOTICE_TYPE")
    @ApiModelProperty(value = "消息类型：1广播消息，2通知消息")
    private Integer noticeType;


	@TableField("del_flag")
	@ApiModelProperty(value = "删除状态 1.已删除 0.未删除")
	private Integer delFlag;

	@TableField("publish_status")
	@ApiModelProperty(value = "发布状态：1未发布，2已发布")
	private Integer publishStatus;
}