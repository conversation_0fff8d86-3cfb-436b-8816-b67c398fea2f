package com.basiscotton.base.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinosoft.dandelion.system.client.params.DataItem;
import com.sinosoft.dandelion.system.client.serializer.DataItemCodeDeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
* 基差标的表
* @TableName trd_basis_contract_target
*/
@ApiModel(description="基差标的表")
@Data
@TableName(value = "trd_basis_delivery_target")
public class BasisDeliveryTargetEntity implements Serializable {

    /**
    * 主键
    */
    @TableId(value = "id")
    @ApiModelProperty(value="主键")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;
    /**
    * 交易号
    */
    @TableField(value = "transaction_no")
    @ApiModelProperty("交易号")
    private String transactionNo;
    /**
    * 商品id
    */
    @TableField(value = "stock_id")
    @ApiModelProperty("商品id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long stockId;
    /**
    * 商品编码
    */
    @TableField(value = "stock_code")
    @ApiModelProperty("商品编码")
    private String stockCode;

    /**
     * 交易模式 1 现货挂牌 2基差点价
     */
    private DataItem tradeMode;

    /**
    * 合同id
    */
    @TableField(value = "contract_id")
    @ApiModelProperty("合同id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long contractId;
    /**
    * 商品基础信息id
    */
    @TableField(value = "stock_base_info_id")
    @ApiModelProperty("商品基础信息id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long stockBaseInfoId;
    /**
    * 报价id
    */
    @TableField(value = "quote_id")
    @ApiModelProperty("报价id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long quoteId;
    /**
    * 仓单号
    */
    @TableField(value = "warehouse_receipt_no")
    @ApiModelProperty("仓单号")
    private String warehouseReceiptNo;
    /**
    * 批号
    */
    @TableField(value = "batch_no")
    @ApiModelProperty("批号")
    private String batchNo;
    /**
    * 仓库代码
    */
    @TableField(value = "storage_whs_code")
    @ApiModelProperty("仓库代码")
    private String storageWhsCode;
    /**
    * 仓库名称
    */
    @TableField(value = "storage_whs_name")
    @ApiModelProperty("仓库名称")
    private String storageWhsName;
    /**
     * 仓储业务状态：1、正常 2、注册中 3、注销中 4、移库中 5、过户中
     */
    @TableField(value = "storage_status")
    @ApiModelProperty("仓储业务状态：1、正常 2、注册中 3、注销中 4、移库中 5、过户中")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem storageStatus;
    /**
     * 资金监管状态：1、正常 2、三方监管-籽棉贷款 3、三方监管-皮棉调销 4、三方监管-融资监管状态 5、质押监管 6、质押及三方监管
     */
    @TableField(value = "whs_supervision_status")
    @ApiModelProperty("资金监管状态：1、正常 2、三方监管-籽棉贷款 3、三方监管-皮棉调销 4、三方监管-融资监管状态 5、质押监管 6、质押及三方监管")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem whsSupervisionStatus;
    /**
     * 监管机构代码
     */
    @TableField(value = "supervise_code")
    @ApiModelProperty("监管机构代码")
    private String superviseCode;
    /**
     * 监管机构名称
     */
    @TableField(value = "supervise_name")
    @ApiModelProperty("监管机构名称")
    private String superviseName;
    /**
     * 金融业务渠道：1正常、2购销、3金益棉、4棉贸通、5e棉通、6棉e通、7全棉通、8邮棉贷、9农商银行
     */
    @TableField(value = "whs_pledge_channel")
    @ApiModelProperty("金融业务渠道：1正常、2购销、3金益棉、4棉贸通、5e棉通、6棉e通、7全棉通、8邮棉贷、9农商银行")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem whsPledgeChannel;
    /**
    * 卖方交易商代码
    */
    @TableField(value = "seller_trader_code")
    @ApiModelProperty("卖方交易商代码")
    private String sellerTraderCode;
    /**
    * 卖方交易商名称
    */
    @TableField(value = "seller_trader_name")
    @ApiModelProperty("卖方交易商名称")
    private String sellerTraderName;
    /**
    * 买方交易商代码
    */
    @TableField(value = "buyer_trader_code")
    @ApiModelProperty("买方交易商代码")
    private String buyerTraderCode;
    /**
    * 买方交易商名称
    */
    @TableField(value = "buyer_trader_name")
    @ApiModelProperty("买方交易商名称")
    private String buyerTraderName;
    /**
    * 报价方式: 1-买方点价
    */
    @TableField(value = "quote_type")
    @ApiModelProperty("报价方式: 1-买方点价")
    private Integer quoteType;
    /**
    * 期货合约
    */
    @TableField(value = "future_code")
    @ApiModelProperty("期货合约")
    private String futureCode;
    /**
    * 基差成交价格
    */
    @TableField(value = "trade_basis_price")
    @ApiModelProperty("基差成交价格")
    private BigDecimal tradeBasisPrice;
    /**
    * 成交重量
    */
    @TableField(value = "trade_weight")
    @ApiModelProperty("成交重量")
    private BigDecimal tradeWeight;
    /**
    * 点价有效结束日期
    */
    @TableField(value = "pricing_valid_end_time")
    @ApiModelProperty("点价有效结束日期")
    private Date pricingValidEndTime;
    /**
    * 成交时间
    */
    @TableField(value = "trade_date")
    @ApiModelProperty("成交时间")
    private Date tradeDate;
    /**
    * 交割方式：1先点价后交割2先交割后点价
    */
    @TableField(value = "delivery_type")
    @ApiModelProperty("交割方式：1先点价后交割2先交割后点价")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem deliveryType;
    /**
     * 点价状态：1未点价2点价中3已点价
     */
    @TableField(value = "pricing_status")
    @ApiModelProperty("点价状态：1未点价2点价中3已点价")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem pricingStatus;
    /**
     * 点价价格
     */
    @TableField(value = "pricing_price")
    @ApiModelProperty("点价价格")
    private BigDecimal pricingPrice;
    /**
     * 暂定期货价格
     */
    @TableField(value = "temp_futurn_price")
    @ApiModelProperty("暂定期货价格")
    private BigDecimal tempFuturnPrice;
    /**
     * 点价类型1:点价击穿-自动2:系统成交-到期结算3:系统成交-风控触线4手动确认-卖方
     */
    @TableField(value = "pricing_from")
    @ApiModelProperty("点价类型1:点价击穿-自动2:系统成交-到期结算3:系统成交-风控触线4手动确认-卖方")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem pricingFrom;
    /**
     * 点价时间
     */
    @TableField(value = "pricing_time")
    @ApiModelProperty("点价时间")
    private Date pricingTime;
    /**
     * 暂定结算价格
     */
    @TableField(value = "temp_settlement_price")
    @ApiModelProperty("暂定结算价格")
    private BigDecimal tempSettlementPrice;
    /**
     * 最终结算价格
     */
    @TableField(value = "final_settlement_price")
    @ApiModelProperty("最终结算价格")
    private BigDecimal finalSettlementPrice;
    /**
     * 风险类型：1正常2基差风险3点价风险
     */
    @TableField(value = "target_risk_control_type")
    @ApiModelProperty("风险类型：1正常2基差风险3点价风险")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem targetRiskControlType;
    /**
     * 基差风险状态：1正常2卖方风险3买方风险
     */
    @TableField(value = "basis_risk_control_status")
    @ApiModelProperty("基差风险状态：1正常2卖方风险3买方风险")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem basisRiskControlStatus;
    /**
     * 点价风险状态：1正常2卖方追保3卖方强平4买方追保5买方强平
     */
    @TableField(value = "pricing_risk_control_status")
    @ApiModelProperty("点价风险状态：1正常2卖方追保3卖方强平4买方追保5买方强平")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem pricingRiskControlStatus;
    /**
     * 风控时间
     */
    @TableField(value = "risk_control_time")
    @ApiModelProperty("追保强平时间")
    private Date riskControlTime;
    /**
     * 交割时间
     */
    @TableField(value = "delivery_time")
    @ApiModelProperty("交割时间")
    private Date deliveryTime;
    /**
     * 支付卖方货款比例
     */
    @TableField(value = "payment_seller_amount_ratio")
    @ApiModelProperty("支付卖方货款比例")
    private BigDecimal paymentSellerAmountRatio;
    /**
     * 收买方货款比例
     */
    @TableField(value = "collection_buyer_amount_ratio")
    @ApiModelProperty("收买方货款比例")
    private BigDecimal collectionBuyerAmountRatio;
    /**
     * 仓单抵免次数-卖方
     */
    @TableField(value = "seller_credit_num")
    @ApiModelProperty("仓单抵免次数-卖方")
    private Integer sellerCreditNum;
    /**
     * 仓单抵免总金额-卖方
     */
    @TableField(value = "seller_credit_amount")
    @ApiModelProperty("仓单抵免总金额-卖方")
    private BigDecimal sellerCreditAmount;
    /**
     * 仓单抵免次数-买方
     */
    @TableField(value = "buyer_credit_num")
    @ApiModelProperty("仓单抵免次数-买方")
    private Integer buyerCreditNum;
    /**
     * 仓单抵免总金额-买方
     */
    @TableField(value = "buyer_credit_amount")
    @ApiModelProperty("仓单抵免总金额-买方")
    private BigDecimal buyerCreditAmount;
    /**
    * 卖方基差保证金收取方式: 1-集团账户 2-电商
    */
    @TableField(value = "seller_basis_margin_type")
    @ApiModelProperty("卖方基差保证金收取方式: 1-集团账户 2-电商")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem sellerBasisMarginType;
    /**
    * 卖方基差保证金标准
    */
    @TableField(value = "seller_basis_margin_standard")
    @ApiModelProperty("卖方基差保证金标准")
    private BigDecimal sellerBasisMarginStandard;
    /**
    * 卖方基差保证金金额
    */
    @TableField(value = "seller_basis_margin_amount")
    @ApiModelProperty("卖方基差保证金金额")
    private BigDecimal sellerBasisMarginAmount;
    /**
    * 卖方交易手续费收取方式: 1-集团账户 2-电商
    */
    @TableField(value = "seller_trade_fee_type")
    @ApiModelProperty("卖方交易手续费收取方式: 1-集团账户 2-电商")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem sellerTradeFeeType;
    /**
    * 卖方交易手续费标准
    */
    @TableField(value = "seller_trade_fee_standard")
    @ApiModelProperty("卖方交易手续费标准")
    private BigDecimal sellerTradeFeeStandard;
    /**
    * 卖方手续费金额
    */
    @TableField(value = "seller_trade_fee_amount")
    @ApiModelProperty("卖方手续费金额")
    private BigDecimal sellerTradeFeeAmount;

    /**
     * 卖方手续费收取环节 1 成交时 2 随货款
     */
    @TableField(value = "seller_trade_fee_collect_phase")
    @ApiModelProperty("卖方手续费收取环节 1 成交时 2 随货款")
    private DataItem sellerTradeFeeCollectPhase;


    /**
    * 卖方交割手续费收取方式: 1-集团账户 2-电商
    */
    @TableField(value = "seller_delivery_fee_type")
    @ApiModelProperty("卖方交割手续费收取方式: 1-集团账户 2-电商")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem sellerDeliveryFeeType;
    /**
    * 卖方交割手续费标准
    */
    @TableField(value = "seller_delivery_fee_standard")
    @ApiModelProperty("卖方交割手续费标准")
    private BigDecimal sellerDeliveryFeeStandard;
    /**
    * 卖方交割手续费金额
    */
    @TableField(value = "seller_delivery_fee_amount")
    @ApiModelProperty("卖方交割手续费金额")
    private BigDecimal sellerDeliveryFeeAmount;
    /**
    * 卖方点价强平线
    */
    @TableField(value = "seller_pricing_forced_liquidation_line")
    @ApiModelProperty("卖方点价强平线")
    private BigDecimal sellerPricingForcedLiquidationLine;
    /**
    * 买方基差保证金收取方式: 1-集团账户 2-电商
    */
    @TableField(value = "buyer_basis_margin_type")
    @ApiModelProperty("买方基差保证金收取方式: 1-集团账户 2-电商")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem buyerBasisMarginType;
    /**
    * 买方基差保证金标准
    */
    @TableField(value = "buyer_basis_margin_standard")
    @ApiModelProperty("买方基差保证金标准")
    private BigDecimal buyerBasisMarginStandard;
    /**
    * 买方基差保证金金额
    */
    @TableField(value = "buyer_basis_margin_amount")
    @ApiModelProperty("买方基差保证金金额")
    private BigDecimal buyerBasisMarginAmount;
    /**
    * 买方交易手续费收取方式: 1-集团账户 2-电商
    */
    @TableField(value = "buyer_trade_fee_type")
    @ApiModelProperty("买方交易手续费收取方式: 1-集团账户 2-电商")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem buyerTradeFeeType;
    /**
    * 买方交易手续费标准
    */
    @TableField(value = "buyer_trade_fee_standard")
    @ApiModelProperty("买方交易手续费标准")
    private BigDecimal buyerTradeFeeStandard;
    /**
    * 买方交易手续费金额
    */
    @TableField(value = "buyer_trade_fee_amount")
    @ApiModelProperty("买方交易手续费金额")
    private BigDecimal buyerTradeFeeAmount;
    /**
    * 买方交割手续费收取方式: 1-集团账户 2-电商
    */
    @TableField(value = "buyer_delivery_fee_type")
    @ApiModelProperty("买方交割手续费收取方式: 1-集团账户 2-电商")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem buyerDeliveryFeeType;
    /**
    * 买方交割手续费标准
    */
    @TableField(value = "buyer_delivery_fee_standard")
    @ApiModelProperty("买方交割手续费标准")
    private BigDecimal buyerDeliveryFeeStandard;
    /**
    * 买方交割手续费金额
    */
    @TableField(value = "buyer_delivery_fee_amount")
    @ApiModelProperty("买方交割手续费金额")
    private BigDecimal buyerDeliveryFeeAmount;
    /**
     * 买方基差追保线
     */
    @TableField(value = "buyer_basis_margin_call_line")
    @ApiModelProperty("买方基差追保线")
    private BigDecimal buyerBasisMarginCallLine;
    /**
    * 买方基差追保标准
    */
    @TableField(value = "buyer_basis_margin_call_standard")
    @ApiModelProperty("买方基差追保标准")
    private BigDecimal buyerBasisMarginCallStandard;
    /**
    * 买方基差保证金金额
    */
    @TableField(value = "buyer_basis_margin_call_amount")
    @ApiModelProperty("买方基差保证金金额")
    private BigDecimal buyerBasisMarginCallAmount;
    /**
    * 买方点价追保线
    */
    @TableField(value = "buyer_pricing_margin_call_line")
    @ApiModelProperty("买方点价追保线")
    private BigDecimal buyerPricingMarginCallLine;
    /**
    * 买方点价保证金标准
    */
    @TableField(value = "buyer_pricing_margin_call_standard")
    @ApiModelProperty("买方点价保证金标准")
    private BigDecimal buyerPricingMarginCallStandard;
    /**
     * 买方点价强平线
     */
    @TableField(value = "buyer_pricing_forced_liquidation_line")
    @ApiModelProperty("买方点价强平线")
    private BigDecimal buyerPricingForcedLiquidationLine;
    /**
     * 买方点价保证金金额
     */
    @TableField(value = "buyer_pricing_margin_amount")
    @ApiModelProperty("买方点价保证金金额")
    private BigDecimal buyerPricingMarginAmount;
    /**
    * 已收买方金额
    */
    @TableField(value = "collection_buyer_amount")
    @ApiModelProperty("已收买方金额")
    private BigDecimal collectionBuyerAmount;
    /**
    * 已付卖方金额
    */
    @TableField(value = "payment_seller_amount")
    @ApiModelProperty("已付卖方金额")
    private BigDecimal paymentSellerAmount;
    /**
    * 买方点价保证金状态1已冻结2已释放3已扣除
    */
    @TableField(value = "buyer_pricing_margin_status")
    @ApiModelProperty("买方点价保证金状态1已冻结2已释放3已扣除")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem buyerPricingMarginStatus;
    /**
    * 批次交割状态：1未收买方2已收买方3已过户4已支付5已违约6已完结
    */
    @TableField(value = "batch_delivery_status")
    @ApiModelProperty("批次交割状态：1未收买方2已收买方3已过户4已支付5已违约6已完结")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem batchDeliveryStatus;
    /**
    * 发票状态：1未开票2已开票3买方已确认
    */
    @TableField(value = "invoices_status")
    @ApiModelProperty("发票状态：1未开票2已开票3买方已确认")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem invoicesStatus;
    /**
     * 发票确认时间
     */
    @TableField(value = "invoices_confirm_time")
    @ApiModelProperty("发票确认时间")
    private Date invoicesConfirmTime;
    /**
    * 买方货权确认状态1未确认2已确认
    */
    @TableField(value = "buyer_delivery_confirm_status")
    @ApiModelProperty("买方货权确认状态1未确认2已确认")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem buyerDeliveryConfirmStatus;
    /**
    * 货权确认时间
    */
    @TableField(value = "delivery_confirm_time")
    @ApiModelProperty("货权确认时间")
    private Date deliveryConfirmTime;

    /**
    * 生成合同状态 0未生成 1已生成
    */
    @TableField(value = "generate_contract_status")
    @ApiModelProperty("生成合同状态1未生成2已生成")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem generateContractStatus;
    /**
     * 卖方交易手续费状态 1 未收取 2 已收取 3 无需收取
     */
    @TableField(value = "seller_trade_fee_status")
    private Integer sellerTradeFeeStatus;

    /**
     * 卖方交易手续费交易码
     */
    @TableField(value = "seller_trade_fee_tx_code")
    private Long sellerTradeFeeTxCode;

    /**
     * 买方交易手续费状态 1 未收取 2 已收取 3 无需收取
     */
    @TableField(value = "buyer_trade_fee_status")
    private Integer buyerTradeFeeStatus;

    /**
     * 买方交易手续费交易码
     */
    @TableField(value = "buyer_trade_fee_tx_code")
    private Long buyerTradeFeeTxCode;

    /**
     * 运输补贴申领方
     */
    @TableField(value = "transport_subsidy_apply_party")
    @ApiModelProperty("运输补贴申领方")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem transportSubsidyApplyParty;

    /**
    * 创建时间
    */
    @TableField(value = "create_time")
    @ApiModelProperty("创建时间")
    private Date createTime;
    /**
    * 创建人id
    */
    @TableField(value = "create_user_id")
    @ApiModelProperty("创建人id")
    private String createUserId;
    /**
    * 创建人
    */
    @TableField(value = "create_user_name")
    @ApiModelProperty("创建人")
    private String createUserName;
    /**
    * 更新时间
    */
    @TableField(value = "update_time")
    @ApiModelProperty("更新时间")
    private Date updateTime;
    /**
    * 更新人id
    */
    @TableField(value = "update_user_id")
    @ApiModelProperty("更新人id")
    private String updateUserId;
    /**
    * 更新人
    */
    @TableField(value = "update_user_name")
    @ApiModelProperty("更新人")
    private String updateUserName;



}
