package com.basiscotton.base.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinosoft.dandelion.system.client.params.DataItem;
import com.sinosoft.dandelion.system.client.serializer.DataItemCodeDeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import lombok.Data;

 /**
 * <AUTHOR>
 * @version V1.0
 * @Title: MarketSetting.java
 * @Description: ${DESCRIPTION}
 * @date 2025/8/22 08:40
 */
/**
 * 市场公共设置表
 */
@ApiModel(description="市场设置表")
@Data
@TableName(value = "trd_basis_market_setting")
public class MarketSettingEntity {
    /**
     * 主键
     */
    @TableId(value = "id")
    @ApiModelProperty(value="主键")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    /**
     * 板块名称
     */
    @TableField(value = "setting_name")
    @ApiModelProperty(value="板块名称")
    private String settingName;

    /**
     * 设置类型:1、买方点价2、预售预购 3、现货挂牌
     */
    @TableField(value = "basis_setting_type")
    @ApiModelProperty(value="设置类型:1、买方点价2、预售预购 3、现货挂牌")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem basisSettingType;

    /**
     * 交易开始时间
     */
    @TableField(value = "trade_start_time")
    @ApiModelProperty(value="交易开始时间")
    private LocalTime tradeStartTime;

    /**
     * 交易结束时间
     */
    @TableField(value = "trade_end_time")
    @ApiModelProperty(value="交易结束时间")
    private LocalTime tradeEndTime;



    /**
     * 买方基差追保线
     */
    @TableField(value = "buyer_basis_margin_call_line")
    @ApiModelProperty(value="买方基差追保线")
    private BigDecimal buyerBasisMarginCallLine;

    /**
     * 买方基差追保标准
     */
    @TableField(value = "buyer_basis_margin_call_standard")
    @ApiModelProperty(value="买方基差追保标准")
    private BigDecimal buyerBasisMarginCallStandard;

    /**
     * 买方点价追保线
     */
    @TableField(value = "buyer_pricing_margin_call_line")
    @ApiModelProperty(value="买方点价追保线")
    private BigDecimal buyerPricingMarginCallLine;

    /**
     * 买方点价保证金标准
     */
    @TableField(value = "buyer_pricing_margin_call_standard")
    @ApiModelProperty(value="买方点价保证金标准")
    private BigDecimal buyerPricingMarginCallStandard;

    /**
     * 买方点价强平线
     */
    @TableField(value = "buyer_pricing_forced_liquidation_line")
    @ApiModelProperty(value="买方点价强平线")
    private BigDecimal buyerPricingForcedLiquidationLine;

    /**
     * 卖方点价强平线
     */
    @TableField(value = "seller_pricing_forced_liquidation_line")
    @ApiModelProperty(value="卖方点价强平线")
    private BigDecimal sellerPricingForcedLiquidationLine;

    /**
     * 仓单抵免次数+
     */
    @TableField(value = "warehouse_receipt_deposit_times")
    @ApiModelProperty(value="仓单抵免次数")
    private Integer warehouseReceiptDepositTimes;
    /**
     * 仓单抵免-单次保证金金额+
     */
    @TableField(value = "warehouse_receipt_deposit_onetime_amount")
    @ApiModelProperty(value="仓单抵免-单次保证金金额")
    private BigDecimal warehouseReceiptDepositOnetimeAmount;

    /**
     * 卖方基差保证金收取方式: 1-集团账户 2-电商 3-交易中心
     */
    @TableField(value = "seller_basis_margin_type")
    @ApiModelProperty(value="卖方基差保证金收取方式: 1-集团账户 2-电商 3-交易中心")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem sellerBasisMarginType;

    /**
     * 卖方基差保证金标准
     */
    @TableField(value = "seller_basis_margin_standard")
    @ApiModelProperty(value="卖方基差保证金标准")
    private BigDecimal sellerBasisMarginStandard;

    /** -------start卖方通用手续费设置----------*/
    /**
     * 卖方交易手续费收取方式: 1-集团账户 2-电商 3-交易中心
     */
    @TableField(value = "seller_trade_fee_type")
    @ApiModelProperty(value="卖方交易手续费收取方式: 1-集团账户 2-电商 3-交易中心")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem sellerTradeFeeType;

    /**
     * 卖方交易手续费标准
     */
    @TableField(value = "seller_trade_fee_standard")
    @ApiModelProperty(value="卖方交易手续费标准")
    private BigDecimal sellerTradeFeeStandard;

    /**
     * 卖方交割手续费收取方式: 1-集团账户 2-电商 3-交易中心
     */
    @TableField(value = "seller_delivery_fee_type")
    @ApiModelProperty(value="卖方交割手续费收取方式: 1-集团账户 2-电商 3-交易中心")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem sellerDeliveryFeeType;

    /**
     * 卖方交割手续费标准
     */
    @TableField(value = "seller_delivery_fee_standard")
    @ApiModelProperty(value="卖方交割手续费标准")
    private BigDecimal sellerDeliveryFeeStandard;

    /** -------end卖方通用手续费设置----------*/

    /**
     * 买方基差保证金收取方式: 1-集团账户 2-电商 3-交易中心
     */
    @TableField(value = "buyer_basis_margin_type")
    @ApiModelProperty(value="买方基差保证金收取方式: 1-集团账户 2-电商 3-交易中心")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem buyerBasisMarginType;

    /**
     * 买方基差保证金标准
     */
    @TableField(value = "buyer_basis_margin_standard")
    @ApiModelProperty(value="买方基差保证金标准")
    private BigDecimal buyerBasisMarginStandard;

    /** -------start 买方通用 费用设置----------*/

    /**
     * 买方交易手续费收取方式: 1-集团账户 2-电商 3-交易中心
     */
    @TableField(value = "buyer_trade_fee_type")
    @ApiModelProperty(value="买方交易手续费收取方式: 1-集团账户 2-电商 3-交易中心")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem buyerTradeFeeType;

    /**
     * 买方交易手续费标准
     */
    @TableField(value = "buyer_trade_fee_standard")
    @ApiModelProperty(value="买方交易手续费标准")
    private BigDecimal buyerTradeFeeStandard;

    /**
     * 买方交割手续费收取方式: 1-集团账户 2-电商 3-交易中心
     */
    @TableField(value = "buyer_delivery_fee_type")
    @ApiModelProperty(value="买方交割手续费收取方式: 1-集团账户 2-电商 3-交易中心")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem buyerDeliveryFeeType;

    /**
     * 买方交割手续费标准
     */
    @TableField(value = "buyer_delivery_fee_standard")
    @ApiModelProperty(value="买方交割手续费标准")
    private BigDecimal buyerDeliveryFeeStandard;

    /** -------end 买方通用 费用设置----------*/
    /**
     * 支付卖方货款比例
     */
    @TableField(value = "payment_seller_amount_ratio")
    @ApiModelProperty(value="支付卖方货款比例")
    private BigDecimal paymentSellerAmountRatio;

    /**
     * 收买方货款比例
     */
    @TableField(value = "collection_buyer_amount_ratio")
    @ApiModelProperty(value="收买方货款比例")
    private BigDecimal collectionBuyerAmountRatio;


    /**
     * 自动点价确认时间
     */
    @TableField(value = "auto_pricing_time")
    @ApiModelProperty(value="自动点价确认时间")
    private Integer autoPricingTime;

    /**
     * 合同是否自动生成1不生成2生成
     */
    @TableField(value = "contract_generate_status")
    @ApiModelProperty(value="合同是否自动生成1不生成2生成")
    private Integer contractGenerateStatus;


    /**
     * 是否自动审核备注 1-否，2-是
     */
    @TableField(value = "audit_remark_status")
    @ApiModelProperty(value="是否自动审核备注 1-否，2-是")
    private Integer auditRemarkStatus;

    /**
     * 是否启用
     */
    @TableField(value = "setting_status")
    @ApiModelProperty(value="是否启用")
    private String settingStatus;

    /**
     * 挂牌系统状态 1-正常，2-暂停
     */
    @TableField(value = "spot_system_status")
    @ApiModelProperty(value="挂牌系统状态 1-正常，2-暂停")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem spotSystemStatus;

    /**
     * 资源提交的最大有效天数
     */
    @TableField(value = "max_validity_submit_days")
    @ApiModelProperty(value="挂牌系统-资源提交的最大有效天数")
    private Integer maxValiditySubmitDays;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createTime;

    /**
     * 创建人id
     */
    @TableField(value = "create_user_id")
    @ApiModelProperty(value="创建人id")
    private String createUserId;

    /**
     * 创建人
     */
    @TableField(value = "create_user_name")
    @ApiModelProperty(value="创建人")
    private String createUserName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value="更新时间")
    private LocalDateTime updateTime;

    /**
     * 更新人id
     */
    @TableField(value = "update_user_id")
    @ApiModelProperty(value="更新人id")
    private String updateUserId;

    /**
     * 更新人
     */
    @TableField(value = "update_user_name")
    @ApiModelProperty(value="更新人")
    private String updateUserName;

}