package com.basiscotton.base.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinosoft.dandelion.system.client.params.DataItem;
import com.sinosoft.dandelion.system.client.serializer.DataItemCodeDeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* 用户操作记录表
* @TableName trd_basis_user_operate
*/
@ApiModel(description="用户操作记录表")
@Data
@TableName(value = "trd_basis_user_operate")
public class BasisUserOperateEntity implements Serializable {

    /**
    * 主键
    */
    @TableId(value = "id")
    @ApiModelProperty(value="主键")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;
    /**
    * 交易商代码
    */
    @TableField(value = "trader_code")
    @ApiModelProperty("交易商代码")
    private String traderCode;
    /**
    * 交易商名称
    */
    @TableField(value = "trader_name")
    @ApiModelProperty("交易商名称")
    private String traderName;
    /**
    * 操作类型：1提交资源2查询资源3关注资源4报价5手动成交
    */
    @TableField(value = "operate_type")
    @ApiModelProperty("操作类型：1提交资源2查询资源3关注资源4报价5手动成交")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem operateType;
    /**
    * 业务ID
    */
    @TableField(value = "business_id")
    @ApiModelProperty("业务ID")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long businessId;
    /**
    * 结果说明
    */
    @TableField(value = "operation_result")
    @ApiModelProperty("结果说明")
    private String operationResult;
    /**
    * 操作内容
    */
    @TableField(value = "operation_content")
    @ApiModelProperty("操作内容")
    private String operationContent;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty("创建时间")
    private Date createTime;
    /**
     * 创建人ID
     */
    @TableField(value = "create_user_id")
    @ApiModelProperty("创建人ID")
    private String createUserId;
    /**
     * 创建人
     */
    @TableField(value = "create_user_name")
    @ApiModelProperty("创建人")
    private String createUserName;

}
