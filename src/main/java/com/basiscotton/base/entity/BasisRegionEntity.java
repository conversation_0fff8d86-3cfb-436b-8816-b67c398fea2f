package com.basiscotton.base.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;

import java.io.Serializable;

import java.util.Date;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
* 基差交易区域表
* @TableName trd_basis_region
*/
@ApiModel(description="基差交易区域表")
@Data
@TableName(value = "trd_basis_region")
public class BasisRegionEntity implements Serializable {

    /**
    * 
    */
    @TableId(value = "id")
    @ApiModelProperty(value="主键")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;
    /**
    * 
    */
    @TableField(value = "PARENT_ID")
    @ApiModelProperty("")
    private Long parentId;
    /**
    * 
    */
    @TableField(value = "REGION_NAME")
    @ApiModelProperty("")
    private String regionName;
    /**
    * 
    */
    @TableField(value = "REGION_LEVEL")
    @ApiModelProperty("")
    private Integer regionLevel;
    /**
    * 
    */
    @TableField(value = "REGION_KEY")
    @ApiModelProperty("")
    private String regionKey;
    /**
    * 
    */
    @TableField(value = "SEQ")
    @ApiModelProperty("")
    private Integer seq;
    /**
    * 
    */
    @TableField(value = "CREATE_TIME")
    @ApiModelProperty("")
    private Date createTime;
    /**
    * 
    */
    @TableField(value = "UPDATE_TIME")
    @ApiModelProperty("")
    private Date updateTime;

}
