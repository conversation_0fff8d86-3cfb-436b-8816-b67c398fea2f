package com.basiscotton.base.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* 订单发票表
* @TableName trd_basis_orders_invoices
*/
@ApiModel(description="订单发票表")
@Data
@TableName(value = "trd_basis_orders_invoices")
public class BasisOrdersInvoicesEntity implements Serializable {

    /**
    * 主键
    */
    @TableId(value = "id")
	@ApiModelProperty(value="主键")
	@JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;
    /**
    * 开票交易商代码
    */
    @TableField(value = "provider_trader_code")
	@ApiModelProperty(value="开票交易商代码")
    private String providerTraderCode;
    /**
    * 开票交易商名称
    */
    @TableField(value = "provider_trader_name")
    @ApiModelProperty(value="开票交易商名称")
    private String providerTraderName;
    /**
    * 对方交易商代码
    */
    @TableField(value = "receiver_trader_code")
    @ApiModelProperty(value="对方交易商代码")
    private String receiverTraderCode;
    /**
    * 对方交易商名称
    */
    @TableField(value = "receiver_trader_name")
    @ApiModelProperty(value="对方交易商名称")
    private String receiverTraderName;
    /**
    * 发票文件id
    */
    @TableField(value = "invoices_file_id")
    @ApiModelProperty("发票文件id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long invoicesFileId;
    /**
    * 创建时间
    */
    @TableField(value = "create_time")
    @ApiModelProperty("创建时间")
    private Date createTime;
    /**
    * 创建人ID
    */
    @TableField(value = "create_user_id")
    @ApiModelProperty("创建人ID")
    private String createUserId;
    /**
    * 创建人
    */
    @TableField(value = "create_user_name")
    @ApiModelProperty("创建人")
    private String createUserName;

}
