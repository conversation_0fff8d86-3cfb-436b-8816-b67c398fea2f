package com.basiscotton.base.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinosoft.dandelion.system.client.params.DataItem;
import com.sinosoft.dandelion.system.client.serializer.DataItemCodeDeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

 /**
 * <AUTHOR>
 * @version V1.0
 * @Title: BasisPricingCommandEntity.java
 * @Description: ${DESCRIPTION}
 * @date 2025/8/21 16:16
 */
/**
 * 基差点价指令表
 */
@ApiModel(description="基差点价指令表")
@Data
@TableName(value = "trd_basis_pricing_command")
public class BasisPricingCommandEntity {
    /**
     * 主键
     */
    @TableId(value = "id")
    @ApiModelProperty(value="主键")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    /**
     * 标的id
     */
    @TableField(value = "target_id")
    @ApiModelProperty(value="标的id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long targetId;

    /**
     * 交易号
     */
    @TableField(value = "transaction_no")
    @ApiModelProperty(value="交易号")
    private String transactionNo;

    /**
     * 点价价格
     */
    @TableField(value = "pricing_price")
    @ApiModelProperty(value="点价价格")
    private BigDecimal pricingPrice;

    /**
     * 点价有效期
     */
    @TableField(value = "pricing_valid_time")
    @ApiModelProperty(value="点价有效期")
    private LocalDateTime pricingValidTime;

    /**
     * 批号
     */
    @TableField(value = "batch_no")
    @ApiModelProperty(value="批号")
    private String batchNo;

    /**
     * 仓单号
     */
    @TableField(value = "warehouse_receipt_no")
    @ApiModelProperty(value="仓单号")
    private String warehouseReceiptNo;

    /**
     * 期货合约代码
     */
    @TableField(value = "future_code")
    @ApiModelProperty(value="期货合约代码")
    private String futureCode;

    /**
     * 点价类型 1买方点价 2卖方点价
     */
    @TableField(value = "pricing_type")
    @ApiModelProperty(value="点价类型 1买方点价 2卖方点价")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem pricingType;

    /**
     * 点价方代码
     */
    @TableField(value = "pricing_commander_code")
    @ApiModelProperty(value="点价方代码")
    private String pricingCommanderCode;

    /**
     * 点价方名称
     */
    @TableField(value = "pricing_commander_name")
    @ApiModelProperty(value="点价方名称")
    private String pricingCommanderName;

    /**
     * 接收方代码
     */
    @TableField(value = "pricing_taker_code")
    @ApiModelProperty(value="接收方代码")
    private String pricingTakerCode;

    /**
     * 接收方名称
     */
    @TableField(value = "pricing_taker_name")
    @ApiModelProperty(value="接收方名称")
    private String pricingTakerName;

    /**
     * 指令状态
1预生效、
2已生效、
3自主撤销、
4系统撤销-到期结算--有指令时
5系统撤销-风控触线-有指令时
6点价击穿-自动
7对方确认-卖方
     */
    @TableField(value = "command_status")
    @ApiModelProperty(value="指令状态,1预生效、,2已生效、,3自主撤销、,4系统撤销-到期结算--有指令时,5系统撤销-风控触线-有指令时,6点价击穿-自动,7对方确认-卖方")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem commandStatus;

    /**
     * 指令创建时间
     */
    @TableField(value = "command_create_time")
    @ApiModelProperty(value="指令创建时间")
    private LocalDateTime commandCreateTime;

    /**
     * 指令生效时间
     */
    @TableField(value = "command_effect_time")
    @ApiModelProperty(value="指令生效时间")
    private LocalDateTime commandEffectTime;

    /**
     * 指令生效方式
1卖方确认
2系统确认-到时

     */
    @TableField(value = "command_effect_type")
    @ApiModelProperty(value="指令生效方式,1卖方确认,2系统确认-到时,")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem commandEffectType;

    /**
     * 点价完成时间
     */
    @TableField(value = "pricing_complete_time")
    @ApiModelProperty(value="点价完成时间")
    private LocalDateTime pricingCompleteTime;

    /**
     * 点价撤销时间
     */
    @TableField(value = "pricing_cancel_time")
    @ApiModelProperty(value="点价撤销时间")
    private LocalDateTime pricingCancelTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createTime;

    /**
     * 创建人id
     */
    @TableField(value = "create_user_id")
    @ApiModelProperty(value="创建人id")
    private String createUserId;

    /**
     * 创建人
     */
    @TableField(value = "create_user_name")
    @ApiModelProperty(value="创建人")
    private String createUserName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value="更新时间")
    private LocalDateTime updateTime;

    /**
     * 更新人id
     */
    @TableField(value = "update_user_id")
    @ApiModelProperty(value="更新人id")
    private String updateUserId;

    /**
     * 更新人
     */
    @TableField(value = "update_user_name")
    @ApiModelProperty(value="更新人")
    private String updateUserName;
}