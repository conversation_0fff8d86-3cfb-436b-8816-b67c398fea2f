package com.basiscotton.base.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinosoft.dandelion.system.client.params.DataItem;
import com.sinosoft.dandelion.system.client.serializer.DataItemCodeDeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
* 商品基础信息
* @TableName trd_basis_stock_base_info
*/
@ApiModel(description="商品基础信息")
@Data
@TableName(value = "trd_basis_stock_base_info")
public class BasisStockBaseInfoEntity implements Serializable {

    /**
    * 主键
    */
    @TableId(value = "id")
    @ApiModelProperty(value="主键")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;
    /**
    * 商品编码
    */
    @TableField(value = "stock_code")
    @ApiModelProperty("商品编码")
    private String stockCode;
    /**
    * 仓单号
    */
    @TableField(value = "warehouse_receipt_no")
    @ApiModelProperty("仓单号")
    private String warehouseReceiptNo;
    /**
    * 批号
    */
    @TableField(value = "batch_no")
    @ApiModelProperty("批号")
    private String batchNo;
    /**
    * 件数：186
    */
    @TableField(value = "quantity")
    @ApiModelProperty("件数：186")
    private Integer quantity;
    /**
    * 唛头重量
    */
    @TableField(value = "marks_weight")
    @ApiModelProperty("唛头重量")
    private BigDecimal marksWeight;
    /**
    * 公定重量
    */
    @TableField(value = "conditioned_weight")
    @ApiModelProperty("公定重量")
    private BigDecimal conditionedWeight;
    /**
    * 毛重
    */
    @TableField(value = "cross_weight")
    @ApiModelProperty("毛重")
    private BigDecimal crossWeight;
    /**
    * 棉花年度
    */
    @TableField(value = "product_year")
    @ApiModelProperty("棉花年度")
    private String productYear;
    /**
    * 加工单位代码
    */
    @TableField(value = "factory_code")
    @ApiModelProperty("加工单位代码")
    private String factoryCode;
    /**
    * 加工单位名称
    */
    @TableField(value = "factory_name")
    @ApiModelProperty("加工单位名称")
    private String factoryName;
    /**
    * 采摘方式：1、手摘棉 2机采棉
    */
    @TableField(value = "whs_pick_mode")
    @ApiModelProperty("采摘方式：1、手摘棉 2机采棉")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem whsPickMode;
    /**
    * 质量标识
    */
    @TableField(value = "mark_desc")
    @ApiModelProperty("质量标识")
    private String markDesc;
    /**
    * 主体颜色级
    */
    @TableField(value = "color_grade")
    @ApiModelProperty("主体颜色级")
    private String colorGrade;
    /**
    * 颜色级比率：白棉1级
    */
    @TableField(value = "white_cotton_l1")
    @ApiModelProperty("颜色级比率：白棉1级")
    private BigDecimal whiteCottonL1;
    /**
    * 颜色级比率：白棉2级
    */
    @TableField(value = "white_cotton_l2")
    @ApiModelProperty("颜色级比率：白棉2级")
    private BigDecimal whiteCottonL2;
    /**
    * 颜色级比率：白棉3级
    */
    @TableField(value = "white_cotton_l3")
    @ApiModelProperty("颜色级比率：白棉3级")
    private BigDecimal whiteCottonL3;
    /**
    * 颜色级比率：白棉4级
    */
    @TableField(value = "white_cotton_l4")
    @ApiModelProperty("颜色级比率：白棉4级")
    private BigDecimal whiteCottonL4;
    /**
    * 颜色级比率：白棉5级
    */
    @TableField(value = "white_cotton_l5")
    @ApiModelProperty("颜色级比率：白棉5级")
    private BigDecimal whiteCottonL5;
    /**
    * 颜色级比率：白棉6级
    */
    @TableField(value = "white_cotton_l6")
    @ApiModelProperty("颜色级比率：白棉6级")
    private BigDecimal whiteCottonL6;
    /**
    * 颜色级比率：淡点污棉1级
    */
    @TableField(value = "spot_cotton_l1")
    @ApiModelProperty("颜色级比率：淡点污棉1级")
    private BigDecimal spotCottonL1;
    /**
    * 颜色级比率：淡点污棉2级
    */
    @TableField(value = "spot_cotton_l2")
    @ApiModelProperty("颜色级比率：淡点污棉2级")
    private BigDecimal spotCottonL2;
    /**
    * 颜色级比率：淡点污棉3级
    */
    @TableField(value = "spot_cotton_l3")
    @ApiModelProperty("颜色级比率：淡点污棉3级")
    private BigDecimal spotCottonL3;
    /**
    * 颜色级比率：淡点污棉4级
    */
    @TableField(value = "spot_cotton_l4")
    @ApiModelProperty("颜色级比率：淡点污棉4级")
    private BigDecimal spotCottonL4;
    /**
    * 颜色级比率：淡黄染棉1级
    */
    @TableField(value = "yellow_ish_cotton_l1")
    @ApiModelProperty("颜色级比率：淡黄染棉1级")
    private BigDecimal yellowIshCottonL1;
    /**
    * 颜色级比率：淡黄染棉2级
    */
    @TableField(value = "yellow_ish_cotton_l2")
    @ApiModelProperty("颜色级比率：淡黄染棉2级")
    private BigDecimal yellowIshCottonL2;
    /**
    * 颜色级比率：淡黄染棉3级
    */
    @TableField(value = "yellow_ish_cotton_l3")
    @ApiModelProperty("颜色级比率：淡黄染棉3级")
    private BigDecimal yellowIshCottonL3;
    /**
    * 颜色级比率：黄染棉1级
    */
    @TableField(value = "yellow_cotton_l1")
    @ApiModelProperty("颜色级比率：黄染棉1级")
    private BigDecimal yellowCottonL1;
    /**
    * 颜色级比率：黄染棉2级
    */
    @TableField(value = "yellow_cotton_l2")
    @ApiModelProperty("颜色级比率：黄染棉2级")
    private BigDecimal yellowCottonL2;
    /**
    * 马克隆值
    */
    @TableField(value = "main_mkl")
    @ApiModelProperty("马克隆值")
    private String mainMkl;
    /**
    * 马克隆值级平均值
    */
    @TableField(value = "avg_mkl")
    @ApiModelProperty("马克隆值级平均值")
    private BigDecimal avgMkl;
    /**
    * 断裂比强度平均值
    */
    @TableField(value = "avg_break_rate")
    @ApiModelProperty("断裂比强度平均值")
    private BigDecimal avgBreakRate;
    /**
    * 主体长度
    */
    @TableField(value = "main_length")
    @ApiModelProperty("主体长度")
    private String mainLength;
    /**
    * 长度级平均值
    */
    @TableField(value = "avg_length")
    @ApiModelProperty("长度级平均值")
    private BigDecimal avgLength;
    /**
    * 轧工质量
    */
    @TableField(value = "rolling_quality")
    @ApiModelProperty("轧工质量")
    private String rollingQuality;
    /**
    * 轧工质量平均值
    */
    @TableField(value = "rolling_quality_avg")
    @ApiModelProperty("轧工质量平均值")
    private String rollingQualityAvg;
    /**
    * 长度整齐度平均值
    */
    @TableField(value = "uniformity_average_value")
    @ApiModelProperty("长度整齐度平均值")
    private String uniformityAverageValue;
    /**
    * 回潮率
    */
    @TableField(value = "moisture_rate")
    @ApiModelProperty("回潮率")
    private String moistureRate;
    /**
    * 含杂率
    */
    @TableField(value = "impurity_rate")
    @ApiModelProperty("含杂率")
    private String impurityRate;
    /**
    * 强力
    */
    @TableField(value = "break_value")
    @ApiModelProperty("强力")
    private BigDecimal breakValue;
    /**
    * 品名 1:长绒棉 2:细绒棉 3:彩棉 4:籽棉 5:棉浆粕 6:短绒 7:棉纱 8:涤纶短纤 9:粘胶短纤
    */
    @TableField(value = "stock_name")
    @ApiModelProperty("品名 1:长绒棉 2:细绒棉 3:彩棉 4:籽棉 5:棉浆粕 6:短绒 7:棉纱 8:涤纶短纤 9:粘胶短纤")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem stockName;
    /**
    * 产地编码：兵团-第二师
    */
    @TableField(value = "place_type")
    @ApiModelProperty("产地编码：兵团-第二师")
    private Integer placeType;
    /**
    * 具体产地
    */
    @TableField(value = "place_detail")
    @ApiModelProperty("具体产地")
    private String placeDetail;
    /**
    * 完整产地
    */
    @TableField(value = "intact_place")
    @ApiModelProperty("完整产地")
    private String intactPlace;
    /**
    * 公检日期
    */
    @TableField(value = "inspect_date")
    @ApiModelProperty("公检日期")
    private Date inspectDate;
    /**
    * 货权人代码
    */
    @TableField(value = "trader_code")
    @ApiModelProperty("货权人代码")
    private String traderCode;
    /**
    * 货权人名称
    */
    @TableField(value = "trader_name")
    @ApiModelProperty("货权人名称")
    private String traderName;
    /**
    * 仓库代码
    */
    @TableField(value = "storage_whs_code")
    @ApiModelProperty("仓库代码")
    private String storageWhsCode;
    /**
    * 仓库名称
    */
    @TableField(value = "storage_whs_name")
    @ApiModelProperty("仓库名称")
    private String storageWhsName;
    /**
    * 仓储业务状态：1、正常 2、注册中 3、注销中 4、移库中 5、过户中
    */
    @TableField(value = "storage_status")
    @ApiModelProperty("仓储业务状态：1、正常 2、注册中 3、注销中 4、移库中 5、过户中")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem storageStatus;
    /**
    * 资金监管状态：1、正常 2、三方监管-籽棉贷款 3、三方监管-皮棉调销 4、三方监管-融资监管状态 5、质押监管 6、质押及三方监管
    */
    @TableField(value = "whs_supervision_status")
    @ApiModelProperty("资金监管状态：1、正常 2、三方监管-籽棉贷款 3、三方监管-皮棉调销 4、三方监管-融资监管状态 5、质押监管 6、质押及三方监管")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem whsSupervisionStatus;
    /**
    * 监管机构代码
    */
    @TableField(value = "supervise_code")
    @ApiModelProperty("监管机构代码")
    private String superviseCode;
    /**
    * 监管机构名称
    */
    @TableField(value = "supervise_name")
    @ApiModelProperty("监管机构名称")
    private String superviseName;
    /**
    * 金融业务渠道：1正常、2购销、3金益棉、4棉贸通、5e棉通、6棉e通、7全棉通、8邮棉贷、9农商银行
    */
    @TableField(value = "whs_pledge_channel")
    @ApiModelProperty("金融业务渠道：1正常、2购销、3金益棉、4棉贸通、5e棉通、6棉e通、7全棉通、8邮棉贷、9农商银行")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem whsPledgeChannel;
    /**
    * 马克隆值级最大值
    */
    @TableField(value = "max_mkl")
    @ApiModelProperty("马克隆值级最大值")
    private BigDecimal maxMkl;
    /**
    * 马克隆值级最小值
    */
    @TableField(value = "min_mkl")
    @ApiModelProperty("马克隆值级最小值")
    private BigDecimal minMkl;
    /**
    * Rd平均值
    */
    @TableField(value = "AVG_RD")
    @ApiModelProperty("Rd平均值")
    private BigDecimal avgRd;
    /**
    * Rd最大值
    */
    @TableField(value = "MAX_RD")
    @ApiModelProperty("Rd最大值")
    private BigDecimal maxRd;
    /**
    * Rd最小值
    */
    @TableField(value = "MIN_RD")
    @ApiModelProperty("Rd最小值")
    private BigDecimal minRd;
    /**
    * +b平均值
    */
    @TableField(value = "AVG_PLUS_B")
    @ApiModelProperty("+b平均值")
    private BigDecimal avgPlusB;
    /**
    * +b最大值
    */
    @TableField(value = "MAX_PLUS_B")
    @ApiModelProperty("+b最大值")
    private BigDecimal maxPlusB;
    /**
    * +b最小值
    */
    @TableField(value = "MAX_PLUS_B")
    @ApiModelProperty("+b最小值")
    private BigDecimal minPlusB;

}
