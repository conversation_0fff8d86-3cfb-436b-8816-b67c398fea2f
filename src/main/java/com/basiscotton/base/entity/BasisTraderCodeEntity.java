package com.basiscotton.base.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* 基差交易商编码
* @TableName trd_basis_trader_code
*/
@ApiModel(description="基差交易商编码")
@Data
@TableName(value = "trd_basis_trader_code")
public class BasisTraderCodeEntity implements Serializable {

    /**
    * 主键
    */
    @TableId(value = "id")
    @ApiModelProperty(value="主键")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;
    /**
    * 交易商代码
    */
    @TableField(value = "trader_code")
    @ApiModelProperty("交易商代码")
    private String traderCode;
    /**
    * 交易商名称
    */
    @TableField(value = "trader_name")
    @ApiModelProperty("交易商名称")
    private String traderName;
    /**
    * 客户编码
    */
    @TableField(value = "custom_code")
    @ApiModelProperty("客户编码")
    private String customCode;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty("创建时间")
    private Date createTime;
    /**
     * 创建人ID
     */
    @TableField(value = "create_user_id")
    @ApiModelProperty("创建人ID")
    private String createUserId;
    /**
     * 创建人
     */
    @TableField(value = "create_user_name")
    @ApiModelProperty("创建人")
    private String createUserName;

}
