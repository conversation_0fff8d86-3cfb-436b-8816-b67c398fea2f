package com.basiscotton.base.entity;


import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinosoft.dandelion.system.client.params.DataItem;
import com.sinosoft.dandelion.system.client.serializer.DataItemCodeDeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Title: BasisPreStockBaseInfoEntity.java
 * @Description: 预售预购商品基础信息表
 * <AUTHOR>
 * @date 2025/5/28
 * @version V1.0
 */
/**
 * @Deprecated 暂时不使用
 */
@Deprecated
@ApiModel(description="预售预购商品基础信息表")
@Data
@TableName(value = "trd_basis_pre_stock_base_info")
public class BasisPreStockBaseInfoEntity implements Serializable {

	@TableId(value = "id")
	@ApiModelProperty(value="主键")
	@JSONField(serializeUsing = ToStringSerializer.class)
	private long id;


	/**
	 * 商品码
	 */
	@TableField(value = "stock_code")
	@ApiModelProperty(value="商品码")
	private String stockCode;

	/**
	 * 件数
	 */
	@TableField(value = "quantity")
	@ApiModelProperty(value="件数")
	private Integer quantity;

	/**
	 * 企业代码
	 */
	@TableField(value = "trader_code")
	@ApiModelProperty(value="企业代码")
	private String traderCode;

	/**
	 * 企业名称
	 */
	@TableField(value = "trader_name")
	@ApiModelProperty(value="企业名称")
	private String traderName;

	/**
	* 采摘方式：1、手摘棉 2机采棉
	*/
	@TableField(value = "whs_pick_mode")
    @ApiModelProperty(value="采摘方式：1、手摘棉 2机采棉")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem whsPickMode;

	/**
	 * 最小颜色级
	 */
	@TableField(value = "min_color_grade")
	@ApiModelProperty(value="最小颜色级")
	private BigDecimal minColorGrade;

	/**
	 * 最大颜色级
	 */
	@TableField(value = "max_color_grade")
	@ApiModelProperty(value="最大颜色级")
	private BigDecimal maxColorGrade;

	/**
	 * 最小马值
	 */
	@TableField(value = "min_mkl")
	@ApiModelProperty(value="最小马值")
	private BigDecimal minMkl;

	/**
	 * 最大马值
	 */
	@TableField(value = "max_mkl")
	@ApiModelProperty(value="最大马值")
	private BigDecimal maxMkl;
	/**
	 * 最小长度
	 */
	@TableField(value = "min_length")
	@ApiModelProperty(value="最小长度")
	private BigDecimal minLength;

	/**
	 * 最大长度
	 */
	@TableField(value = "max_length")
	@ApiModelProperty(value="最大长度")
	private BigDecimal maxLength;

	/**
	 * 最小强力
	 */
	@TableField(value = "min_break")
	@ApiModelProperty(value="最小强力")
	private BigDecimal minBreak;

	/**
	 * 最大强力
	 */
	@TableField(value = "max_break")
	@ApiModelProperty(value="最大强力")
	private BigDecimal maxBreak;

	/**
	 * 最小长整
	 */
	@TableField(value = "min_uniformity_average")
	@ApiModelProperty(value="最小长整")
	private BigDecimal minUniformityAverage;

	/**
	 * 最大长整
	 */
	@TableField(value = "max_uniformity_average")
	@ApiModelProperty(value="最大长整")
	private BigDecimal maxUniformityAverage;

	/**
	 * 最小含杂
	 */
	@TableField(value = "min_impurity")
	@ApiModelProperty(value="最小含杂")
	private BigDecimal minImpurity;

	/**
	 * 最大含杂
	 */
	@TableField(value = "max_impurity")
	@ApiModelProperty(value="最大含杂")
	private BigDecimal maxImpurity;

	/**
	 * 最小回潮
	 */
	@TableField(value = "min_moisture")
	@ApiModelProperty(value="最小回潮")
	private BigDecimal minMoisture;

	/**
	 * 最大回潮
	 */
	@TableField(value = "max_moisture")
	@ApiModelProperty(value="最大回潮")
	private BigDecimal maxMoisture;

	/**
	* 产地编码：兵团-第二师
	*/
	@TableField(value = "place_type")
	@ApiModelProperty(value="产地编码：兵团-第二师")
	private String placeType;

	/**
	 * 具体地址
	 */
	@TableField(value = "place_detail")
	@ApiModelProperty(value="具体地址")
	private String placeDetail;

	/**
	 * 完整地址
	 */
	@TableField(value = "intact_place")
	@ApiModelProperty(value="完整地址")
	private String intactPlace;

}
