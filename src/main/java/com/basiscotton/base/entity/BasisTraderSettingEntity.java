package com.basiscotton.base.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinosoft.dandelion.system.client.params.DataItem;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Deprecated 已废弃
 */
@Deprecated
@ApiModel(description="基差交易商设置表")
@Data
@TableName(value = "trd_basis_trader_setting")
public class BasisTraderSettingEntity implements Serializable {

    @TableId(value = "id")
    @ApiModelProperty(value="主键")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    /**
     * 设置类型
     */
    @TableField(value = "basis_setting_type")
    @ApiModelProperty(value="设置类型1、买方点价")
    private DataItem basisSettingType;

    @TableField(value = "trader_code")
    @ApiModelProperty(value="企业代码")
    private String traderCode;

    @TableField(value = "trader_name")
    @ApiModelProperty(value="企业名称")
    private String traderName;

    @TableField(value = "margin_standard")
    @ApiModelProperty(value="保证金标准")
    private BigDecimal marginStandard;

    @TableField(value = "trade_fee_standard")
    @ApiModelProperty(value="交易手续费标准")
    private BigDecimal tradeFeeStandard;

    @TableField(value = "delivery_fee_standard")
    @ApiModelProperty(value="交割手续费标准")
    private BigDecimal deliveryFeeStandard;

    @TableField("create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @TableField("create_user_id")
    @ApiModelProperty(value = "创建人ID")
    private String createUserId;

    @TableField("create_user_name")
    @ApiModelProperty(value = "创建人")
    private String createUserName;

    @TableField("update_time")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @TableField("update_user_id")
    @ApiModelProperty(value = "更新人ID")
    private String updateUserId;

    @TableField("update_user_name")
    @ApiModelProperty(value = "更新人")
    private String updateUserName;
}