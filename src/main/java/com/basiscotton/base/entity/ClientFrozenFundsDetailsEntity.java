package com.basiscotton.base.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinosoft.dandelion.system.client.params.DataItem;
import com.sinosoft.dandelion.system.client.serializer.DataItemCodeDeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel("客户冻结资金明细表")
@TableName("sc_client_frozen_funds_details")
@Data
public class ClientFrozenFundsDetailsEntity implements Serializable {

    private static final long serialVersionUID = 808325118L;

    /**
     * 客户资金账户代码
     */
    @TableId(value = "ID", type = IdType.AUTO)
    @ApiModelProperty(value = "流水号")
    private Long id;
    /**
     * 客户资金账户代码
     */
    @TableField("CLIENT_ACCOUNT_CODE")
    @ApiModelProperty(value = "客户资金账户代码")
    private String clientAccountCode;

    /**
     * 客户账户代码
     */
    @TableField("CLIENT_CODE")
    @ApiModelProperty(value = "客户账户代码")
    private String clientCode;

    /**
     * 结算码
     */
    @TableField("SETTLE_CODE")
    @ApiModelProperty(value = "结算码")
    private String settleCode;

    /**
     * 财务主体资金账户代码
     */
    @TableField("FE_ACCOUNT_CODE")
    @ApiModelProperty(value = "财务主体资金账户代码")
    private String feAccountCode;

    /**
     * 业务模块ID
     */
    @TableField("BUSINESS_MODULE_ID")
    @ApiModelProperty(value = "业务模块ID")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem businessModuleId;

    /**
     * 冻结资金
     */
    @TableField("FROZEN_FUNDS")
    @ApiModelProperty(value = "冻结资金")
    private BigDecimal frozenFunds;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("UPDATE_TIME")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;


}
