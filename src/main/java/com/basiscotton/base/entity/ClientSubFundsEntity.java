package com.basiscotton.base.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel("客户资金情况分表")
@TableName("sc_client_sub_funds")
@Data
public class ClientSubFundsEntity implements Serializable {

    private static final long serialVersionUID = -1084359452L;

    /**
     * 客户资金账户代码
     */
    @TableId(value = "CLIENT_ACCOUNT_CODE", type = IdType.INPUT)
    @ApiModelProperty(value = "客户资金账户代码")
    private String clientAccountCode;

    /**
     * 客户账户代码
     */
    @TableField("CLIENT_CODE")
    @ApiModelProperty(value = "客户账户代码")
    private String clientCode;

    /**
     * 结算码
     */
    @TableField("SETTLE_CODE")
    @ApiModelProperty(value = "结算码")
    private String settleCode;

    /**
     * 财务主体资金账户代码
     */
    @TableField("FE_ACCOUNT_CODE")
    @ApiModelProperty(value = "财务主体资金账户代码")
    private String feAccountCode;

    /**
     * 余额
     */
    @TableField("BALANCE")
    @ApiModelProperty(value = "余额")
    private BigDecimal balance;

    /**
     * 可用余额
     */
    @TableField("AVAILABLE_BALANCE")
    @ApiModelProperty(value = "可用余额")
    private BigDecimal availableBalance;

    /**
     * 可出金总额
     */
    @TableField("WITHDRAWABLE_BALANCE")
    @ApiModelProperty(value = "可用余额")
    private BigDecimal withdrawableBalance;

    /**
     * 闲置资金
     */
    @TableField("IDLE_FUNDS")
    @ApiModelProperty(value = "闲置资金")
    private BigDecimal idleFunds;

    /**
     * 冻结资金
     */
    @TableField("FROZEN_FUNDS")
    @ApiModelProperty(value = "冻结资金")
    private BigDecimal frozenFunds;

    /**
     * 暂扣资金（交易保证金）
     */
    @TableField("RETAINAGE")
    @ApiModelProperty(value = "暂扣资金（交易保证金）")
    private BigDecimal retainage;

    /**
     * 应付金额
     */
    @TableField("PAYABLE_FUNDS")
    @ApiModelProperty(value = "应付金额")
    private BigDecimal payableFunds;

    /**
     * 应收金额
     */
    @TableField("RECEIVABLE_FUNDS")
    @ApiModelProperty(value = "应收金额")
    private BigDecimal receivableFunds;

    /**
     * 预留金额
     */
    @TableField("RESERVED_FUNDS")
    @ApiModelProperty(value = "预留金额")
    private BigDecimal reservedFunds;

    /**
     * 欠费金额
     */
    @TableField("ARREARS")
    @ApiModelProperty(value = "欠费金额")
    private BigDecimal arrears;

    /**
     * 风险保证金
     */
    @TableField("RISK_MARGIN")
    @ApiModelProperty(value = "风险保证金")
    private BigDecimal riskMargin;

    /**
     * 专用款金额
     */
    @TableField("EARMARKED_FUNDS")
    @ApiModelProperty(value = "专用款金额")
    private BigDecimal earmarkedFunds;

    /**
     * 贷款支户总额
     */
    @TableField("LOAN_AMOUNTS")
    @ApiModelProperty(value = "贷款支户总额")
    private BigDecimal loanAmounts;

    /**
     * 贷款额度
     */
    @TableField("LOAN_LIMIT_AMOUNT")
    @ApiModelProperty(value = "贷款支户总额")
    private BigDecimal loanLimitAmount;

    /**
     * 贷款预付金(贷款预付户)
     */
    @TableField("LOAN_ADVANCE_AMOUNT")
    @ApiModelProperty(value = "贷款预付金(贷款预付户)")
    private BigDecimal loanAdvanceAmount;

    /**
     * 储备棉资金
     */
    @TableField("CBM_FUNDS")
    @ApiModelProperty(value = "储备棉资金")
    private BigDecimal cbmFunds;

    /**
     * 更新时间
     */
    @TableField("UPDATE_TIME")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
