package com.basiscotton.base.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinosoft.dandelion.system.client.params.DataItem;
import com.sinosoft.dandelion.system.client.serializer.DataItemCodeDeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
* 抵免业务关系表
* @TableName trd_basis_credit_business
*/
@ApiModel(description="抵免业务关系表")
@Data
@TableName(value = "trd_basis_credit_business")
public class BasisCreditBusinessEntity implements Serializable {

    /**
    * 主键
    */
    @TableId(value = "id")
    @ApiModelProperty(value="主键")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;
    /**
    * 抵免id
    */
    @TableField(value = "credit_id")
    @ApiModelProperty("抵免id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long creditId;
    /**
    * 抵免批次id
    */
    @TableField(value = "credit_batch_id")
    @ApiModelProperty("抵免批次id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long creditBatchId;
    /**
    * 业务id
    */
    @TableField(value = "business_id")
    @ApiModelProperty("业务id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long businessId;
    /**
    * 抵免类型：1报价2追保
    */
    @TableField(value = "credit_type")
    @ApiModelProperty("抵免类型：1报价2追保")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem creditType;
    /**
    * 资金类型：1交易保证金2基差保证金
    */
    @TableField(value = "funds_type")
    @ApiModelProperty("资金类型：1交易保证金2基差保证金")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem fundsType;
    /**
     * 抵免金额
     */
    @TableField(value = "credit_standard")
    @ApiModelProperty("抵免标准")
    private BigDecimal creditStandard;
    /**
    * 抵免金额
    */
    @TableField(value = "credit_amount")
    @ApiModelProperty("抵免金额")
    private BigDecimal creditAmount;
    /**
     * 抵免状态：1已抵免2已取消
     */
    @TableField(value = "business_credit_status")
    @ApiModelProperty("抵免状态：抵免状态：1已抵免2已取消")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem businessCreditStatus;
    /**
    * 创建时间
    */
    @TableField(value = "create_time")
    @ApiModelProperty("创建时间")
    private Date createTime;
    /**
    * 创建人id
    */
    @TableField(value = "create_user_id")
    @ApiModelProperty("创建人id")
    private String createUserId;
    /**
    * 创建人
    */
    @TableField(value = "create_user_name")
    @ApiModelProperty("创建人")
    private String createUserName;
    /**
    * 更新时间
    */
    @TableField(value = "update_time")
    @ApiModelProperty("更新时间")
    private Date updateTime;
    /**
    * 更新人id
    */
    @TableField(value = "update_user_id")
    @ApiModelProperty("更新人id")
    private String updateUserId;
    /**
    * 更新人
    */
    @TableField(value = "update_user_name")
    @ApiModelProperty("更新人")
    private String updateUserName;

}
