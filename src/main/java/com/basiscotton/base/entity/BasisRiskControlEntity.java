package com.basiscotton.base.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinosoft.dandelion.system.client.params.DataItem;
import com.sinosoft.dandelion.system.client.serializer.DataItemCodeDeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
* 风险控制表
* @TableName trd_basis_risk_control
*/
@ApiModel(description="风险控制表")
@Data
@TableName(value = "trd_basis_risk_control")
public class BasisRiskControlEntity implements Serializable {

    /**
    * 主键
    */
    @TableId(value = "id")
    @ApiModelProperty(value="主键")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;
    /**
    * 交易号
    */
    @TableField(value = "transaction_no")
    @ApiModelProperty("交易号")
    private String transactionNo;
    /**
    * 合同id
    */
    @TableField(value = "contract_id")
    @ApiModelProperty("合同id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long contractId;
    /**
    * 标的id
    */
    @TableField(value = "target_id")
    @ApiModelProperty("标的id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long targetId;
    /**
    * 商品id
    */
    @TableField(value = "stock_id")
    @ApiModelProperty("商品id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long stockId;
    /**
    * 商品基础信息id
    */
    @TableField(value = "stock_base_info_id")
    @ApiModelProperty("商品基础信息id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long stockBaseInfoId;
    /**
    * 追保文件id
    */
    @TableField(value = "file_id")
    @ApiModelProperty("追保文件id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long fileId;
    /**
     * 期货合约
     */
    @TableField(value = "future_code")
    @ApiModelProperty("期货合约")
    private String futureCode;
    /**
     * 批号
     */
    @TableField(value = "batch_no")
    @ApiModelProperty("批号")
    private String batchNo;
    /**
     * 仓单号
     */
    @TableField(value = "warehouse_receipt_no")
    @ApiModelProperty("仓单号")
    private String warehouseReceiptNo;
    /**
    * 交割方式：1先点价后交割2先交割后点价
    */
    @TableField(value = "delivery_type")
    @ApiModelProperty("交割方式：1先点价后交割2先交割后点价")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem deliveryType;
    /**
    * 保证金类型：1基差保证金2交易保证金
    */
    @TableField(value = "funds_type")
    @ApiModelProperty("保证金类型：1基差保证金2交易保证金")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem fundsType;
    /**
    * 追保标准
    */
    @TableField(value = "margin_call_standard")
    @ApiModelProperty("追保标准")
    private BigDecimal marginCallStandard;
    /**
    * 追保金额
    */
    @TableField(value = "margin_call_amount")
    @ApiModelProperty("追保金额")
    private BigDecimal marginCallAmount;
    /**
    * 追保时间
    */
    @TableField(value = "margin_call_time")
    @ApiModelProperty("追保时间")
    private Date marginCallTime;
    /**
    * 强平点价
    */
    @TableField(value = "liquidation_price")
    @ApiModelProperty("强平点价")
    private BigDecimal liquidationPrice;
    /**
     * 强平点价时间
     */
    @TableField(value = "liquidation_time")
    @ApiModelProperty("强平点价时间")
    private BigDecimal liquidationTime;
    /**
    * 追保方代码
    */
    @TableField(value = "payment_trader_code")
    @ApiModelProperty("追保方代码")
    private String paymentTraderCode;
    /**
    * 追保方名称
    */
    @TableField(value = "payment_trader_name")
    @ApiModelProperty("追保方名称")
    private String paymentTraderName;
    /**
    * 追保强平状态1追保中2已追保3已强平
    */
    @TableField(value = "risk_control_status")
    @ApiModelProperty("追保强平状态1追保中2已追保3已强平")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem riskControlStatus;
    /**
    * 创建时间
    */
    @TableField(value = "create_time")
    @ApiModelProperty("创建时间")
    private Date createTime;
    /**
    * 创建人id
    */
    @TableField(value = "create_user_id")
    @ApiModelProperty("创建人id")
    private String createUserId;
    /**
    * 创建人
    */
    @TableField(value = "create_user_name")
    @ApiModelProperty("创建人")
    private String createUserName;
    /**
    * 更新时间
    */
    @TableField(value = "update_time")
    @ApiModelProperty("更新时间")
    private Date updateTime;
    /**
    * 更新人id
    */
    @TableField(value = "update_user_id")
    @ApiModelProperty("更新人id")
    private String updateUserId;
    /**
    * 更新人
    */
    @TableField(value = "update_user_name")
    @ApiModelProperty("更新人")
    private String updateUserName;

}
