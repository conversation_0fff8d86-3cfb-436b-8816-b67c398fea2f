package com.basiscotton.base.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinosoft.dandelion.system.client.params.DataItem;
import com.sinosoft.dandelion.system.client.serializer.DataItemCodeDeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* 基差合同表
* @TableName trd_basis_contract
*/
@ApiModel(description="基差合同表")
@Data
@TableName(value = "trd_basis_contract")
public class BasisContractEntity implements Serializable {

    /**
    * 主键
    */
    @TableId(value = "id")
    @ApiModelProperty(value="主键")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;
    /**
    * 交易号
    */
    @TableField(value = "transaction_no")
    @ApiModelProperty("交易号")
    private String transactionNo;
    /**
    * 卖方交易商代码
    */
    @TableField(value = "seller_trader_code")
    @ApiModelProperty("卖方交易商代码")
    private String sellerTraderCode;
    /**
    * 卖方交易商名称
    */
    @TableField(value = "seller_trader_name")
    @ApiModelProperty("卖方交易商名称")
    private String sellerTraderName;
    /**
    * 买方交易商代码
    */
    @TableField(value = "buyer_trader_code")
    @ApiModelProperty("买方交易商代码")
    private String buyerTraderCode;
    /**
    * 买方交易商名称
    */
    @TableField(value = "buyer_trader_name")
    @ApiModelProperty("买方交易商名称")
    private String buyerTraderName;
    /**
    * 合同状态：1未完结2已违约3已取消4已完结
    */
    @TableField(value = "contract_status")
    @ApiModelProperty("合同状态：1未完结2已违约3已取消4已完结")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem contractStatus;
    /**
     * 交割方式：1先点价后交割2先交割后点价
     */
    @TableField(value = "delivery_type")
    @ApiModelProperty("交割方式：1先点价后交割2先交割后点价")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem deliveryType;
    /**
    * 创建时间
    */
    @TableField(value = "create_time")
    @ApiModelProperty("创建时间")
    private Date createTime;
    /**
    * 创建人id
    */
    @TableField(value = "create_user_id")
    @ApiModelProperty("创建人id")
    private String createUserId;
    /**
    * 创建人
    */
    @TableField(value = "create_user_name")
    @ApiModelProperty("创建人")
    private String createUserName;
    /**
    * 更新时间
    */
    @TableField(value = "update_time")
    @ApiModelProperty("更新时间")
    private Date updateTime;
    /**
    * 更新人id
    */
    @TableField(value = "update_user_id")
    @ApiModelProperty("更新人id")
    private String updateUserId;
    /**
    * 更新人
    */
    @TableField(value = "update_user_name")
    @ApiModelProperty("更新人")
    private String updateUserName;

}
