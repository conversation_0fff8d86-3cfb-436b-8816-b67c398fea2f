package com.basiscotton.base.entity;


import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinosoft.dandelion.system.client.params.DataItem;
import com.sinosoft.dandelion.system.client.serializer.DataItemCodeDeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Title: BasisPreStockEntity.java
 * @Description: 预售预购商品表
 * <AUTHOR>
 * @date 2025/5/28
 * @version V1.0
 */
/**
 * @Deprecated 暂时不使用
 */
@ApiModel(description="预售预购商品表")
@Data
@TableName(value = "trd_basis_pre_stock")
public class BasisPreStockEntity implements Serializable {

    /**
     * 主键
     */
    @TableId(value = "id")
    @ApiModelProperty(value="主键")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    @TableField(value = "stock_base_info_id")
    @ApiModelProperty(value="商品基础信息ID")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long stockBaseInfoId;

    /**
     * 商品编码
     */
    @TableField(value = "stock_code")
    @ApiModelProperty(value="商品编码")
    private String stockCode;

    @TableField(value = "factory_stock_id")
    @ApiModelProperty(value="加工厂商品id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long factoryStockId;

    /**
     *资源展示方式: 1-公开 2-指定
     */
    @TableField("resource_display_type")
    @ApiModelProperty(value = "资源展示方式: 1-公开 2-指定")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem resourceDisplayType;

    /**
     *资资源审核状态: 1-未提交 2-待审核 3-已上市 4-已取消
     */
    @TableField("resource_audit_status")
    @ApiModelProperty(value = "资源审核状态: 1-未提交 2-待审核 3-已上市 4-已取消")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem resourceAuditStatus;

    /**
     *报价方式: 1-一口价 2-锁基差 3-即时点价
     */
    @TableField("quote_type")
    @ApiModelProperty(value = "报价方式: 1-一口价 2-锁基差 3-即时点价")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem quoteType;

    /**
     *点价方: 1-买方 2-卖方
     */
    @TableField("pricing_party")
    @ApiModelProperty(value = "点价方: 1-买方 2-卖方")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem pricingParty;

    /**
     *预售预购重量
     */
    @TableField("pre_stock_weight")
    @ApiModelProperty(value = "预售预购重量")
    private BigDecimal preStockWeight;

    /**
     * 期货合约
     */
    @TableField(value = "future_code")
    @ApiModelProperty(value="期货合约")
    private String futureCode;

    /**
     *最小基差价格
     */
    @TableField("min_basis_price")
    @ApiModelProperty(value = "基差价格")
    private BigDecimal minBasisPrice;

    /**
     *最大基差价格
     */
    @TableField("max_basis_price")
    @ApiModelProperty(value = "基差价格")
    private BigDecimal maxBasisPrice;

    /**
     *点价有效期
     */
    @TableField("pricing_valid_time")
    @ApiModelProperty(value = "点价有效期")
    private Date pricingValidTime;

    /**
     *预计交货时间
     */
    @TableField("delivery_time")
    @ApiModelProperty(value = "预计交货时间")
    private Date deliveryTime;

    /**
     *商品来源: 1-预售 2-预购
     */
    @TableField("pre_stock_source")
    @ApiModelProperty(value = "商品来源: 1-预售 2-预购")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem preStockSource;

    /**
     *交易状态: 1-未成交 2-已成交 3-已交易 4-已流拍
     */
    @TableField("trade_status")
    @ApiModelProperty(value = "易状态: 1-未成交 2-已成交 3-已交易 4-已流拍")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem tradeStatus;

    /**
     * 企业代码
     */
    @TableField(value = "trader_code")
    @ApiModelProperty(value="企业代码")
    private String traderCode;

    /**
     * 企业名称
     */
    @TableField(value = "trader_name")
    @ApiModelProperty(value="企业名称")
    private String traderName;

    /**
     *卖方保证金收取方式: 1-集团账户 2-电商
     */
    @TableField("basis_seller_margin_type")
    @ApiModelProperty(value = "卖方保证金收取方式: 1-集团账户 2-电商")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem basisSellerMarginType;

    /**
     * 卖方保证金标准
     */
    @TableField(value = "seller_margin_standard")
    @ApiModelProperty(value="卖方保证金标准")
    private BigDecimal sellerMarginStandard;

    /**
     *卖方交易手续费收取方式: 1-集团账户 2-电商
     */
    @TableField("basis_seller_trade_fee_type")
    @ApiModelProperty(value = "卖方交易手续费收取方式: 1-集团账户 2-电商")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem basisSellerTradeFeeType;

    @TableField("basis_seller_delivery_fee_type")
    @ApiModelProperty(value = "卖方交割手续费收取方式: 1-集团账户 2-电商")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem basisSellerDeliveryFeeType;

    /**
     * 卖方手续费标准
     */
    @TableField(value = "seller_trade_fee_standard")
    @ApiModelProperty(value="卖方手续费标准")
    private BigDecimal sellerTradeFeeStandard;

    @TableField(value = "seller_delivery_fee_standard")
    @ApiModelProperty(value="卖方手续费标准")
    private BigDecimal sellerDeliveryFeeStandard;

    /**
     * 补充说明
     */
    @TableField(value = "supplement_content")
    @ApiModelProperty(value="补充说明")
    private String supplementContent;

    /**
     * 备注
     */
    @TableField(value = "remark")
    @ApiModelProperty(value="备注")
    private String remark;

    /**
     *创建时间
     */
    @TableField("create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     *创建人
     */
    @TableField("create_user_name")
    @ApiModelProperty(value = "创建人")
    private String createUserName;

    /**
     *创建人ID
     */
    @TableField("create_user_id")
    @ApiModelProperty(value = "创建人ID")
    private String createUserId;

    /**
     *修改时间
     */
    @TableField("update_time")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     *修改人
     */
    @TableField("update_user_name")
    @ApiModelProperty(value = "修改人")
    private String updateUserName;

    /**
     *修改人ID
     */
    @TableField("update_user_id")
    @ApiModelProperty(value = "修改人ID")
    private String updateUserId;

}
