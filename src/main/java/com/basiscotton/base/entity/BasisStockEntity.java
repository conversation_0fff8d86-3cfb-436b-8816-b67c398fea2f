package com.basiscotton.base.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinosoft.dandelion.system.client.params.DataItem;
import com.sinosoft.dandelion.system.client.serializer.DataItemCodeDeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
* 基差商品表
* @TableName trd_basis_stock
*/
@ApiModel(description="基差商品表")
@Data
@TableName(value = "trd_basis_stock")
public class BasisStockEntity implements Serializable {

    /**
    * 主键
    */
    @TableId(value = "id")
    @ApiModelProperty(value="主键")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;
    /**
    * 商品基础信息id
    */
    @TableField(value = "stock_base_info_id")
    @ApiModelProperty("商品基础信息id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long stockBaseInfoId;
    /**
    * 商品编码
    */
    @TableField(value = "stock_code")
    @ApiModelProperty("商品编码")
    private String stockCode;
    /**
    * 仓单号
    */
    @TableField(value = "warehouse_receipt_no")
    @ApiModelProperty("仓单号")
    private String warehouseReceiptNo;
    /**
    * 批号
    */
    @TableField(value = "batch_no")
    @ApiModelProperty("批号")
    private String batchNo;
    /**
    * 资源交易方式: 1单批
    */
    @TableField(value = "resource_trade_type")
    @ApiModelProperty("资源交易方式: 1单批")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem resourceTradeType;
    /**
    * 资源展示方式: 1-公开
    */
    @TableField(value = "resource_display_type")
    @ApiModelProperty("资源展示方式: 1-公开")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem resourceDisplayType;
    /**
    * 资源审核状态: 1-未审核 2-已审核 3-已取消 4-未提交
    */
    @TableField(value = "resource_audit_status")
    @ApiModelProperty("资源审核状态: 1-未审核 2-已审核 3-已取消 4-未提交")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem resourceAuditStatus;

    /**
     * 交易模式 1 现货挂牌 2 基差点价
     */
    private DataItem tradeMode;

    /**
    * 报价方式: 1-锁基差
    */
    @TableField(value = "quote_type")
    @ApiModelProperty("报价方式: 1-锁基差")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem quoteType;

    /**
    * 点价方式: 1 买方点价 2卖方点价
    */
    @TableField(value = "basis_pricing_type")
    @ApiModelProperty("点价方: 1-买方")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem basisPricingType;

    /**
    * 期货合约
    */
    @TableField(value = "future_code")
    @ApiModelProperty("期货合约")
    private String futureCode;
    /**
    * 商品价格
    */
    @TableField(value = "stock_price")
    @ApiModelProperty("商品价格")
    private BigDecimal stockPrice;

    @TableField(value = "stock_weight")
    @ApiModelProperty("商品重量")
    private BigDecimal stockWeight;

    /**
    * 点价有效期
    */
    @TableField(value = "pricing_valid_time")
    @ApiModelProperty("点价有效期")
    private Date pricingValidTime;

    /**
     * 挂牌有效期
     */
    @TableField(value = "stock_validity_end")
    @ApiModelProperty("挂牌有效期")
    private Date stockValidityEnd;

    /**
     * 可否议价 0 否 1 是
     */
    private Integer negotiable;

    /**
    * 利息成本
    */
    @TableField(value = "interest_cost")
    @ApiModelProperty("利息成本")
    private BigDecimal interestCost;
    /**
    * 商品来源: 1-仓单 2-非市场仓单
    */
    @TableField(value = "stock_source")
    @ApiModelProperty("商品来源: 1-市场仓单 2-非市场仓单")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem stockSource;
    /**
    * 交易状态: 1-未上架 2-未成交 3-已成交 4-已下架
    */
    @TableField(value = "trade_status")
    @ApiModelProperty("交易状态: 1-未上架 2-未成交 3-已成交 4-已下架")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem tradeStatus;
    /**
    * 下架原因
    */
    @TableField(value = "delisting_reason")
    @ApiModelProperty("下架原因")
    private String delistingReason;
    /**
    * 货权人代码
    */
    @TableField(value = "trader_code")
    @ApiModelProperty("货权人代码")
    private String traderCode;
    /**
    * 货权人名称
    */
    @TableField(value = "trader_name")
    @ApiModelProperty("货权人名称")
    private String traderName;
    /**
    * 卖方保证金收取方式: 1-集团账户 2-电商 3-交易中心
    */
    @TableField(value = "seller_basis_margin_type")
    @ApiModelProperty("卖方保证金收取方式: 1-集团账户 2-电商 3-交易中心")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem sellerBasisMarginType;
    /**
    * 卖方保证金标准
    */
    @TableField(value = "seller_basis_margin_standard")
    @ApiModelProperty("卖方保证金标准")
    private BigDecimal sellerBasisMarginStandard;
    /**
    * 卖方冻结保证金金额
    */
    @TableField(value = "seller_freeze_margin_amount")
    @ApiModelProperty("卖方冻结保证金金额")
    private BigDecimal sellerFreezeMarginAmount;
    /**
    * 卖方交易手续费收取方式: 1-集团账户 2-电商 3-交易中心
    */
    @TableField(value = "seller_trade_fee_type")
    @ApiModelProperty("卖方交易手续费收取方式: 1-集团账户 2-电商 3-交易中心")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem sellerTradeFeeType;
    /**
    * 卖方交易手续费标准
    */
    @TableField(value = "seller_trade_fee_standard")
    @ApiModelProperty("卖方交易手续费标准")
    private BigDecimal sellerTradeFeeStandard;
    /**
    * 卖方冻结交易手续费金额
    */
    @TableField(value = "seller_freeze_trade_fee_amount")
    @ApiModelProperty("卖方冻结交易手续费金额")
    private BigDecimal sellerFreezeTradeFeeAmount;

    /**
     * 运输补贴申领方
     */
    @TableField(value = "transport_subsidy_apply_party")
    @ApiModelProperty("运输补贴申领方")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem transportSubsidyApplyParty;

    /**
     * 卖方手续费收取环节 1 成交时 2 随货款
     */
    @TableField(value = "seller_trade_fee_collect_phase")
    @ApiModelProperty("卖方手续费收取环节 1 成交时 2 随货款")
    private DataItem sellerTradeFeeCollectPhase;

    /**
    * 卖方交割手续费收取方式: 1-集团账户 2-电商 3-交易中心
    */
    @TableField(value = "seller_delivery_fee_type")
    @ApiModelProperty("卖方交割手续费收取方式: 1-集团账户 2-电商 3-交易中心")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem sellerDeliveryFeeType;
    /**
    * 卖方交割手续费标准
    */
    @TableField(value = "seller_delivery_fee_standard")
    @ApiModelProperty("卖方交割手续费标准")
    private BigDecimal sellerDeliveryFeeStandard;
    /**
    * 卖方冻结交割手续费金额
    */
    @TableField(value = "seller_freeze_delivery_fee_amount")
    @ApiModelProperty("卖方冻结交割手续费金额")
    private BigDecimal sellerFreezeDeliveryFeeAmount;
    /**
    * 备注
    */
    @TableField(value = "remark")
    @ApiModelProperty("备注")
    private String remark;

    /**
    * 创建时间
    */
    @TableField(value = "create_time")
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
    * 创建人id
    */
    @TableField(value = "create_user_id")
    @ApiModelProperty("创建人id")
    private String createUserId;
    /**
    * 创建人
    */
    @TableField(value = "create_user_name")
    @ApiModelProperty("创建人")
    private String createUserName;
    /**
    * 更新时间
    */
    @TableField(value = "update_time")
    @ApiModelProperty("更新时间")
    private Date updateTime;
    /**
    * 更新人id
    */
    @TableField(value = "update_user_id")
    @ApiModelProperty("更新人id")
    private String updateUserId;
    /**
    * 更新人
    */
    @TableField(value = "update_user_name")
    @ApiModelProperty("更新人")
    private String updateUserName;

}
