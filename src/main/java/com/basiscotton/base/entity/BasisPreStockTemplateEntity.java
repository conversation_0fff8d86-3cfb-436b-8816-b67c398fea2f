package com.basiscotton.base.entity;


import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinosoft.dandelion.system.client.params.DataItem;
import com.sinosoft.dandelion.system.client.serializer.DataItemCodeDeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Title: BasisPreStockTemplateEntity.java
 * @Description: 预售预购商品模板表
 * <AUTHOR>
 * @date 2025/6/4
 * @version V1.0
 */
/**
 * @Deprecated 暂时不使用
 */
@ApiModel(description="预售预购商品模板表")
@Data
@TableName(value = "trd_basis_pre_stock_template")
public class BasisPreStockTemplateEntity implements Serializable {

    /**
     * 主键
     */
    @TableId(value = "id")
    @ApiModelProperty(value="主键")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;

    /**
     * 模板名称
     */
    @TableField(value = "template_name")
    @ApiModelProperty(value="模板名称")
    private String templateName;

    /**
     * 企业代码
     */
    @TableField(value = "trader_code")
    @ApiModelProperty(value="企业代码")
    private String traderCode;

    /**
     * 企业名称
     */
    @TableField(value = "trader_name")
    @ApiModelProperty(value="企业名称")
    private String traderName;

    /**
     *商品来源: 1-预售 2-预购
     */
    @TableField("pre_stock_source")
    @ApiModelProperty(value = "商品来源: 1-预售 2-预购")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem preStockSource;

    /**
     *预售预购重量
     */
    @TableField("pre_stock_weight")
    @ApiModelProperty(value = "预售预购重量")
    private BigDecimal preStockWeight;

    /**
     * 件数
     */
    @TableField(value = "quantity")
    @ApiModelProperty(value="件数")
    private Integer quantity;

    /**
     * 产地编码：兵团-第二师
     */
    @TableField(value = "place_type")
    @ApiModelProperty(value="产地编码：兵团-第二师")
    private String placeType;

    /**
     * 具体地址
     */
    @TableField(value = "place_detail")
    @ApiModelProperty(value="具体地址")
    private String placeDetail;

    /**
     * 完整地址
     */
    @TableField(value = "intact_place")
    @ApiModelProperty(value="完整地址")
    private String intactPlace;

    /**
     * 采摘方式：1、手摘棉 2机采棉
     */
    @TableField(value = "whs_pick_mode")
    @ApiModelProperty(value="采摘方式：1、手摘棉 2机采棉")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem whsPickMode;

    /**
     * 最小颜色级
     */
    @TableField(value = "min_color_grade")
    @ApiModelProperty(value="最小颜色级")
    private BigDecimal minColorGrade;

    /**
     * 最大颜色级
     */
    @TableField(value = "max_color_grade")
    @ApiModelProperty(value="最大颜色级")
    private BigDecimal maxColorGrade;

    /**
     * 最小马值
     */
    @TableField(value = "min_mkl")
    @ApiModelProperty(value="最小马值")
    private BigDecimal minMkl;

    /**
     * 最大马值
     */
    @TableField(value = "max_mkl")
    @ApiModelProperty(value="最大马值")
    private BigDecimal maxMkl;
    /**
     * 最小长度
     */
    @TableField(value = "min_length")
    @ApiModelProperty(value="最小长度")
    private BigDecimal minLength;

    /**
     * 最大长度
     */
    @TableField(value = "max_length")
    @ApiModelProperty(value="最大长度")
    private BigDecimal maxLength;

    /**
     * 最小强力
     */
    @TableField(value = "min_break")
    @ApiModelProperty(value="最小强力")
    private BigDecimal minBreak;

    /**
     * 最大强力
     */
    @TableField(value = "max_break")
    @ApiModelProperty(value="最大强力")
    private BigDecimal maxBreak;

    /**
     * 最小长整
     */
    @TableField(value = "min_uniformity_average")
    @ApiModelProperty(value="最小长整")
    private BigDecimal minUniformityAverage;

    /**
     * 最大长整
     */
    @TableField(value = "max_uniformity_average")
    @ApiModelProperty(value="最大长整")
    private BigDecimal maxUniformityAverage;

    /**
     * 最小含杂
     */
    @TableField(value = "min_impurity")
    @ApiModelProperty(value="最小含杂")
    private BigDecimal minImpurity;

    /**
     * 最大含杂
     */
    @TableField(value = "max_impurity")
    @ApiModelProperty(value="最大含杂")
    private BigDecimal maxImpurity;

    /**
     * 最小回潮
     */
    @TableField(value = "min_moisture")
    @ApiModelProperty(value="最小回潮")
    private BigDecimal minMoisture;

    /**
     * 最大回潮
     */
    @TableField(value = "max_moisture")
    @ApiModelProperty(value="最大回潮")
    private BigDecimal maxMoisture;

    /**
     * 补充说明
     */
    @TableField(value = "supplement_content")
    @ApiModelProperty(value="补充说明")
    private String supplementContent;

    /**
     * 期货合约
     */
    @TableField(value = "future_code")
    @ApiModelProperty(value="期货合约")
    private String futureCode;

    /**
     *最小基差价格
     */
    @TableField("min_basis_price")
    @ApiModelProperty(value = "基差价格")
    private BigDecimal minBasisPrice;

    /**
     *最大基差价格
     */
    @TableField("max_basis_price")
    @ApiModelProperty(value = "基差价格")
    private BigDecimal maxBasisPrice;

    /**
     *点价有效期
     */
    @TableField("pricing_valid_time")
    @ApiModelProperty(value = "点价有效期")
    private Date pricingValidTime;

    /**
     *预计交货时间
     */
    @TableField("delivery_time")
    @ApiModelProperty(value = "预计交货时间")
    private Date deliveryTime;

    /**
     * 备注
     */
    @TableField(value = "remark")
    @ApiModelProperty(value="备注")
    private String remark;

    /**
     *创建时间
     */
    @TableField("create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     *创建人
     */
    @TableField("create_user_name")
    @ApiModelProperty(value = "创建人")
    private String createUserName;

    /**
     *创建人ID
     */
    @TableField("create_user_id")
    @ApiModelProperty(value = "创建人ID")
    private String createUserId;

    /**
     *修改时间
     */
    @TableField("update_time")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    /**
     *修改人
     */
    @TableField("update_user_name")
    @ApiModelProperty(value = "修改人")
    private String updateUserName;

    /**
     *修改人ID
     */
    @TableField("update_user_id")
    @ApiModelProperty(value = "修改人ID")
    private String updateUserId;

}
