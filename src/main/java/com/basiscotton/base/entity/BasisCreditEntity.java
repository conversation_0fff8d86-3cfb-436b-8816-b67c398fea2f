package com.basiscotton.base.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinosoft.dandelion.system.client.params.DataItem;
import com.sinosoft.dandelion.system.client.serializer.DataItemCodeDeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* 仓单抵免协议表
* @TableName trd_basis_credit
*/
@ApiModel(description="仓单抵免协议表")
@Data
@TableName(value = "trd_basis_credit")
public class BasisCreditEntity implements Serializable {

    /**
    * 主键
    */
    @TableId(value = "id")
    @ApiModelProperty(value="主键")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;
    /**
    * 抵免编号
    */
    @TableField(value = "credit_code")
    @ApiModelProperty("抵免编号")
    private String creditCode;
    /**
    * 交易商代码
    */
    @TableField(value = "trader_code")
    @ApiModelProperty("交易商代码")
    private String traderCode;
    /**
    * 交易商名称
    */
    @TableField(value = "trader_name")
    @ApiModelProperty("交易商名称")
    private String traderName;
    /**
    * 抵免协议id
    */
    @TableField(value = "credit_file_id")
    @ApiModelProperty("抵免协议id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long creditFileId;
    /**
    * 审核状态：1已申请2已通过3已驳回
    */
    @TableField(value = "review_status")
    @ApiModelProperty("审核状态：1已申请2已通过3已驳回")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem reviewStatus;
    /**
    * 创建时间
    */
    @TableField(value = "create_time")
    @ApiModelProperty("创建时间")
    private Date createTime;
    /**
    * 创建人id
    */
    @TableField(value = "create_user_id")
    @ApiModelProperty("创建人id")
    private String createUserId;
    /**
    * 创建人
    */
    @TableField(value = "create_user_name")
    @ApiModelProperty("创建人")
    private String createUserName;
    /**
    * 更新时间
    */
    @TableField(value = "update_time")
    @ApiModelProperty("更新时间")
    private Date updateTime;
    /**
    * 更新人id
    */
    @TableField(value = "update_user_id")
    @ApiModelProperty("更新人id")
    private String updateUserId;
    /**
    * 更新人
    */
    @TableField(value = "update_user_name")
    @ApiModelProperty("更新人")
    private String updateUserName;

}
