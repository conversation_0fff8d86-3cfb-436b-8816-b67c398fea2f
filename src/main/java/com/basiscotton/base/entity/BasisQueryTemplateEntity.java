package com.basiscotton.base.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinosoft.dandelion.system.client.params.DataItem;
import com.sinosoft.dandelion.system.client.serializer.DataItemCodeDeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* 查询模板表
* @TableName trd_basis_query_template
*/
@ApiModel(description="查询模板表")
@Data
@TableName(value = "trd_basis_query_template")
public class BasisQueryTemplateEntity implements Serializable {

    /**
    * 主键
    */
    @TableId(value = "id")
    @ApiModelProperty(value="主键")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;
    /**
    * 模板类型1-全部、2-买方点价
    */
    @TableField(value = "template_type")
    @ApiModelProperty("模板类型1-全部、2-买方点价")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem templateType;
    /**
    * 模板名称
    */
    @TableField(value = "template_name")
    @ApiModelProperty("模板名称")
    private String templateName;
    /**
    * 模板内容-查询项json
    */
    @TableField(value = "template_value")
    @ApiModelProperty("模板内容-查询项json")
    private String templateValue;
    /**
    * 交易商代码
    */
    @TableField(value = "trader_code")
    @ApiModelProperty("交易商代码")
    private String traderCode;
    /**
    * 交易商名称
    */
    @TableField(value = "trader_name")
    @ApiModelProperty("交易商名称")
    private String traderName;
    /**
    * 
    */
    @TableField(value = "create_time")
    @ApiModelProperty("")
    private Date createTime;
    /**
    * 
    */
    @TableField(value = "create_user_id")
    @ApiModelProperty("")
    private String createUserId;
    /**
    * 
    */
    @TableField(value = "create_user_name")
    @ApiModelProperty("")
    private String createUserName;
    /**
    * 
    */
    @TableField(value = "update_time")
    @ApiModelProperty("")
    private Date updateTime;
    /**
    * 
    */
    @TableField(value = "update_user_id")
    @ApiModelProperty("")
    private String updateUserId;
    /**
    * 
    */
    @TableField(value = "update_user_name")
    @ApiModelProperty("")
    private String updateUserName;

}
