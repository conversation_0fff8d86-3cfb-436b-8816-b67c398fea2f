package com.basiscotton.base.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* 基差商品购物车表
* @TableName trd_basis_stock_cart
*/
@ApiModel(description="基差商品购物车表")
@Data
@TableName(value = "trd_basis_stock_cart")
public class BasisStockCartEntity implements Serializable {

    /**
    * 主键
    */
    @TableId(value = "id")
    @ApiModelProperty(value="主键")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;
    /**
    * 商品id
    */
    @TableField(value = "stock_id")
    @ApiModelProperty("商品id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long stockId;
    /**
    * 商品基础信息id
    */
    @TableField(value = "stock_base_info_id")
    @ApiModelProperty("商品基础信息id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long stockBaseInfoId;
    /**
    * 仓单号
    */
    @TableField(value = "warehouse_receipt_no")
    @ApiModelProperty("仓单号")
    private String warehouseReceiptNo;
    /**
    * 批号
    */
    @TableField(value = "batch_no")
    @ApiModelProperty("批号")
    private String batchNo;
    /**
    * 自选商品状态1添加自选2取消自选
    */
    @TableField(value = "stock_cart_status")
    @ApiModelProperty("自选商品状态1添加自选2取消自选")
    private Integer stockCartStatus;
    /**
    * 创建人
    */
    @TableField(value = "create_user_name")
    @ApiModelProperty("创建人")
    private String createUserName;
    /**
    * 创建人id
    */
    @TableField(value = "create_user_id")
    @ApiModelProperty("创建人id")
    private String createUserId;
    /**
    * 创建时间
    */
    @TableField(value = "create_time")
    @ApiModelProperty("创建时间")
    private Date createTime;

}
