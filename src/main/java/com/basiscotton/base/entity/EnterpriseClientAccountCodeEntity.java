package com.basiscotton.base.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@ApiModel("企业结算码关联表")
@TableName("sc_enterprise_client_account_code")
@Data
public class EnterpriseClientAccountCodeEntity implements Serializable {

    private static final long serialVersionUID = 7212421270918221981L;

    /**
     * 主键
     */
    @TableId("ID")
    @ApiModelProperty(value = "主键")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;


    /**
     * 企业ID
     */
    @TableField("ENTERPRISE_ID")
    @ApiModelProperty(value = "企业ID")
    private Long enterpriseId;

    /**
     * 企业结算码
     */
    @TableField("ENTERPRISE_SETTLEMENT_CODE")
    @ApiModelProperty(value = "企业结算码")
    private String enterpriseSettlementCode;

    /**
     * 客户代码
     */
    @TableField("CLIENT_CODE")
    @ApiModelProperty(value = "客户代码")
    private String clientCode;

    /**
     * 客户名称
     */
    @TableField("CLIENT_ACCOUNT_NAME")
    @ApiModelProperty(value = "客户名称")
    private String clientAccountName;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("UPDATE_TIME")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
