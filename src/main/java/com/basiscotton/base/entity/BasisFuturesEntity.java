package com.basiscotton.base.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* 基差期货合约表
* @TableName trd_basis_futures
*/
@ApiModel(description="基差期货合约表")
@Data
@TableName(value = "trd_basis_futures")
public class BasisFuturesEntity implements Serializable {

    /**
    * 主键
    */
    @TableId(value = "id")
    @ApiModelProperty(value="主键")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;
    /**
    * 期货合约
    */
    @TableField(value = "future_code")
    @ApiModelProperty("期货合约")
    private String futureCode;
    /**
    * 是否为主力合约 0否 1是
    */
    @TableField(value = "active_future")
    @ApiModelProperty("是否为主力合约 0否 1是")
    private Integer activeFuture;
    /**
    * 备注
    */
    @TableField(value = "remark")
    @ApiModelProperty("备注")
    private String remark;
    /**
    * 
    */
    @TableField(value = "create_user_name")
    @ApiModelProperty("")
    private String createUserName;
    /**
    * 
    */
    @TableField(value = "create_user_id")
    @ApiModelProperty("")
    private String createUserId;
    /**
    * 
    */
    @TableField(value = "create_time")
    @ApiModelProperty("")
    private Date createTime;

}
