package com.basiscotton.base.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sinosoft.dandelion.system.client.params.DataItem;
import com.sinosoft.dandelion.system.client.serializer.DataItemCodeDeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
* 基差交易报价表
* @TableName trd_basis_quote
*/
@ApiModel(description="基差交易报价表")
@Data
@TableName(value = "trd_basis_quote")
public class BasisQuoteEntity implements Serializable {

    /**
    * 主键
    */
    @TableId(value = "id")
    @ApiModelProperty(value="主键")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long id;
    /**
    * 商品id
    */
    @TableField(value = "stock_id")
    @ApiModelProperty("商品id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long stockId;
    /**
    * 商品编码
    */
    @TableField(value = "stock_code")
    @ApiModelProperty("商品编码")
    private String stockCode;
    /**
    * 商品基础信息id
    */
    @TableField(value = "stock_base_info_id")
    @ApiModelProperty("商品基础信息id")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Long stockBaseInfoId;
    /**
    * 委托账户名称
    */
    @TableField(value = "user_name")
    @ApiModelProperty("委托账户名称")
    private String userName;
    /**
    * 委托交易商代码
    */
    @TableField(value = "trader_code")
    @ApiModelProperty("委托交易商代码")
    private String traderCode;
    /**
    * 委托交易商名称
    */
    @TableField(value = "trader_name")
    @ApiModelProperty("委托交易商名称")
    private String traderName;
    /**
    * 报价状态：1未成交、2 成交、3 撤销
    */
    @TableField(value = "quote_status")
    @ApiModelProperty("报价状态 1: 未成交, 2: 已成交, 3: 已撤销")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem quoteStatus;

    /**
    * 报价取消方式1 买家自主撤销, 2 其它买家成交, 3 卖家撤销商品, 4 卖家确认
    */
    @TableField(value = "quote_cancel_type")
    @ApiModelProperty("报价取消方式1 买家自主撤销, 2 其它买家成交, 3 卖家撤销商品, 4 卖家确认")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem quoteCancelType;

    /**
    * 委托价格
    */
    @TableField(value = "quote_price")
    @ApiModelProperty("委托价格")
    private BigDecimal quotePrice;
    /**
    * 委托重量
    */
    @TableField(value = "quote_weight")
    @ApiModelProperty("委托重量")
    private BigDecimal quoteWeight;

    /**
    * 报价时间
    */
    @TableField(value = "quote_time")
    @ApiModelProperty("报价时间")
    private Date quoteTime;

    /**
    * 买方集成保证金收取方式: 1-集团账户 2-电商
    */
    @TableField(value = "buyer_basis_margin_type")
    @ApiModelProperty("买方集成保证金收取方式: 1-集团账户 2-电商")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem buyerBasisMarginType;
    /**
    * 买方集成保证金标准
    */
    @TableField(value = "buyer_basis_margin_standard")
    @ApiModelProperty("买方集成保证金标准")
    private BigDecimal buyerBasisMarginStandard;
    /**
    * 买方集成保证金金额
    */
    @TableField(value = "buyer_basis_margin_amount")
    @ApiModelProperty("买方集成保证金金额")
    private BigDecimal buyerBasisMarginAmount;
    /**
    * 买方交易手续费收取方式: 1-集团账户 2-电商
    */
    @TableField(value = "buyer_trade_fee_type")
    @ApiModelProperty("买方交易手续费收取方式: 1-集团账户 2-电商")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem buyerTradeFeeType;
    /**
    * 买方交易手续费标准
    */
    @TableField(value = "buyer_trade_fee_standard")
    @ApiModelProperty("买方交易手续费标准")
    private BigDecimal buyerTradeFeeStandard;
    /**
    * 买方交易手续费金额
    */
    @TableField(value = "buyer_trade_fee_amount")
    @ApiModelProperty("买方交易手续费金额")
    private BigDecimal buyerTradeFeeAmount;
    /**
    * 买方交割手续费收取方式: 1-集团账户 2-电商
    */
    @TableField(value = "buyer_delivery_fee_type")
    @ApiModelProperty("买方交割手续费收取方式: 1-集团账户 2-电商")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem buyerDeliveryFeeType;
    /**
    * 买方交割手续费标准
    */
    @TableField(value = "buyer_delivery_fee_standard")
    @ApiModelProperty("买方交割手续费标准")
    private BigDecimal buyerDeliveryFeeStandard;
    /**
    * 买方交割手续费金额
    */
    @TableField(value = "buyer_delivery_fee_amount")
    @ApiModelProperty("买方交割手续费金额")
    private BigDecimal buyerDeliveryFeeAmount;
    /**
    * 创建时间
    */
    @TableField(value = "create_time")
    @ApiModelProperty("创建时间")
    private Date createTime;
    /**
    * 创建人id
    */
    @TableField(value = "create_user_id")
    @ApiModelProperty("创建人id")
    private String createUserId;
    /**
    * 创建人
    */
    @TableField(value = "create_user_name")
    @ApiModelProperty("创建人")
    private String createUserName;
    /**
    * 更新时间
    */
    @TableField(value = "update_time")
    @ApiModelProperty("更新时间")
    private Date updateTime;
    /**
    * 更新人id
    */
    @TableField(value = "update_user_id")
    @ApiModelProperty("更新人id")
    private String updateUserId;
    /**
    * 更新人
    */
    @TableField(value = "update_user_name")
    @ApiModelProperty("更新人")
    private String updateUserName;

}
