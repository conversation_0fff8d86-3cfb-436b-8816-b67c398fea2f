package com.basiscotton.base;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: ExceptionEnums.java
 * @Description: 错误提示
 * @date 2021年12月29日
 */
public enum ExceptionEnums {

    // 公共模块
    /**
     * 请求参数不能为空
     */
    S0001("S0001", "请求参数不能为空！"),
    S0004("S0004", "数据转换异常！"),
    S0005("S0005", "请求重复！"),
    /**
     * id不能为空！
     */
    S0002("S0002", "id不能为空！"),

    /**
     * 分页请求参数为空！
     */
    S0003("S0003", "分页请求参数为空！"),

    B0216("B0216", "合同编号不存在"),


;
    @Getter
    private String eCode;
    @Getter
    private String eMsg;

    ExceptionEnums(String eCode, String eMsg) {
        this.eCode = eCode;
        this.eMsg = eMsg;
    }
}