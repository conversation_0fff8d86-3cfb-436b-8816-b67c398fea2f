package com.basiscotton.base.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum FinancialEntity {

    /**
     * 集团
     */
    GROUP("A01"),
    /**
     * 电商
     */
    E_COMMERCE("A02"),
    /**
     * 交易中心
     */
    TRADE_CENTER("A301"),
    ;

    /**
     * 财务主体代码
     */
    private final String feAccountCode;

    private static final Map<String, FinancialEntity> valueMap = Arrays.stream(values())
            .collect(Collectors.toMap(FinancialEntity::getFeAccountCode, v -> v));

    FinancialEntity(String feAccountCode) {
        this.feAccountCode = feAccountCode;
    }

    public static FinancialEntity parseFromFeAccountCode(String feAccountCode) {
        return valueMap.get(feAccountCode);
    }
}
