package com.basiscotton.base.enums;


import lombok.Getter;

import static com.basiscotton.base.BasisCottonConstants.DELISTING_REASON_1;
import static com.basiscotton.base.BasisCottonConstants.DELISTING_REASON_2;
import static com.basiscotton.base.BasisCottonConstants.DELISTING_REASON_3;

@Getter
public enum StockCancelType {

    /**
     * 卖家撤销
     */
    SELLER_WITHDRAW(DELISTING_REASON_1),
    /**
     * 其他业务
     */
    OTHER_BUSINESS(DELISTING_REASON_2),
    /**
     * 到期撤销
     */
    EXPIRED(DELISTING_REASON_3),

    ;
    private final String value;

    StockCancelType(String value) {
        this.value = value;
    }
}
