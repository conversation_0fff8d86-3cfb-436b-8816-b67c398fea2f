package com.basiscotton.base.enums;

import com.sinosoft.dandelion.system.client.params.DataItem;
import lombok.Getter;

import static com.basiscotton.base.BasisCottonConstants.CONTRACT_STATUS_1;
import static com.basiscotton.base.BasisCottonConstants.CONTRACT_STATUS_2;
import static com.basiscotton.base.BasisCottonConstants.CONTRACT_STATUS_3;
import static com.basiscotton.base.BasisCottonConstants.CONTRACT_STATUS_4;

/**
 * 合同状态枚举
 */
@Getter
public enum ContractStatus {

    /**
     * 未完结
     */
    UNFINISHED(CONTRACT_STATUS_1),
    /**
     * 已违约
     */
    BREACHED(CONTRACT_STATUS_2),
    /**
     * 已取消
     */
    CANCELED(CONTRACT_STATUS_3),
    /**
     * 已完结
     */
    FINISHED(CONTRACT_STATUS_4),
    ;

    private final Integer value;

    ContractStatus(Integer value) {
        this.value = value;
    }

    public DataItem toDataItem() {
        return new DataItem(null, this.value.toString());
    }
}
