package com.basiscotton.base.enums;

import com.sinosoft.cnce.sc.base.enumeration.BusinessModuleEnum;
import com.sinosoft.dandelion.system.client.params.DataItem;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
public enum TradeMode {

    /**
     * 现货挂牌
     */
    SPOT_LISTING("1", BusinessModuleEnum.SPOT_LISTING.businessModuleId.toString()),

    /**
     * 基差点价
     */
    BASIS_PRICING("2", BusinessModuleEnum.WAREHOUSE_RECEIPT_TRADE.businessModuleId.toString()),
    ;

    private final String value;

    private final String businessModuleId;

    private static final Map<String, TradeMode> VALUE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(TradeMode::getValue, Function.identity()));

    TradeMode(String value, String businessModuleId) {
        this.value = value;
        this.businessModuleId = businessModuleId;
    }

    public static TradeMode getByValue(String value) {
        return VALUE_MAP.get(value);
    }

    public static TradeMode parseDataItem(DataItem dataItem) {
        return VALUE_MAP.get(dataItem.getCode());
    }

    public DataItem getDataItem() {
        return new DataItem(businessModuleId, null);
    }

}
