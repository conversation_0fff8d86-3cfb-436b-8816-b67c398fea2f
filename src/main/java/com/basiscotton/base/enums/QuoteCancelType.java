package com.basiscotton.base.enums;

import com.sinosoft.dandelion.system.client.params.DataItem;
import lombok.Getter;

import static com.basiscotton.base.BasisCottonConstants.QUOTE_CANCEL_TYPE_1;
import static com.basiscotton.base.BasisCottonConstants.QUOTE_CANCEL_TYPE_2;
import static com.basiscotton.base.BasisCottonConstants.QUOTE_CANCEL_TYPE_3;
import static com.basiscotton.base.BasisCottonConstants.QUOTE_CANCEL_TYPE_4;

/**
 * 报价撤销类型枚举
 */
@Getter
public enum QuoteCancelType {
    /**
     * 买方撤销
     */
    BUYER_CANCEL(QUOTE_CANCEL_TYPE_1),
    /**
     * 其他撮合撤销
     */
    OTHER_MATCH(QUOTE_CANCEL_TYPE_2),
    /**
     * 卖家撤销
     */
    SELLER_CANCEL(QUOTE_CANCEL_TYPE_3),
    /**
     * 系统撤销
     */
    SYSTEM_CANCEL(QUOTE_CANCEL_TYPE_4);

    private final String value;

    QuoteCancelType(String value) {
        this.value = value;
    }

    public DataItem getDataItem() {
        return new DataItem(null, value);
    }
}
