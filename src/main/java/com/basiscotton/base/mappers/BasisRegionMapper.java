package com.basiscotton.base.mappers;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.basiscotton.base.entity.BasisRegionEntity;
import com.basiscotton.common.region.vo.RegionTreeVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Title: BasisRegionMapper.java
 * @Description: 基差区域mapper
 * <AUTHOR>
 * @date 2025/5/28
 * @version V1.0
 */
public interface BasisRegionMapper extends BaseMapper<BasisRegionEntity> {

    List<RegionTreeVo> selectRegionTreeList(@Param("parentKey") String parentKey);
}