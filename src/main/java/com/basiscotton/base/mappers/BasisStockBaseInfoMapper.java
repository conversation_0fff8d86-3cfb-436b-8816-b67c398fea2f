package com.basiscotton.base.mappers;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.basiscotton.base.entity.BasisStockBaseInfoEntity;
import com.basiscotton.manager.hall.vo.BatchInspectVo;
import com.basiscotton.manager.hall.vo.BatchWeightVo;
import com.basiscotton.manager.resources.manage.vo.MBasisWhsReceiptParamVo;
import com.basiscotton.manager.resources.manage.vo.MBasisWhsReceiptVo;
import com.basiscotton.manager.stock.vo.BasisBatchInfoVo;
import com.basiscotton.manager.stock.vo.BasisWhsReceiptParamVo;
import com.basiscotton.manager.stock.vo.BasisWhsReceiptVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.kafka.common.protocol.types.Field;
import org.harry.dandelion.framework.rep.support.page.PipPagination;

import java.util.List;

/**
 * @Title: BasisBaseStockMapper.java
 * @Description: 商品基础信息mapper
 * <AUTHOR>
 * @date 2025/5/23
 * @version V1.0
 */
@Mapper
public interface BasisStockBaseInfoMapper extends BaseMapper<BasisStockBaseInfoEntity> {
    /**
     * 交易商端-查询交易商下仓单信息
     * @param pipPagination
     * @param paramVo
     * @return
     */
    PipPagination<BasisWhsReceiptVo> selectBasisWarehouseReceiptByTraderCode(PipPagination<BasisWhsReceiptVo> pipPagination, @Param("paramVo") BasisWhsReceiptParamVo paramVo);

    //从仓储视图提取公检信息
    List<BasisBatchInfoVo> getBatchInfoVo(@Param("warehouseReceiptNumberList")List<String> warehouseReceiptNumberList);

    Integer getBatchInfoVoByWhsNo(String whsNo);

    PipPagination<MBasisWhsReceiptVo> selectBasisWarehouseReceipt(PipPagination<MBasisWhsReceiptVo> pipPagination,@Param("paramVo") MBasisWhsReceiptParamVo paramVo);

    BasisWhsReceiptVo getWhsReceiptVo(@Param("wareNo") String wareNo);

    BatchInspectVo getBatchInspectVo(@Param("batchNo") String batchNo);

    BatchWeightVo getBatchWeightVo(@Param("batchNo") String batchNo);

    /**
     * 查询可以进行交易的仓单
     * <p>仓单生效状态已生效, 仓储业务状态正常, 交易业务状态正常</p>
     *
     * @param warehouseReceiptNo 仓单号
     */
    BasisWhsReceiptVo getValidWhsReceiptVO(@Param("warehouseReceiptNo") String warehouseReceiptNo);

    //
    List<String> getValidWhsReceiptNoList(@Param("warehouseReceiptNoList") List<String> warehouseReceiptNoList);


}
