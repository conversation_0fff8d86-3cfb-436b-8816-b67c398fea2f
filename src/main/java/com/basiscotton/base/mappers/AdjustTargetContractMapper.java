package com.basiscotton.base.mappers;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.basiscotton.base.entity.BasisStockEntity;
import com.basiscotton.manager.traderecord.vo.AdjustTargetContractVo;
import org.apache.ibatis.annotations.Param;
import org.harry.dandelion.framework.rep.support.page.PipPagination;

public    interface AdjustTargetContractMapper extends BaseMapper<BasisStockEntity> {
    PipPagination<AdjustTargetContractVo> selectAdjustTargetContracts(PipPagination<AdjustTargetContractVo> pipPagination, @Param("paramVo") AdjustTargetContractVo paramVo);
    PipPagination<AdjustTargetContractVo> selectTrdBasisDeliveryTargets(PipPagination<AdjustTargetContractVo> pipPagination, @Param("paramVo") AdjustTargetContractVo paramVo);
    PipPagination<AdjustTargetContractVo> selectTrdBasisQuotes(PipPagination<AdjustTargetContractVo> pipPagination, @Param("paramVo") AdjustTargetContractVo paramVo);
    String  selectTotalTradeWeight( @Param("paramVo") AdjustTargetContractVo paramVo);

}
