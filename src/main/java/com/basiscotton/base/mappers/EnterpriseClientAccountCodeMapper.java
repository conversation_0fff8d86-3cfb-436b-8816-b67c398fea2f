package com.basiscotton.base.mappers;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.basiscotton.base.entity.EnterpriseClientAccountCodeEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 企业结算码关联表 mapper
 *
 * <AUTHOR>
 * @date 2022/08/25
 */
@Mapper
@DS("SC")
public interface EnterpriseClientAccountCodeMapper extends BaseMapper<EnterpriseClientAccountCodeEntity> {

    @Select("select ENTERPRISE_SETTLEMENT_CODE " +
            " from settlement_centre.sc_enterprise_client_account_code se " +
            " where se.CLIENT_CODE=#{traderCode}")
    String getSettleCodeByTradeCode(@Param("traderCode") String traderCode);
}
