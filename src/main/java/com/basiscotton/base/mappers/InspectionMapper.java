package com.basiscotton.base.mappers;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.basiscotton.manager.stock.vo.InspectionValueVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface InspectionMapper extends BaseMapper<InspectionValueVO> {

    //从仓储视图提取公检信息
    InspectionValueVO getInspectionValueByBatchNO(@Param("batchNo")String batchNo, @Param("traderCode")String traderCode);

}
