package com.basiscotton.base.mappers;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.basiscotton.base.entity.BasisPreStockEntity;
import com.basiscotton.manager.hall.vo.PreOrderReqVo;
import com.basiscotton.manager.hall.vo.PreOrderResVo;
import com.basiscotton.manager.hall.vo.PreSaleReqVo;
import com.basiscotton.manager.hall.vo.PreSaleResVo;
import com.basiscotton.manager.resources.manage.vo.MPreResourceReqVo;
import com.basiscotton.manager.resources.manage.vo.MPreResourceResVo;
import com.basiscotton.manager.resources.manage.vo.MPreStockReqVo;
import com.basiscotton.manager.resources.manage.vo.MPreStockResVo;
import com.basiscotton.manager.resources.trader.vo.PreResourceReqVo;
import com.basiscotton.manager.resources.trader.vo.PreResourceResVo;
import com.basiscotton.manager.resources.trader.vo.PreStockReqVo;
import com.basiscotton.manager.resources.trader.vo.PreStockResVo;
import org.apache.ibatis.annotations.Param;
import org.harry.dandelion.framework.rep.support.page.PipPagination;

import java.util.List;

/**
 * @Title: BasisPreStockMapper.java
 * @Description: 预售预购商品表mapper
 * <AUTHOR>
 * @date 2025/5/28
 * @version V1.0
 */
public interface BasisPreStockMapper extends BaseMapper<BasisPreStockEntity> {

    PipPagination<PreResourceResVo> selectResourceListByTraderCode(PipPagination<PreResourceResVo> pipPagination, @Param("paramVo") PreResourceReqVo paramVo);

    PipPagination<PreStockResVo> selectStockListByTraderCode(PipPagination<PreStockResVo> pipPagination,@Param("paramVo") PreStockReqVo paramVo);

    PipPagination<PreSaleResVo> getPreSaleStockByPage(PipPagination<PreSaleResVo> pipPagination,@Param("paramVo") PreSaleReqVo reqVo);

    PipPagination<PreOrderResVo> getPreOrderStockByPage(PipPagination<PreOrderResVo> pipPagination,@Param("paramVo") PreOrderReqVo reqVo);

    PipPagination<MPreResourceResVo> selectResourceList(PipPagination<MPreResourceResVo> pipPagination,@Param("paramVo") MPreResourceReqVo paramVo);

    PipPagination<MPreStockResVo> selectStockList(PipPagination<MPreStockResVo> pipPagination,@Param("paramVo") MPreStockReqVo paramVo);

    List<BasisPreStockEntity> getAllStock(@Param("pricingValidTime")String pricingValidTime);

    List<BasisPreStockEntity> getAllResource(@Param("pricingValidTime")String pricingValidTime);
}