package com.basiscotton.base.mappers;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.basiscotton.base.entity.ClientFrozenFundsFlowInfoEntity;
import org.apache.ibatis.annotations.Mapper;

/**
 * 客户冻结记录mapper
 *
 * <AUTHOR> kun
 * @since 2021/11/10
 */
@DS(value = "SC")
@Mapper
public interface ClientFrozenFundsFlowInfoMapper extends BaseMapper<ClientFrozenFundsFlowInfoEntity> {

}
