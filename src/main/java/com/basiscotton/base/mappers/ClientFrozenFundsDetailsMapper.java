package com.basiscotton.base.mappers;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.basiscotton.base.entity.ClientFrozenFundsDetailsEntity;
import org.apache.ibatis.annotations.Mapper;

/**
 * 客户冻结资金明细mapper
 *
 * <AUTHOR> kun
 * @since 2021/11/10
 */
@DS(value = "SC")
@Mapper
public interface ClientFrozenFundsDetailsMapper extends BaseMapper<ClientFrozenFundsDetailsEntity> {
}
