package com.basiscotton.base.mappers;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.basiscotton.base.entity.BasisOrderEntity;
import com.basiscotton.manager.order.vo.BasisOrderParamVo;
import com.basiscotton.manager.order.vo.BasisOrderResVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.harry.dandelion.framework.rep.support.page.PipPagination;

/**
 * @Title: BasisTradeOrderMapper.java
 * @Description: 基差交易订单表mapper
 * <AUTHOR>
 * @date 2025/5/24
 * @version V1.0
 */
@Mapper
public interface BasisOrderMapper extends BaseMapper<BasisOrderEntity> {

    /**
     * 基差交易订单表分页查询
     * @param pipPagination
     * @param paramVo
     * @return
     */
    PipPagination<BasisOrderResVo> selectBasisOrderByPage(PipPagination<BasisOrderResVo> pipPagination, @Param("paramVo") BasisOrderParamVo paramVo);


}
