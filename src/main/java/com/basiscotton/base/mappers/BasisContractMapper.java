package com.basiscotton.base.mappers;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.basiscotton.base.entity.BasisContractEntity;
import com.basiscotton.manager.contract.vo.BasisContractParamVo;
import com.basiscotton.manager.contract.vo.BasisContractResVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.harry.dandelion.framework.rep.support.page.PipPagination;

/**
 * @Title: BasisContractMapper.java
 * @Description: 基差合同mapper
 * <AUTHOR>
 * @date 2025/8/23
 * @version V1.0
 */
@Mapper
public interface BasisContractMapper extends BaseMapper<BasisContractEntity> {

    PipPagination<BasisContractResVo> selectBasisContractByPage(PipPagination<BasisContractResVo> pipPagination, @Param("paramVo") BasisContractParamVo paramVo);
}
