package com.basiscotton.base.mappers;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.basiscotton.base.entity.BasisPricingCommandEntity;
import com.basiscotton.deliverycore.cache.bo.PricingCommand;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: BasisPricingCommandEntityMapper.java
 * @Description: ${DESCRIPTION}
 * @date 2025/8/21 16:16
 */
public interface BasisPricingCommandMapper extends BaseMapper<BasisPricingCommandEntity> {
    /*
     * @Description: 获取预生效、已生效数据
     **/
     ArrayList<PricingCommand> getAllPricingCommandList(List<String> statusList);

}