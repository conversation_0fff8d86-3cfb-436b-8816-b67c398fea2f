package com.basiscotton.base.mappers;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.basiscotton.base.entity.BasisNoticeEntity;
import com.basiscotton.manager.notice.vo.BasisNoticeVo;
import io.lettuce.core.dynamic.annotation.Param;
import org.harry.dandelion.framework.rep.support.page.PipPagination;

/**
 * @Title: BasisFutureMapper.java
 * @Description: 期货合约表mapper
 * <AUTHOR>
 * @date 2025/5/23
 * @version V1.0
 */
public interface BasisNoticeMapper extends BaseMapper<BasisNoticeEntity> {


    /**
     * 交易板块列表
     * @param pipPagination
     * @param queryParam
     * @return
     */
    PipPagination<BasisNoticeEntity> queryNoticeByPage(PipPagination<BasisNoticeEntity> pipPagination, @Param("queryParam") BasisNoticeVo queryParam);

}