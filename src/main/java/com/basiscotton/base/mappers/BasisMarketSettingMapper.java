package com.basiscotton.base.mappers;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.basiscotton.base.entity.MarketSettingEntity;
import com.basiscotton.common.tradetime.vo.MonitorHolidayVo;
import com.basiscotton.riskControl.vo.PriceIndexVo;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: BasisMarketSettingMapper.java
 * @Description: ${DESCRIPTION}
 * @date 2025/8/22 08:40
 */
public interface BasisMarketSettingMapper extends BaseMapper<MarketSettingEntity> {

    /**
     * 查询监控系统节假日
     */
    List<MonitorHolidayVo> selectMonitorHoliday();

    /**
     * 获取价格指数
     */
    PriceIndexVo selectPriceIndex();
}