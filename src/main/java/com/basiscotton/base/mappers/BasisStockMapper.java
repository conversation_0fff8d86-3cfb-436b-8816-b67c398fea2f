package com.basiscotton.base.mappers;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.basiscotton.base.entity.BasisStockEntity;
import com.basiscotton.manager.hall.service.SpotStockSqlBuilder;
import com.basiscotton.manager.hall.vo.*;
import com.basiscotton.manager.resources.manage.vo.ManageStockReqVo;
import com.basiscotton.manager.resources.manage.vo.ManageStockResVo;
import com.basiscotton.manager.stock.vo.TraderAllSpotStockResVo;
import com.basiscotton.manager.stock.vo.TraderAllStockReqVo;
import com.basiscotton.manager.stock.vo.TraderAllStockResVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectProvider;
import org.harry.dandelion.framework.rep.support.page.PipPagination;

import java.util.List;

/**
 * @Title: BasisSpotStockMapper.java
 * @Description: 现货商品表mapper
 * <AUTHOR>
 * @date 2025/5/23
 * @version V1.0
 */
@Mapper
public interface BasisStockMapper extends BaseMapper<BasisStockEntity> {

    PipPagination<BuyerStockResVo> getAllStockByPage(PipPagination<BuyerStockResVo> pipPagination, @Param("search") BuyerStockReqVo reqVo);

    @SelectProvider(type = SpotStockSqlBuilder.class, method = "buildAllStockQuery")
    PipPagination<BuyerStockResVo> getAllSpotStockByPage(PipPagination<BuyerStockResVo> pipPagination, SpotStockReqVO reqVo);

    PipPagination<BuyerStockResVo> getAllStockBuyerByPage(PipPagination<BuyerStockResVo> pipPagination, @Param("search") BuyerStockReqVo reqVo);

    PipPagination<TraderAllStockResVo> getTraderAllStockByPage(PipPagination<TraderAllStockResVo> pipPagination,@Param("search") TraderAllStockReqVo reqVo);

    PipPagination<ManageStockResVo> getManageStockByPage(PipPagination<ManageStockResVo> pipPagination,@Param("search") ManageStockReqVo reqVo);

    List<BasisStockEntity> getAllStock(@Param("pricingValidTime")String pricingValidTime);

    List<BasisStockEntity> getAllResource(@Param("pricingValidTime")String pricingValidTime);

    List<StockCountVo> getDataCountListByWarehouseName(@Param("search") BuyerStockReqVo reqVo);

    List<StockCountVo> getDataCountListByTraderName(@Param("search") BuyerStockReqVo reqVo);

    PipPagination<TraderAllSpotStockResVo> getTraderAllSpotStockByPage(PipPagination<TraderAllSpotStockResVo> pipPagination, @Param("search") TraderAllStockReqVo reqVo);

    NowStockStatisticsVo getNowStockStatistics(@Param("nowDate") String nowDate);

    List<NowTradeStockTitleVo> getNowTradeStockTiTle(@Param("nowDate") String nowDate);

    List<BuyerStockResVo> getAllStockList(@Param("search") BuyerStockReqVo reqVo);

}
