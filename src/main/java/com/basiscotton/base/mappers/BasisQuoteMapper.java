package com.basiscotton.base.mappers;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.basiscotton.base.entity.BasisQuoteEntity;
import com.basiscotton.manager.quote.vo.BuyerQuoteReqVo;
import com.basiscotton.manager.quote.vo.BuyerQuoteResVo;
import com.basiscotton.manager.quote.vo.SellerQuoteReqVo;
import com.basiscotton.manager.quote.vo.SellerQuoteResVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.harry.dandelion.framework.rep.support.page.PipPagination;

/**
 * @Title: BasisQuoteMapper.java
 * @Description: 基差交易委托表mapper
 * <AUTHOR>
 * @date 2025/5/24
 * @version V1.0
 */
@Mapper
public interface BasisQuoteMapper extends BaseMapper<BasisQuoteEntity> {

    PipPagination<BuyerQuoteResVo> getBuyerQuoteByPage(PipPagination<BuyerQuoteResVo> pipPagination, @Param("search") BuyerQuoteReqVo reqVo);

    PipPagination<SellerQuoteResVo> getSellerQuoteByPage(PipPagination<SellerQuoteResVo> pipPagination, @Param("search") SellerQuoteReqVo reqVo);

    PipPagination<BuyerQuoteResVo> getBuyerQuotePageByStockId(PipPagination<BuyerQuoteResVo> pipPagination, @Param("stockId") String stockId);
}
