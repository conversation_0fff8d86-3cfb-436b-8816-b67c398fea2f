package com.basiscotton.base.mappers;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.basiscotton.base.entity.SpotFundsEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: SettlementClientFundsMapper.java
 * @Description: ${DESCRIPTION}
 * @date 2025/9/1 14:17
 */
public interface SpotFundsMapper extends BaseMapper<SpotFundsEntity> {

     List<SpotFundsEntity> getSpotFundsByTraderCode(@Param("traderCode") String traderCode);
}