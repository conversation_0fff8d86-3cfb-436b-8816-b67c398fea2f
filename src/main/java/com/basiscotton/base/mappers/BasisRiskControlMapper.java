package com.basiscotton.base.mappers;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.basiscotton.base.entity.BasisRiskControlEntity;
import com.basiscotton.riskControl.vo.RiskControl;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;

/**
 * @Title: BasisRiskControlMapper.java
 * @Description: 风险控制表mapper
 * <AUTHOR>
 * @date 2025/5/23
 * @version V1.0
 */
public interface BasisRiskControlMapper extends BaseMapper<BasisRiskControlEntity> {

    ArrayList<RiskControl> getAllRiskControlList(@Param("fundsType")String fundsType, @Param("rcStatusList")List<String> rcStatusList);

}