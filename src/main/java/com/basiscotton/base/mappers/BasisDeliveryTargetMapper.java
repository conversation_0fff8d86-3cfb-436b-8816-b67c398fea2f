package com.basiscotton.base.mappers;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.basiscotton.base.entity.BasisDeliveryTargetEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * @Title: BasisContractTargetMapper.java
 * @Description: 基差标记mapper
 * <AUTHOR>
 * @date 2025/5/23
 * @version V1.0
 */
@Mapper
public interface BasisDeliveryTargetMapper extends BaseMapper<BasisDeliveryTargetEntity> {

    @Update("update trd_basis_delivery_target set buyer_trade_fee_tx_code = #{txCode} where id = #{id}")
    void updateBuyerTxCode(@Param("id") Long id, @Param("txCode") Long txCode);

    @Update("update trd_basis_delivery_target set buyer_trade_fee_status = #{status} where id = #{id}")
    void updateBuyerTradeFeeStaus(@Param("id") Long id, @Param("status") String status);

    @Update("update trd_basis_delivery_target set seller_trade_fee_tx_code = #{txCode} where id = #{id}")
    void updateSellerTxCode(@Param("id") Long id, @Param("txCode") Long txCode);

    @Update("update trd_basis_delivery_target set seller_trade_fee_status = #{status} where id = #{id}")
    void updateSellerTradeFeeStaus(@Param("id") Long id, @Param("status") String status);
}
