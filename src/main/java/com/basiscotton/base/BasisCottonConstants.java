package com.basiscotton.base;

import java.math.BigDecimal;

/**
 * @Title: BasisCottonContants.java
 * @Description: 系统常量类
 * <AUTHOR>
 * @date 2025/5/20
 * @version V1.0
 */
public class BasisCottonConstants {

    //region 公共常量
    /**
     * 通用的否 -1
     */
    public static final String COMMON_NO = "-1";

    /**
     * 通用的是 1
     */
    public static final String COMMON_YES = "1";
    //endregion

    //region业务常量
    //电子商务公司默认客户编码
    public static final String DS_CUSTOM_CODE = "000001";
    //交易市场客户编码
    public static final String DS_MARKET_CODE = "000008";
    //配送客户编码
    public static final String PEISONG_CODE = "188000";
    //国贸客户编码
    public static final String GUOMAO_CODE = "878018";

    // region 用户中心
    /** 客户类型代码: 1、交易商 */
    public final static String custom_type_1 = "1";
    /** 客户类型代码: 2、仓库 */
    public final static String custom_type_2 = "2";
    /** 客户类型代码: 3、物流公司 */
    public final static String custom_type_3 = "3";
    /** 客户类型代码: 4、加工单位 */
    public final static String custom_type_4 = "4";
    /** 客户类型代码: 5、办事处 */
    public final static String custom_type_5 = "5";
    /** 客户类型代码: 6、监管机构 */
    public final static String custom_type_6 = "6";
    /** 客户类型代码: 7、保险公司 */
    public final static String custom_type_7 = "7";
    /** 客户类型代码: 8、纤检机构 */
    public final static String custom_type_8 = "8";
    /** 客户类型代码: 9、市场 */
    public final static String custom_type_9 = "9";
    /** 客户类型代码: 10、市场交易商 */
    public final static String custom_type_10 = "10";
    /** 客户类型代码: 11、普通企业 */
    public final static String custom_type_11 = "11";
    /** 客户类型代码: 12、其他 */
    public final static String custom_type_12 = "12";
    //endregion

    //region 基差交易
    //买方基差保证金收取方式: 1-集团账户 2-电商
    public static final String buyer_basis_margin_type_1 = "1";
    public static final String buyer_basis_margin_type_2 = "2";

    //买方交易手续费收取方式: 1-集团账户 2-电商
    public static final String buyer_trade_fee_type_1 = "1";
    public static final String buyer_trade_fee_type_2 = "2";

    //买方交割手续费收取方式: 1-集团账户 2-电商
    public static final String buyer_delivery_fee_type_1 = "1";
    public static final String buyer_delivery_fee_type_2 = "2";

    //报价方式: 1-锁基差
    public static final String quote_type_1 = "1";


    // region 交易状态
    /**
     * 1-未上架
     */
    public static final String trade_status_1 = "1";
    /**
     * 2-未成交
     */
    public static final String trade_status_2 = "2";
    /**
     * 3-已成交
     */
    public static final String trade_status_3 = "3";
    /**
     * 4-已下架
     */
    public static final String trade_status_4 = "4";
    // endregion

    // region 报价状态
    /**
     * 未成交
     */
    public static final String quote_status_1 = "1";
    /**
     * 已成交
     */
    public static final String quote_status_2 = "2";
    /**
     * 已撤销
     */
    public static final String quote_status_3 = "3";
    // endregion

    // region 撤销类型
    /**
     * 买方自主撤销
     */
    public static final String QUOTE_CANCEL_TYPE_1 = "1";
    /**
     * 其它买方成交
     */
    public static final String QUOTE_CANCEL_TYPE_2 = "2";
    /**
     * 卖方自主撤销
     */
    public static final String QUOTE_CANCEL_TYPE_3 = "3";
    /**
     * 系统撤销
     */
    public static final String QUOTE_CANCEL_TYPE_4 = "4";
    // endregion

    // region 资源审核状态
    /**
     * 资源审核状态: 1-未审核
     */
    public static final String resource_audit_status_1 = "1";
    /**
     * 资源审核状态: 2-已审核
     */
    public static final String resource_audit_status_2 = "2";
    /**
     * 资源审核状态:  3-已取消
     */
    public static final String resource_audit_status_3 = "3";
    /**
     * 资源审核状态:  4-未提交
     */
    public static final String resource_audit_status_4 = "4";

    // region 资源取消方式
    /**
     * 卖方自主取消
     */
    public static final String resource_cancel_type_1 = "1";
    /**
     * 系统取消(过户、移库、注销、金融、交易)
     */
    public static final String resource_cancel_type_2 = "2";
    // endregion

    // region 商品取消方式
    /**
     * 商品取消方式: 1-卖方自主撤销
     */
    public static final String DELISTING_REASON_1 = "1";
    /**
     * 其它业务撤销
     */
    public static final String DELISTING_REASON_2 = "2";
    /**
     * 到期撤销
     */
    public static final String DELISTING_REASON_3 = "3";
    // endregion

    // region 卖方手续费收取方式
    /**
     * 卖方手续费收取环节 1 成交时
     */
    public static final String SELLER_TRADE_FEE_COLLECT_PHASE_1 = "1";
    /**
     * 卖方手续费收取环节 2 随货款
     */
    public static final String SELLER_TRADE_FEE_COLLECT_PHASE_2 = "2";
    // endregion

    // region 手续费处理状态
    /**
     * 手续费处理状态 1 未处理
     */
    public static final String TRADE_FEE_STATUS_1 = "1";
    /**
     * 手续费处理状态 2 已处理
     */
    public static final String TRADE_FEE_STATUS_2 = "1";
    /**
     * 手续费处理状态 3 无需处理
     */
    public static final String TRADE_FEE_STATUS_3 = "3";
    /**
     * 手续费处理状态 4 结算失败
     */
    public static final String TRADE_FEE_STATUS_4 = "4";
    // endregion


    // region 仓单交易状态
    /**
     * 仓单交易状态 2 正常
     */
    public static final Integer TRANSACTION_STATUS_2 = 2;

    /**
     * 仓单交易状态 4 基差交易
     */
    public static final Integer TRANSACTION_STATUS_4 = 4;
    // endregion

    // 市场设置设置类型：1、买方点价2、预售预购 3现货挂牌
    public static final String basis_setting_type_1 = "1";
    public static final String basis_setting_type_2 = "2";
    public static final String basis_setting_type_3 = "3";

    // 市场设置是否生效 setting_status 1启用 -1未启用
    public static final String setting_status_1 = "1";
    public static final String setting_status_negative_1 = "1";

    // 现货系统状态: 1-正常 2-暂停 3-非交易日 4-非交易时间
    public static final String spot_system_status_1 = "1";
    public static final String spot_system_status_2 = "2";
    public static final String spot_system_status_3 = "3";
    public static final String spot_system_status_4 = "4";


    //resource_trade_type 资源交易方式: 1-单批
    public static final String resource_trade_type_1 = "1";
    public static final String resource_trade_type_2 = "2";

    //resource_display_type 资源展示方式: 1-公开 2-指定
    public static final String resource_display_type_1 = "1";
    public static final String resource_display_type_2 = "2";

    //stock_source 商品来源: 1-仓单 2-非市场仓单
    public static final String stock_source_1 = "1";
    public static final String stock_source_2 = "2";

    //卖方基差保证金收取方式: 1-集团账户 2-电商
    public static final String seller_basis_margin_type_1 = "1";
    public static final String seller_basis_margin_type_2 = "2";

    //卖方交易手续费收取方式: 1-集团账户 2-电商
    public static final String basis_seller_free_type_1 = "1";
    public static final String basis_seller_free_type_2 = "2";

    //卖方交割手续费收取方式: 1-集团账户 2-电商
    public static final String seller_delivery_fee_type_1 = "1";
    public static final String seller_delivery_fee_type_2 = "2";

    //点价方: 1-买方 2-卖方
    public static final String pricing_party_1 = "1";
    public static final String pricing_party_2 = "2";

    //是否为主力期货合约 0否 1是
    public static final Integer activeFuture_0 = 0;
    public static final Integer activeFuture_1 = 1;

    //买方货权确认状态1未确认2已确认
    public static final Integer buyer_delivery_confirm_status_1 = 1;
    public static final Integer buyer_delivery_confirm_status_2 = 2;
    //endregion
    //endregion

    /** -----------------点价指令----------------- **/
    //点价类型 1买方点价 2卖方点价
    public static String pricing_type_1;

    //指令状态 command_status 1预生效、   2已生效、   3自主撤销、  4系统撤销-到期结算--有指令时  5系统撤销-风控触线-有指令时   6点价击穿-自动
    public static final String command_status_1 = "1";
    public static final String command_status_2 = "2";
    public static final String command_status_3 = "3";
    public static final String command_status_4 = "4";
    public static final String command_status_5 = "5";
    public static final String command_status_6 = "6";

    //指令生效方式  command_effect_type  1卖方确认  2系统确认-到时
    public static final String command_effect_type_1 = "1";
    public static final String command_effect_type_2 = "2";

    /** -----------------交易时间窗口----------------- **/

    //WORK_DAY 期类型：1:工作日 2：非工作日
    public static final String WORK_DAY = "1";
    public static final String NON_WORK_DAY = "2";

    //1交易正常 2非交易日 3非交易时间
    public static final String TRADE_NORMAL = "1";
    public static final String NOT_TRADE_DAY = "2";
    public static final String NOT_TRADE_TIME = "3";



    /** -----------------交割标的----------------- **/
    //点价状态：1未点价2点价中3已点价
    public static final String pricing_status_1 = "1";
    public static final String pricing_status_2 = "2";
    public static final String pricing_status_3 = "3";

    //交割方式：1先点价后交割2先交割后点价
    public static final Integer DELIVERY_TYPE_1 = 1;
    public static final Integer DELIVERY_TYPE_2 = 2;

    //批次交割状态：1未收买方2已收买方3已过户4已支付5已违约6已完结
    public static String delivery_status_1 = "1";
    public static String delivery_status_2 = "2";
    public static String delivery_status_3 = "3";
    public static String delivery_status_4 = "4";
    public static String delivery_status_5 = "5";
    public static String delivery_status_6 = "6";

    //风险类型：1正常2基差风险3点价风险
    public static String target_risk_control_type_1 = "1";
    public static String target_risk_control_type_2 = "2";
    public static String target_risk_control_type_3 = "3";

    //基差风险状态：1正常2卖方风险3买方风险
    public static String basis_risk_control_status_1 = "1";
    public static String basis_risk_control_status_2 = "2";
    public static String basis_risk_control_status_3 = "3";

    //点价风险状态：1正常2卖方追保3卖方强平4买方追保5买方强平
    public static String pricing_risk_control_status_1 = "1";
    public static String pricing_risk_control_status_2 = "2";
    public static String pricing_risk_control_status_3 = "3";
    public static String pricing_risk_control_status_4 = "4";
    public static String pricing_risk_control_status_5 = "5";

    /** -----------------风险控制----------------- **/
    //保证金类型：1基差保证金2交易保证金
    public static String funds_type_1 = "1";
    public static String funds_type_2 = "2";
    //追保强平状态1追保中3已追保4已强平
    public static String risk_control_status_1 = "1";
    public static String risk_control_status_2 = "2";
    public static String risk_control_status_3 = "3";


    //交割标的点价形成或来源  1:点价击穿-自动2:手动确认-卖方3:系统成交-到期结算4:系统成交-风控触线5：手动确认-卖方
    public static final String pricing_From_1 = "1";
    public static final String pricing_From_2 = "2";
    public static final String pricing_From_3 = "3";
    public static final String pricing_From_4 = "4";
    public static final String pricing_From_5 = "5";



    //市场设置 是否自动审核备注 1-否，2-是
    public static final Integer audit_status_1 = 1;
    public static final Integer audit_status_2 = 2;

    /** -----------------抵免业务关系表----------------- **/
    //抵免类型：1报价2追保
    public static String credit_type_1 = "1";
    public static String credit_type_2 = "2";

    //抵免状态：1已抵免2已取消
    public static String business_credit_status_1 = "1";
    public static String business_credit_status_2 = "2";

    // region 合同状态
    /**
     * 合同状态-未完结
     */
    public static final Integer CONTRACT_STATUS_1 = 1;
    /**
     * 合同状态-已违约
     */
    public static final Integer CONTRACT_STATUS_2 = 1;
    /**
     * 合同状态-已取消
     */
    public static final Integer CONTRACT_STATUS_3 = 1;
    /**
     * 合同状态-已完结
     */
    public static final Integer CONTRACT_STATUS_4 = 1;
    // endregion


    /** -----------------未使用功能使用项----------------- **/
    //商品来源1预售2预购
    public static final String pre_stock_source_1 = "1";
    public static final String pre_stock_source_2 = "2";

    //预售预购计算批次，每批按43吨计算
    public static final BigDecimal batch_weught = new BigDecimal("43");

    // 消息通知类型
    public static final Integer noticeType_1 = 1;
    public static final Integer noticeType_2 = 2;

    //发布状态：1、待发布2、已发布
    public static final String publish_status_1 = "1";
    public static final String publish_status_2 = "2";

    //审核状态状态:1通过2驳回
    public static final String AUDIT_STATUS_1 = "1";
    public static final String AUDIT_STATUS_2 = "2";

    // 消息通知状态0未删除1已删除
    public static final Integer notice_nodel_flag = 0;
    public static final Integer notice_del_flag = 1;

    public static final String quote_type_2 = "2";
    public static final String quote_type_3 = "3";

    //交易模式 1 现货挂牌 2 基差点价
    public static final String trade_mode_1 = "1";
    public static final String trade_mode_2 = "2";


    public static final String HOLIDAY_CACHE_KEY = "monitor_holidays";

    //运费补贴申领方 1买方 2卖方 3其它企业
    public static final String transport_subsidy_apply_party_1 = "1";
    public static final String transport_subsidy_apply_party_2 = "2";
    public static final String transport_subsidy_apply_party_3 = "3";
}
