<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.basiscotton.base.mappers.AdjustTargetContractMapper">

    <select id="selectAdjustTargetContracts"   resultType="com.basiscotton.manager.traderecord.vo.AdjustTargetContractVo">
        SELECT
        <if test='paramVo.customCode !=null and paramVo.customCode != "" and   paramVo.customCode == "900001"'>
        CASE  c.CONTRACT_BUSINESS_SOURCE
        WHEN 1 THEN '协商'
        WHEN 2 THEN '竞卖'
        WHEN 3 THEN '基差'
        when 5 then '现货挂牌'
        END AS transSeqType,
        c.BUYER_TRADER_CODE buyerTraderCode,
        c.BUYER_TRADER_NAME buyerTraderName,
        c.SELLER_TRADER_CODE sellerTraderCode,
        c.SELLER_TRADER_NAME sellerTraderName,
       </if>
        c.DEAL_TIME tradeTime,
        t.BATCH_NO   batchNo
        FROM
        tradecenter_stockcotton.trd_contract c
        JOIN tradecenter_stockcotton.trd_contract_target t ON c.id = t.contract_id

        <where>
            c.CONTRACT_BUSINESS_SOURCE  != 4 and  c.TRD_CONTRACT_EXECUTE_STATUS = 2

        <if test='paramVo.batchNumberList !=null and paramVo.batchNumberList != "" '>
             and
            <foreach collection="paramVo.batchNumberList" item="item" open="(" separator=" OR " close=")">

                t.BATCH_NO   = #{item}
            </foreach>
        </if>
            <if test="paramVo.transSeqType !=null and paramVo.transSeqType !='' ">

               and  c.CONTRACT_BUSINESS_SOURCE =  #{paramVo.transSeqType}
            </if>

            <if test="paramVo.sellerTraderCode !=null and paramVo.sellerTraderCode !='' ">
                and   c.SELLER_TRADER_CODE =  #{paramVo.sellerTraderCode}
            </if>
            <if test="paramVo.buyerTraderCode !=null and paramVo.buyerTraderCode !='' ">
                and  c.BUYER_TRADER_CODE  =  #{paramVo.buyerTraderCode}
            </if>

            <if test="paramVo.beginTradeTime !=null and paramVo.beginTradeTime !='' ">
                and   DATE_FORMAT(c.DEAL_TIME , '%Y-%m-%d')  >= #{paramVo.beginTradeTime}
            </if>
            <if test="paramVo.endTradeTime !=null and paramVo.endTradeTime!='' ">
                and  DATE_FORMAT(c.DEAL_TIME , '%Y-%m-%d')  &lt;= #{paramVo.endTradeTime}
            </if>
        </where>
    </select>
    <select id="selectTrdBasisDeliveryTargets"   resultType="com.basiscotton.manager.traderecord.vo.AdjustTargetContractVo">
        SELECT
        a.batch_no batchNo,
        a.seller_trader_code sellerTraderCode,
        a.seller_trader_name sellerTraderName,
        a.buyer_trader_code buyerTraderCode,
        a.buyer_trader_name buyerTraderName,
        a.trade_date tradeTime,
        a.stock_code stockCode ,
        b.CONTRACT_NO contractNo,
        a.trade_weight tradeWeight,
        a.trade_basis_price tradeBasisPrice
        FROM
        tradecenter_basiscotton.trd_basis_delivery_target a
        LEFT JOIN tradecenter_stockcotton.trd_contract b ON a.contract_id = b.id
        <where>

            <if test='paramVo.batchNumberList !=null and paramVo.batchNumberList != "" '>
                and
                <foreach collection="paramVo.batchNumberList" item="item" open="(" separator=" OR " close=")">
                    a.batch_no   = #{item}
                </foreach>
            </if>


            <if test="paramVo.sellerTraderCode !=null and paramVo.sellerTraderCode !='' ">
                and  a.seller_trader_code =  #{paramVo.sellerTraderCode}
            </if>
            <if test="paramVo.buyerTraderCode !=null and paramVo.buyerTraderCode !='' ">
                and a.buyer_trader_code  =  #{paramVo.buyerTraderCode}
            </if>

            <if test="paramVo.beginTradeTime !=null and paramVo.beginTradeTime !='' ">
                and DATE_FORMAT(a.trade_date, '%Y-%m-%d') >= #{paramVo.beginTradeTime}
            </if>
            <if test="paramVo.endTradeTime !=null and paramVo.endTradeTime!='' ">
                and DATE_FORMAT(a.trade_date, '%Y-%m-%d') &lt;= #{paramVo.endTradeTime}
            </if>
        </where>
    </select>


    <select id="selectTotalTradeWeight"   resultType="java.lang.String">
        SELECT

        sum(a.trade_weight) totalTradeWeight
        FROM
        tradecenter_basiscotton.trd_basis_delivery_target a
        LEFT JOIN tradecenter_stockcotton.trd_contract b ON a.contract_id = b.id
        <where>

            <if test='paramVo.batchNumberList !=null and paramVo.batchNumberList != "" '>
                and
                <foreach collection="paramVo.batchNumberList" item="item" open="(" separator=" OR " close=")">
                    a.batch_no   = #{item}
                </foreach>
            </if>


            <if test="paramVo.sellerTraderCode !=null and paramVo.sellerTraderCode !='' ">
                and  a.seller_trader_code =  #{paramVo.sellerTraderCode}
            </if>
            <if test="paramVo.buyerTraderCode !=null and paramVo.buyerTraderCode !='' ">
                and a.buyer_trader_code  =  #{paramVo.buyerTraderCode}
            </if>

            <if test="paramVo.beginTradeTime !=null and paramVo.beginTradeTime !='' ">
                and  DATE_FORMAT(a.trade_date, '%Y-%m-%d') >= #{paramVo.beginTradeTime}
            </if>
            <if test="paramVo.endTradeTime !=null and paramVo.endTradeTime!='' ">
                and  DATE_FORMAT(a.trade_date, '%Y-%m-%d') &lt;= #{paramVo.endTradeTime}
            </if>
        </where>
    </select>


    <select id="selectTrdBasisQuotes"   resultType="com.basiscotton.manager.traderecord.vo.AdjustTargetContractVo">
        select
        b.batch_no batchNo,
        b.trader_code sellerTraderCode,
        b.trader_name sellerTraderName,
        a.trader_code buyerTraderCode,
        a.trader_name buyerTraderName,
        a.stock_code stockCode,
        a.quote_time quoteTime,
        a.quote_weight quoteWeight,
        a.quote_price quotePrice,
        a.quote_status quoteStatus

        from tradecenter_basiscotton.trd_basis_quote a left join  tradecenter_basiscotton.trd_basis_stock b on a.stock_id = b.id
        <where>
            1 = 1
            <if test='paramVo.batchNumberList !=null and paramVo.batchNumberList != "" '>
                and
                <foreach collection="paramVo.batchNumberList" item="item" open="(" separator=" OR " close=")">
                   b.batch_no   = #{item}
                </foreach>
            </if>

            <if test="paramVo.quoteStatus !=null and paramVo.quoteStatus !='' ">
                and  a.quote_status =  #{paramVo.quoteStatus}
            </if>
            <if test="paramVo.sellerTraderCode !=null and paramVo.sellerTraderCode !='' ">
                and  b.trader_code =  #{paramVo.sellerTraderCode}
            </if>
            <if test="paramVo.buyerTraderCode !=null and paramVo.buyerTraderCode !='' ">
                and a.trader_code  =  #{paramVo.buyerTraderCode}
            </if>

            <if test="paramVo.beginTradeTime !=null and paramVo.beginTradeTime !='' ">
                and   DATE_FORMAT(a.quote_time, '%Y-%m-%d')  >= #{paramVo.beginTradeTime}
            </if>
            <if test="paramVo.endTradeTime !=null and paramVo.endTradeTime!='' ">
                and  DATE_FORMAT(a.quote_time, '%Y-%m-%d')  &lt;= #{paramVo.endTradeTime}
            </if>
        </where>
    </select>
</mapper>