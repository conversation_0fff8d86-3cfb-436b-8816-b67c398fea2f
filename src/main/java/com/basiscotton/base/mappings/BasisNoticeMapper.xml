<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.basiscotton.base.mappers.BasisNoticeMapper">

    <!-- 公告列表查询 -->
    <select id="queryNoticeByPage" resultType="com.basiscotton.base.entity.BasisNoticeEntity">
        SELECT id,
        notice_type,
        title,
        content,
        publish_status,
        create_time,
        create_user_id,
        create_user_name,
        update_time,
        update_user_id,
        update_user_name
        FROM trd_basis_notice
        <where>
            DEL_FLAG = 0
            <if test="queryParam.noticeType != null and queryParam.noticeType != ''">
                AND NOTICE_TYPE = #{queryParam.noticeType}
            </if>
            <if test="queryParam.title != null and queryParam.title != ''">
                <bind name="title" value="'%'+ queryParam.title +'%'"/>
                AND TITLE like #{title}
            </if>
        </where>
        ORDER BY CREATE_TIME DESC
    </select>

</mapper>