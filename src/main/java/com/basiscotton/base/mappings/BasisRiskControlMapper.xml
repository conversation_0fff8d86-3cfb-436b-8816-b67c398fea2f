<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.basiscotton.base.mappers.BasisRiskControlMapper">


    <select id="getAllRiskControlList" resultType="com.basiscotton.riskControl.vo.RiskControl">
        SELECT
            id AS riskControlId,
            id AS targetId,
            future_code,
            batch_no,
            delivery_type,
            margin_call_standard,
            liquidation_price,
            risk_control_status
        FROM trd_basis_risk_control
        <where>
            funds_type = #{fundsType}
            AND risk_control_status IN
            <foreach item="rcStatus" collection="rcStatusList" separator="," open="(" close=")">
                #{rcStatus}
            </foreach>
        </where>
        ORDER BY create_time DESC
    </select>
</mapper>