<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.basiscotton.base.mappers.InspectionMapper">

    <select id="getInspectionValueByBatchNO"
            resultType="com.basiscotton.manager.stock.vo.InspectionValueVO">
        SELECT
            vwarehouse.BATCH_NUMBER AS batchNo,
            vwarehouse.CONDITIONED_WEIGHT AS conditionedWeight,
            vwarehouse.COLOR_GRADE_CODE AS colorGrade,
            -- 马值 长度/强力/长整/含杂/回潮
            inspect.AVG_MKL AS avgMkl,
            inspect.AVG_LENGTH AS avgLength,
            inspect.AVG_BREAK_RATE AS breakValue,
            inspect.AVG_LEN_UNIFORMITY AS uniformityAverageValue,
            vwarehouse.DIRTY AS impurityRate,
            vwarehouse.MOISTURE_REGAIN AS moistureRate,
            vwarehouse.PRODUCT_YEAR AS productYear
        FROM
            v_xs_warehouse_info vwarehouse
        LEFT JOIN v_factory_batch_inspect inspect ON vwarehouse.BATCH_NUMBER = inspect.BATCH_CODE
        <where>
            vwarehouse.QUANLITY_STATUS = 1
            <!--批号-->
            <if test='batchNo !=null and batchNo != "" '>
                AND vwarehouse.BATCH_NUMBER = #{batchNo}
            </if>
        </where>
        LIMIT 1
    </select>
</mapper>
