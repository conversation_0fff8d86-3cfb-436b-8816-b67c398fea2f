<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.basiscotton.base.mappers.BasisQuoteMapper">
    <select id="getBuyerQuoteByPage"
            parameterType="com.basiscotton.manager.quote.vo.BuyerQuoteReqVo"
            resultType="com.basiscotton.manager.quote.vo.BuyerQuoteResVo">
        select t1.id,
               t2.id as stock_id,
               t2.trade_status,
               t1.stock_code,
               t2.stock_weight,
               t2.stock_price,
               t1.quote_price,
               t1.buyer_basis_margin_amount,
               t1.buyer_trade_fee_amount + t1.buyer_delivery_fee_amount as buyer_fee_amount
        from trd_basis_quote t1,
             trd_basis_stock t2
        where t1.stock_id = t2.id
          and t1.trader_code = #{search.traderCode}
          and t2.trade_status = 2
        <if test="search.stockCode != null and search.stockCode != ''">
            <bind name="stockCode" value="'%'+ search.stockCode +'%'"/>
            and t2.stock_code like #{stockCode}
        </if>
        ORDER BY t2.create_time DESC
    </select>

    <select id="getSellerQuoteByPage"
            parameterType="com.basiscotton.manager.quote.vo.SellerQuoteReqVo"
            resultType="com.basiscotton.manager.quote.vo.SellerQuoteResVo">
        SELECT
        quote.id,
        stock.id AS stockId,
        stock.stock_code,
        stock.batch_no,
        stock.trade_status,
        stock.stock_weight,
        stock.stock_price,
        quote.quote_price,
        stock.quote_type,
        stock.future_code,
        ROUND(stock.seller_basis_margin_standard * stock.stock_weight,2) AS seller_margin_amount,
        ROUND((stock.seller_trade_fee_standard+stock.seller_delivery_fee_standard) * stock.stock_weight,2) AS
        seller_fee_amount,
        quote.create_time,
        stock.transport_subsidy_apply_party
        FROM
        trd_basis_quote quote
        JOIN trd_basis_stock stock ON quote.stock_id = stock.id
        JOIN trd_basis_stock_base_info baseinfo ON stock.stock_base_info_id = baseinfo.id
        <where>
            stock.trade_status = 2
            and quote.id = ( SELECT id FROM trd_basis_quote where stock_id = quote.stock_id and quote.quote_status = 1 ORDER BY quote_price DESC, create_time LIMIT 1)
            <if test="search.traderCode !=null and search.traderCode !='' ">
                and stock.trader_code = #{search.traderCode}
            </if>
            <!-- 商品码 -->
            <if test="search.stockCode !=null and search.stockCode !='' ">
                <bind name="stockCode" value="'%'+ search.stockCode +'%'"/>
                and stock.stock_code like #{stockCode}
            </if>
            <!-- 批号 -->
            <if test="search.batchNo !=null and search.batchNo !='' ">
                <bind name="batchNo" value="'%'+ search.batchNo +'%'"/>
                and stock.batch_no like #{batchNo}
            </if>
        </where>
        ORDER BY quote.create_time DESC
    </select>

    <select id="getBuyerQuotePageByStockId" resultType="com.basiscotton.manager.quote.vo.BuyerQuoteResVo">
        SELECT
            quote.id,
            stock.id AS stockId,
            stock.stock_code,
            stock.stock_weight,
            quote.quote_price,
            quote.quote_time as create_time
        FROM
            trd_basis_quote quote
            JOIN trd_basis_stock stock ON quote.stock_id = stock.id
            JOIN trd_basis_stock_base_info baseinfo ON stock.stock_base_info_id = baseinfo.id
        <where>
            <if test="stockId !=null and stockId !='' ">
                quote.stock_id = #{stockId}
            </if>
        </where>
        ORDER BY quote.quote_price DESC, quote.create_time
    </select>

</mapper>
