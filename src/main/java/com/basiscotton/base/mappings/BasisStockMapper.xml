<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.basiscotton.base.mappers.BasisStockMapper">

    <select id="getAllStockByPage"
            parameterType="com.basiscotton.manager.hall.vo.BuyerStockReqVo"
            resultType="com.basiscotton.manager.hall.vo.BuyerStockResVo">
        SELECT
            stock.id,
            stock.stock_code,
            baseinfo.warehouse_receipt_no,
            baseinfo.batch_no,
            baseinfo.COLOR_GRADE,
            baseinfo.AVG_LENGTH,
            baseinfo.break_value,
            baseinfo.avg_break_rate BRUTEFORCE,
            baseinfo.uniformity_average_value,
            baseinfo.impurity_rate,
            baseinfo.moisture_rate,
            baseinfo.AVG_MKL,
            baseinfo.conditioned_weight,
            baseinfo.intact_place,
            baseinfo.storage_whs_name,
            baseinfo.product_year,
            stock.quote_type,
            stock.future_code,
            stock.stock_price,
            stock.stock_source,
            stock.negotiable,
            stock.transport_subsidy_apply_party,
            stock.stock_weight,
            stock.remark
        FROM
            trd_basis_stock stock
            LEFT JOIN trd_basis_stock_base_info baseinfo ON stock.stock_base_info_id = baseinfo.id
        <where>
            <if test="search.resourceAuditStatus !=null and search.resourceAuditStatus !='' ">
                and stock.resource_audit_status = #{search.resourceAuditStatus}
            </if>
            <if test="search.tradeMode !=null and search.tradeMode !='' ">
                and stock.trade_mode = #{search.tradeMode}
            </if>
            <if test="search.tradeStatus!=null and search.tradeStatus.size > 0">
                AND stock.trade_status in
                <foreach item="status" collection="search.tradeStatus" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <!-- 产地 -->
            <if test="search.intactPlace !=null and search.intactPlace !='' ">
                <bind name="intactPlace" value="'%'+ search.intactPlace +'%'"/>
                and baseinfo.intact_place like #{intactPlace}
            </if>
            <!-- 棉花年度 -->
            <if test="search.productYear !=null and search.productYear !='' ">
                and baseinfo.product_year = #{search.productYear}
            </if>
            <!-- 采摘方式 -->
            <if test="search.whsPickMode !=null and search.whsPickMode !='' ">
                and baseinfo.whs_pick_mode = #{search.whsPickMode}
            </if>
<!--            &lt;!&ndash; 颜色级 &ndash;&gt;-->
<!--            <if test="search.colorGrade!=null and search.colorGrade.size > 0">-->
<!--                AND baseinfo.COLOR_GRADE in-->
<!--                <foreach item="color" collection="search.colorGrade" open="(" separator="," close=")">-->
<!--                    #{color}-->
<!--                </foreach>-->
<!--            </if>-->
<!--            &lt;!&ndash; 颜色级占比筛选 &ndash;&gt;-->
<!--            <if test="search.beginColorRate!=null and search.beginColorRate!= '' ">-->
<!--                <if test="search.beginColorList!=null and search.beginColorList.size()>0">-->
<!--                    and-->
<!--                    <foreach item="bc" collection="search.beginColorList" open="(" separator=" or "-->
<!--                             close=")" index="index">-->
<!--                        baseinfo.${bc} >= #{search.beginColorRate}-->
<!--                    </foreach>-->
<!--                </if>-->

<!--            </if>-->
<!--            &lt;!&ndash; 且 &ndash;&gt;-->
<!--            <if test="search.isJust">-->
<!--                <if test="search.endColorRate!=null and search.endColorRate!= '' ">-->
<!--                    <if test="search.endColorList!=null and search.endColorList.size()>0">-->
<!--                        and-->
<!--                        <foreach item="ec" collection="search.endColorList" open="(" separator=" or "-->
<!--                                 close=")" index="index">-->
<!--                            baseinfo.${ec} &lt;= #{search.endColorRate}-->
<!--                        </foreach>-->
<!--                    </if>-->
<!--                </if>-->
<!--            </if>-->
<!--            &lt;!&ndash; 长度 &ndash;&gt;-->
<!--            <if test="search.beginLengthLevel !=null and search.beginLengthLevel!= '' ">-->
<!--                and baseinfo.AVG_LENGTH  &gt;= #{search.beginLengthLevel}-->
<!--            </if>-->
<!--            <if test="search.endLengthLevel !=null and search.endLengthLevel!= '' ">-->
<!--                and baseinfo.AVG_LENGTH  &gt;= #{search.endLengthLevel}-->
<!--            </if>-->
<!--            &lt;!&ndash; 强力 &ndash;&gt;-->
<!--            <if test="search.beginBreak !=null and search.beginBreak!= '' ">-->
<!--                and baseinfo.break_value  &gt;= #{search.beginBreak}-->
<!--            </if>-->
<!--            <if test="search.endBreak !=null and search.endBreak!= '' ">-->
<!--                and baseinfo.break_value  &gt;= #{search.endBreak}-->
<!--            </if>-->
<!--            &lt;!&ndash; 马值 &ndash;&gt;-->
<!--            <if test="search.beginMKL !=null and search.beginMKL!= '' ">-->
<!--                and baseinfo.MAIN_MKL  &gt;= #{search.beginMKL}-->
<!--            </if>-->
<!--            <if test="search.endMKL !=null and search.endMKL!= '' ">-->
<!--                and baseinfo.MAIN_MKL  &gt;= #{search.endMKL}-->
<!--            </if>-->
<!--            &lt;!&ndash; 长整 &ndash;&gt;-->
<!--            <if test="search.beginLenUniformity !=null and search.beginLenUniformity!= '' ">-->
<!--                and baseinfo.uniformity_average_value  &gt;= #{search.beginLenUniformity}-->
<!--            </if>-->
<!--            <if test="search.endLenUniformity !=null and search.endLenUniformity!= '' ">-->
<!--                and baseinfo.uniformity_average_value  &gt;= #{search.endLenUniformity}-->
<!--            </if>-->
<!--            &lt;!&ndash; 含杂 &ndash;&gt;-->
<!--            <if test="search.beginImpurityRate !=null and search.beginImpurityRate!= '' ">-->
<!--                and baseinfo.impurity_rate  &gt;= #{search.beginImpurityRate}-->
<!--            </if>-->
<!--            <if test="search.endImpurityRate !=null and search.endImpurityRate!= '' ">-->
<!--                and baseinfo.impurity_rate  &gt;= #{search.endImpurityRate}-->
<!--            </if>-->
<!--            &lt;!&ndash; 回潮 &ndash;&gt;-->
<!--            <if test="search.beginMoistureContent !=null and search.beginMoistureContent!= '' ">-->
<!--                and baseinfo.moisture_rate  &gt;= #{search.beginMoistureContent}-->
<!--            </if>-->
<!--            <if test="search.endMoistureContent !=null and search.endMoistureContent!= '' ">-->
<!--                and baseinfo.moisture_rate  &gt;= #{search.endMoistureContent}-->
<!--            </if>-->
            <!-- 加工单位 -->
            <if test="search.factoryName !=null and search.factoryName !='' ">
                <bind name="factoryName" value="'%'+ search.factoryName +'%'"/>
                and baseinfo.factory_name like #{factoryName}
            </if>
            <!-- 仓库 -->
            <if test="search.storageWhsName !=null and search.storageWhsName !='' ">
                <bind name="storageWhsName" value="'%'+ search.storageWhsName +'%'"/>
                and baseinfo.storage_whs_name like #{storageWhsName}
            </if>
            <!-- 批号 -->
            <if test="search.batchNo !=null and search.batchNo !='' ">
                <bind name="batchNo" value="'%'+ search.batchNo +'%'"/>
                and baseinfo.batch_no like #{batchNo}
            </if>
            <!-- 字段排序 -->
            <if test="search.sortList!=null and search.sortList.size()>0">
                ORDER BY
                <foreach item="sortStr" collection="search.sortList" open="" separator=","
                         close="" index="index">
                    <choose>
                        <when test="sortStr == 'stock_codeasc'">
                            stock.stock_code asc
                        </when>
                        <when test="sortStr == 'stock_codedesc'">
                            stock.stock_code desc
                        </when>
                        <when test="sortStr == 'color_gradeasc'">
                            baseinfo.color_grade asc
                        </when>
                        <when test="sortStr == 'color_gradedesc'">
                            baseinfo.color_grade desc
                        </when>
                        <when test="sortStr == 'quanlityinfoasc'">
                            baseinfo.avg_length asc,baseinfo.break_value asc,baseinfo.uniformity_average_value asc,baseinfo.impurity_rate asc,baseinfo.moisture_rate asc
                        </when>
                        <when test="sortStr == 'quanlityinfodesc'">
                            baseinfo.avg_length desc,baseinfo.break_value desc,baseinfo.uniformity_average_value desc,baseinfo.impurity_rate desc,baseinfo.moisture_rate desc
                        </when>
                        <when test="sortStr == 'avg_mklasc'">
                            baseinfo.avg_mkl asc
                        </when>
                        <when test="sortStr == 'avg_mkldesc'">
                            baseinfo.avg_mkl desc
                        </when>
                        <when test="sortStr == 'stock_weightasc'">
                            stock.stock_weight asc
                        </when>
                        <when test="sortStr == 'stock_weightdesc'">
                            stock.stock_weight desc
                        </when>
                        <when test="sortStr == 'intact_placeasc'">
                            baseinfo.intact_place asc
                        </when>
                        <when test="sortStr == 'intact_placedesc'">
                            baseinfo.intact_place desc
                        </when>
                        <when test="sortStr == 'storage_whs_nameasc'">
                            baseinfo.storage_whs_name asc
                        </when>
                        <when test="sortStr == 'storage_whs_namedesc'">
                            baseinfo.storage_whs_name desc
                        </when>
                        <when test="sortStr == 'stock_priceasc'">
                            stock.stock_price asc
                        </when>
                        <when test="sortStr == 'stock_pricedesc'">
                            stock.stock_price desc
                        </when>
                        <when test="sortStr == 'zssasc'">
                            whsforward.zss asc
                        </when>
                        <when test="sortStr == 'zssdesc'">
                            whsforward.zss desc
                        </when>
                        <when test="sortStr == 'zmxasc'">
                            whsforward.zmx asc
                        </when>
                        <when test="sortStr == 'zmxdesc'">
                            whsforward.zmx desc
                        </when>
                        <otherwise>
                            ORDER BY stock.create_time DESC<!-- 默认排序 -->
                        </otherwise>
                    </choose>
                </foreach>
            </if>
        </where>

    </select>

    <select id="getAllStockBuyerByPage"
            parameterType="com.basiscotton.manager.hall.vo.BuyerStockReqVo"
            resultType="com.basiscotton.manager.hall.vo.BuyerStockResVo">
        SELECT
            dataInfo.*
        FROM (
            SELECT
            stock.id,
            stock.stock_code,
            baseinfo.warehouse_receipt_no,
            baseinfo.batch_no,
            baseinfo.COLOR_GRADE,
            baseinfo.WHITE_COTTON_L1,
            baseinfo.WHITE_COTTON_L2,
            baseinfo.WHITE_COTTON_L3,
            baseinfo.WHITE_COTTON_L4,
            baseinfo.WHITE_COTTON_L5,
            baseinfo.WHITE_COTTON_L6,
            baseinfo.SPOT_COTTON_L1,
            baseinfo.SPOT_COTTON_L2,
            baseinfo.SPOT_COTTON_L3,
            baseinfo.SPOT_COTTON_L4,
            baseinfo.YELLOW_ISH_COTTON_L1,
            baseinfo.YELLOW_ISH_COTTON_L2,
            baseinfo.YELLOW_ISH_COTTON_L3,
            baseinfo.YELLOW_COTTON_L1,
            baseinfo.YELLOW_COTTON_L2,
            baseinfo.AVG_LENGTH,
            baseinfo.avg_break_rate AS breakValue,
            baseinfo.uniformity_average_value,
            baseinfo.impurity_rate,
            baseinfo.moisture_rate,
            baseinfo.AVG_MKL,
            baseinfo.MAX_MKL,
            baseinfo.MIN_MKL,
            baseinfo.conditioned_weight,
            baseinfo.intact_place,
            baseinfo.storage_whs_name,
            stock.quote_type,
            stock.future_code,
            stock.basis_price,
            stock.remark,
            stock.interest_cost,
            stock.pricing_valid_time,
            stock.trader_code AS sellerCode,
            stock.transport_subsidy_apply_party,
            quote.trader_code AS quoteTraderCode,
            whsforward.ZMX,
            whsforward.ZSS,
            baseinfo.factory_code,
            baseinfo.factory_name
            FROM
            trd_basis_stock stock
            LEFT JOIN trd_basis_stock_base_info baseinfo ON stock.stock_base_info_id = baseinfo.id
            LEFT JOIN v_whs_factory_forward_batch whsforward ON stock.batch_no = whsforward.batch_code
            LEFT JOIN trd_basis_quote quote ON stock.ID = QUOTE.stock_id AND quote.quote_status = 'succ'
            <where>
                <if test="search.traderCode !=null and search.traderCode !='' ">
                    and stock.trader_code != #{search.traderCode}
                </if>
                <if test="search.resourceAuditStatus !=null and search.resourceAuditStatus !='' ">
                    and stock.resource_audit_status = #{search.resourceAuditStatus}
                </if>
                <if test="search.tradeStatus!=null and search.tradeStatus.size > 0">
                    AND stock.trade_status in
                    <foreach item="status" collection="search.tradeStatus" open="(" separator="," close=")">
                        #{status}
                    </foreach>
                </if>
                <if test="search.pricingParty !=null and search.pricingParty !='' ">
                    and stock.pricing_party = #{search.pricingParty}
                </if>


                <!-- 产地 -->
                <if test="search.intactPlaceList!=null and search.intactPlaceList.size > 0">
                    AND
                    <foreach item="intactPlaceStr" collection="search.intactPlaceList" open="(" separator=" OR " close=")">
                        baseinfo.intact_place LIKE CONCAT('%', #{intactPlaceStr}, '%')
                    </foreach>
                </if>
                <!-- 棉花年度 -->
                <if test="search.productYear !=null and search.productYear !='' ">
                    and baseinfo.product_year = #{search.productYear}
                </if>
                <!-- 采摘方式 -->
                <if test="search.whsPickMode !=null and search.whsPickMode !='' ">
                    and baseinfo.whs_pick_mode = #{search.whsPickMode}
                </if>
                <!-- 颜色级 -->
                <if test="search.colorGrade!=null and search.colorGrade.size > 0">
                    AND baseinfo.COLOR_GRADE in
                    <foreach item="color" collection="search.colorGrade" open="(" separator="," close=")">
                        #{color}
                    </foreach>
                </if>
                <!-- 颜色级占比筛选 -->
                <if test="search.beginColorRate!=null and search.beginColorRate!= '' ">
                    <if test="search.beginColorList!=null and search.beginColorList.size()>0">
                        and
                        <foreach item="bc" collection="search.beginColorList" open="(" separator=" or "
                                 close=")" index="index">
                            baseinfo.${bc} >= #{search.beginColorRate}
                        </foreach>
                    </if>

                </if>
                <!-- 且 -->
                <if test="search.isJust">
                    <if test="search.endColorRate!=null and search.endColorRate!= '' ">
                        <if test="search.endColorList!=null and search.endColorList.size()>0">
                            and
                            <foreach item="ec" collection="search.endColorList" open="(" separator=" or "
                                     close=")" index="index">
                                baseinfo.${ec} &lt;= #{search.endColorRate}
                            </foreach>
                        </if>
                    </if>
                </if>
                <!-- 长度 -->
                <if test="search.beginLengthLevel !=null and search.beginLengthLevel!= '' ">
                    and baseinfo.AVG_LENGTH  &gt;= #{search.beginLengthLevel}
                </if>
                <if test="search.endLengthLevel !=null and search.endLengthLevel!= '' ">
                    and baseinfo.AVG_LENGTH  &lt;= #{search.endLengthLevel}
                </if>
                <if test="search.minLength !=null and search.minLength!= '' ">
                    and baseinfo.AVG_LENGTH  &gt;= #{search.minLength}
                </if>
                <!-- 强力 -->
                <if test="search.beginBreak !=null and search.beginBreak!= '' ">
                    and baseinfo.break_value  &gt;= #{search.beginBreak}
                </if>
                <if test="search.endBreak !=null and search.endBreak!= '' ">
                    and baseinfo.break_value  &lt;= #{search.endBreak}
                </if>
                <if test="search.minBreak !=null and search.minBreak!= '' ">
                    and baseinfo.break_value  &gt;= #{search.minBreak}
                </if>
                <!-- 马值 -->
                <if test="search.beginMKL !=null and search.beginMKL!= '' ">
                    and baseinfo.AVG_MKL  &gt;= #{search.beginMKL}
                </if>
                <if test="search.endMKL !=null and search.endMKL!= '' ">
                    and baseinfo.AVG_MKL  &lt;= #{search.endMKL}
                </if>
                <if test="search.minMKL != null and search.minMKL != ''">
                    and baseinfo.AVG_MKL &gt;= #{search.minMKL}
                </if>
                <!-- 长整 -->
                <if test="search.beginLenUniformity !=null and search.beginLenUniformity!= '' ">
                    and baseinfo.uniformity_average_value  &gt;= #{search.beginLenUniformity}
                </if>
                <if test="search.endLenUniformity !=null and search.endLenUniformity!= '' ">
                    and baseinfo.uniformity_average_value  &lt;= #{search.endLenUniformity}
                </if>
                <!-- 含杂 -->
                <if test="search.beginImpurityRate !=null and search.beginImpurityRate!= '' ">
                    and baseinfo.impurity_rate  &gt;= #{search.beginImpurityRate}
                </if>
                <if test="search.endImpurityRate !=null and search.endImpurityRate!= '' ">
                    and baseinfo.impurity_rate  &lt;= #{search.endImpurityRate}
                </if>
                <!-- 回潮 -->
                <if test="search.beginMoistureContent !=null and search.beginMoistureContent!= '' ">
                    and baseinfo.moisture_rate  &gt;= #{search.beginMoistureContent}
                </if>
                <if test="search.endMoistureContent !=null and search.endMoistureContent!= '' ">
                    and baseinfo.moisture_rate  &lt;= #{search.endMoistureContent}
                </if>
                <!-- rd% -->
                <if test="search.minRd !=null and search.minRd!= '' ">
                    and baseinfo.AVG_RD  &gt;= #{search.minRd}
                </if>
                <if test="search.maxRd !=null and search.maxRd!= '' ">
                    and baseinfo.AVG_RD  &lt;= #{search.maxRd}
                </if>
                <!-- +d -->
                <if test="search.minB !=null and search.minB!= '' ">
                    and baseinfo.AVG_PLUS_B  &gt;= #{search.minB}
                </if>
                <if test="search.maxB !=null and search.maxB!= '' ">
                    and baseinfo.AVG_PLUS_B  &lt;= #{search.maxB}
                </if>
                <!-- 加工单位 -->
                <if test="search.factoryName !=null and search.factoryName !='' ">
                    <bind name="factoryName" value="'%'+ search.factoryName +'%'"/>
                    and baseinfo.factory_name like #{factoryName}
                </if>
                <!-- 仓库 -->
                <if test="search.warehouseNameList!=null and search.warehouseNameList.size > 0">
                    AND
                    <foreach item="whsName" collection="search.warehouseNameList" open="(" separator=" OR " close=")">
                        baseinfo.storage_whs_name LIKE CONCAT('%', #{whsName}, '%')
                    </foreach>
                </if>
                <!-- 卖方交易商名称 -->
                <if test="search.traderNameList!=null and search.traderNameList.size > 0">
                    AND
                    <foreach item="traderName" collection="search.traderNameList" open="(" separator=" OR " close=")">
                        stock.TRADER_NAME LIKE CONCAT('%', #{traderName}, '%')
                    </foreach>
                </if>
                <!-- 批号 -->
                <if test="search.batchNo !=null and search.batchNo !='' ">
                    <bind name="batchNo" value="'%'+ search.batchNo +'%'"/>
                    and baseinfo.batch_no like #{batchNo}
                </if>
                <!-- 关键字查询 -->
                <if test="search.keyWordsStr !=null and search.keyWordsStr !='' ">
                    <bind name="keyWordsStr" value="'%'+ search.keyWordsStr +'%'"/>
                    AND (baseinfo.factory_name like #{keyWordsStr}
                    OR  baseinfo.storage_whs_name like #{keyWordsStr}
                    OR  stock.trader_name like #{keyWordsStr})
                </if>
                <!-- 字段排序 -->
                <if test="search.sortList!=null and search.sortList.size()>0">
                    ORDER BY
                    <foreach item="sortStr" collection="search.sortList" open="" separator=","
                             close="" index="index">
                        <choose>
                            <when test="sortStr == 'stock_codeasc'">
                                stock.stock_code asc
                            </when>
                            <when test="sortStr == 'stock_codedesc'">
                                stock.stock_code desc
                            </when>
                            <when test="sortStr == 'color_gradeasc'">
                                baseinfo.color_grade asc
                            </when>
                            <when test="sortStr == 'color_gradedesc'">
                                baseinfo.color_grade desc
                            </when>
                            <when test="sortStr == 'quanlityinfoasc'">
                                baseinfo.avg_length asc,baseinfo.break_value asc,baseinfo.uniformity_average_value asc,baseinfo.impurity_rate asc,baseinfo.moisture_rate asc
                            </when>
                            <when test="sortStr == 'quanlityinfodesc'">
                                baseinfo.avg_length desc,baseinfo.break_value desc,baseinfo.uniformity_average_value desc,baseinfo.impurity_rate desc,baseinfo.moisture_rate desc
                            </when>
                            <when test="sortStr == 'avg_mklasc'">
                                baseinfo.avg_mkl asc
                            </when>
                            <when test="sortStr == 'avg_mkldesc'">
                                baseinfo.avg_mkl desc
                            </when>
                            <when test="sortStr == 'conditioned_weightasc'">
                                baseinfo.conditioned_weight asc
                            </when>
                            <when test="sortStr == 'conditioned_weightdesc'">
                                baseinfo.conditioned_weight desc
                            </when>
                            <when test="sortStr == 'intact_placeasc'">
                                baseinfo.intact_place asc
                            </when>
                            <when test="sortStr == 'intact_placedesc'">
                                baseinfo.intact_place desc
                            </when>
                            <when test="sortStr == 'storage_whs_nameasc'">
                                baseinfo.storage_whs_name asc
                            </when>
                            <when test="sortStr == 'storage_whs_namedesc'">
                                baseinfo.storage_whs_name desc
                            </when>
                            <when test="sortStr == 'basis_priceasc'">
                                stock.basis_price asc
                            </when>
                            <when test="sortStr == 'basis_pricedesc'">
                                stock.basis_price desc
                            </when>
                            <when test="sortStr == 'zssasc'">
                                whsforward.zss asc
                            </when>
                            <when test="sortStr == 'zssdesc'">
                                whsforward.zss desc
                            </when>
                            <when test="sortStr == 'zmxasc'">
                                whsforward.zmx asc
                            </when>
                            <when test="sortStr == 'zmxdesc'">
                                whsforward.zmx desc
                            </when>
                            <otherwise>
                                stock.basis_price ASC <!-- 默认排序 -->
                            </otherwise>
                        </choose>
                    </foreach>
                </if>
                <if test="search.queryNum !=null and search.queryNum!= '' ">
                    LIMIT  #{search.queryNum}
                </if>
            </where>
        ) dataInfo
    </select>

    <select id="getTraderAllStockByPage"
            parameterType="com.basiscotton.manager.stock.vo.TraderAllStockReqVo"
            resultType="com.basiscotton.manager.stock.vo.TraderAllStockResVo">
        SELECT
            stock.id,
            stock.stock_code,
            stock.resource_audit_status,
            stock.quote_type,
            stock.resource_trade_type,
            stock.resource_display_type,
            stock.future_code,
            stock.basis_price,
            stock.pricing_valid_time,
            stock.interest_cost,
            baseinfo.warehouse_receipt_no,
            baseinfo.batch_no,
            baseinfo.quantity,
            baseinfo.marks_weight,
            baseinfo.conditioned_weight,
            baseinfo.COLOR_GRADE,
            baseinfo.AVG_LENGTH,
            baseinfo.break_value,
            baseinfo.uniformity_average_value,
            baseinfo.impurity_rate,
            baseinfo.moisture_rate,
            baseinfo.AVG_MKL,
            baseinfo.intact_place,
            baseinfo.storage_whs_name,
            baseinfo.product_year,
            baseinfo.whs_supervision_status,
            baseinfo.supervise_name,
            stock.remark,
            stock.transport_subsidy_apply_party
        FROM
            trd_basis_stock stock
            LEFT JOIN trd_basis_stock_base_info baseinfo ON stock.stock_base_info_id = baseinfo.id
        <where>
            <if test="search.traderCode !=null and search.traderCode !='' ">
                and baseinfo.trader_code = #{search.traderCode}
            </if>
            <if test="search.resourceAuditStatus !=null and search.resourceAuditStatus !='' ">
                and stock.resource_audit_status = #{search.resourceAuditStatus}
            </if>
            <if test="search.tradeStatus!=null and search.tradeStatus.size > 0">
                AND stock.trade_status in
                <foreach item="status" collection="search.tradeStatus" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <!-- 仓单号 -->
            <if test="search.warehouseReceiptNo !=null and search.warehouseReceiptNo !='' ">
                <bind name="warehouseReceiptNo" value="'%'+ search.warehouseReceiptNo +'%'"/>
                and baseinfo.warehouse_receipt_no like #{warehouseReceiptNo}
            </if>
            <!-- 批号 -->
            <if test="search.batchNo !=null and search.batchNo !='' ">
                <bind name="batchNo" value="'%'+ search.batchNo +'%'"/>
                and baseinfo.batch_no like #{batchNo}
            </if>
            <!-- 仓库 -->
            <if test="search.storageWhsName !=null and search.storageWhsName !='' ">
                <bind name="storageWhsName" value="'%'+ search.storageWhsName +'%'"/>
                and baseinfo.storage_whs_name like #{storageWhsName}
            </if>
        </where>
        ORDER BY stock.stock_code DESC
    </select>

    <select id="getManageStockByPage" resultType="com.basiscotton.manager.resources.manage.vo.ManageStockResVo">
        SELECT
            stock.id,
            stock.stock_code,
            stock.resource_audit_status,
            stock.quote_type,
            stock.resource_trade_type,
            stock.resource_display_type,
            stock.future_code,
            stock.basis_price,
            stock.pricing_valid_time,
            stock.interest_cost,
            baseinfo.warehouse_receipt_no,
            baseinfo.batch_no,
            baseinfo.quantity,
            baseinfo.marks_weight,
            baseinfo.conditioned_weight,
            baseinfo.COLOR_GRADE,
            baseinfo.AVG_LENGTH,
            baseinfo.break_value,
            baseinfo.uniformity_average_value,
            baseinfo.impurity_rate,
            baseinfo.moisture_rate,
            baseinfo.AVG_MKL,
            baseinfo.intact_place,
            baseinfo.storage_whs_name,
            baseinfo.product_year,
            baseinfo.whs_supervision_status,
            baseinfo.supervise_name,
            stock.remark,
            baseinfo.trader_code,
            baseinfo.trader_name,
            stock.transport_subsidy_apply_party
        FROM
        trd_basis_stock stock
        LEFT JOIN trd_basis_stock_base_info baseinfo ON stock.stock_base_info_id = baseinfo.id
        <where>
            <if test="search.resourceAuditStatus !=null and search.resourceAuditStatus !='' ">
                and stock.resource_audit_status = #{search.resourceAuditStatus}
            </if>
            <if test="search.tradeStatus!=null and search.tradeStatus.size > 0">
                AND stock.trade_status in
                <foreach item="status" collection="search.tradeStatus" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>



            <if test="search.tradeMode !=null and search.tradeMode !='' ">
                and stock.trade_mode = #{search.tradeMode}
            </if>




            <!-- 仓单号 -->
            <if test="search.warehouseReceiptNo !=null and search.warehouseReceiptNo !='' ">
                <bind name="warehouseReceiptNo" value="'%'+ search.warehouseReceiptNo +'%'"/>
                and baseinfo.warehouse_receipt_no like #{warehouseReceiptNo}
            </if>
            <!-- 批号 -->
            <if test="search.batchNo !=null and search.batchNo !='' ">
                <bind name="batchNo" value="'%'+ search.batchNo +'%'"/>
                and baseinfo.batch_no like #{batchNo}
            </if>
            <!-- 仓库 -->
            <if test="search.storageWhsName !=null and search.storageWhsName !='' ">
                <bind name="storageWhsName" value="'%'+ search.storageWhsName +'%'"/>
                and baseinfo.storage_whs_name like #{storageWhsName}
            </if>
        </where>
        ORDER BY stock.stock_code DESC
    </select>

    <select id="getAllStock" resultType="com.basiscotton.base.entity.BasisStockEntity">
        SELECT
            stock.*
        FROM
            trd_basis_stock stock
            JOIN trd_basis_stock_base_info baseinfo ON stock.stock_base_info_id = baseinfo.id
        WHERE
            stock.resource_audit_status = 3
            AND stock.trade_status IN (1,2)
            AND quote_type = 2
            AND PRICING_PARTY = 1
            AND pricing_valid_time &lt;= #{pricingValidTime}
    </select>
    <select id="getAllResource" resultType="com.basiscotton.base.entity.BasisStockEntity">
        SELECT
            stock.*
        FROM
            trd_basis_stock stock
                JOIN trd_basis_stock_base_info baseinfo ON stock.stock_base_info_id = baseinfo.id
        WHERE
            stock.resource_audit_status IN (1,2)
          AND stock.trade_status IN (1,2)
          AND quote_type = 2
          AND PRICING_PARTY = 1
          AND pricing_valid_time &lt;= #{pricingValidTime}
    </select>


    <sql id="getDataCountListByField">
        SELECT
            stock.id,
            stock.trader_name,
            baseinfo.storage_whs_name
        FROM
            trd_basis_stock stock
            LEFT JOIN trd_basis_stock_base_info baseinfo ON stock.stock_base_info_id = baseinfo.id
            LEFT JOIN trd_basis_quote quote ON stock.ID = QUOTE.stock_id AND quote.quote_status = 'succ'
        <where>
            <if test="search.traderCode !=null and search.traderCode !='' ">
                and stock.trader_code != #{search.traderCode}
            </if>
            <if test="search.resourceAuditStatus !=null and search.resourceAuditStatus !='' ">
                and stock.resource_audit_status = #{search.resourceAuditStatus}
            </if>
            <if test="search.tradeStatus!=null and search.tradeStatus.size > 0">
                AND stock.trade_status in
                <foreach item="status" collection="search.tradeStatus" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="search.pricingParty !=null and search.pricingParty !='' ">
                and stock.pricing_party = #{search.pricingParty}
            </if>


            <!-- 产地 -->
            <if test="search.intactPlaceList!=null and search.intactPlaceList.size > 0">
                AND
                <foreach item="intactPlaceStr" collection="search.intactPlaceList" open="(" separator=" OR " close=")">
                    baseinfo.intact_place LIKE CONCAT('%', #{intactPlaceStr}, '%')
                </foreach>
            </if>
            <!-- 棉花年度 -->
            <if test="search.productYear !=null and search.productYear !='' ">
                and baseinfo.product_year = #{search.productYear}
            </if>
            <!-- 采摘方式 -->
            <if test="search.whsPickMode !=null and search.whsPickMode !='' ">
                and baseinfo.whs_pick_mode = #{search.whsPickMode}
            </if>
            <!-- 颜色级 -->
            <if test="search.colorGrade!=null and search.colorGrade.size > 0">
                AND baseinfo.COLOR_GRADE in
                <foreach item="color" collection="search.colorGrade" open="(" separator="," close=")">
                    #{color}
                </foreach>
            </if>
            <!-- 颜色级占比筛选 -->
            <if test="search.beginColorRate!=null and search.beginColorRate!= '' ">
                <if test="search.beginColorList!=null and search.beginColorList.size()>0">
                    and
                    <foreach item="bc" collection="search.beginColorList" open="(" separator=" or "
                             close=")" index="index">
                        baseinfo.${bc} >= #{search.beginColorRate}
                    </foreach>
                </if>

            </if>
            <!-- 且 -->
            <if test="search.isJust">
                <if test="search.endColorRate!=null and search.endColorRate!= '' ">
                    <if test="search.endColorList!=null and search.endColorList.size()>0">
                        and
                        <foreach item="ec" collection="search.endColorList" open="(" separator=" or "
                                 close=")" index="index">
                            baseinfo.${ec} &lt;= #{search.endColorRate}
                        </foreach>
                    </if>
                </if>
            </if>
            <!-- 长度 -->
            <if test="search.beginLengthLevel !=null and search.beginLengthLevel!= '' ">
                and baseinfo.AVG_LENGTH  &gt;= #{search.beginLengthLevel}
            </if>
            <if test="search.endLengthLevel !=null and search.endLengthLevel!= '' ">
                and baseinfo.AVG_LENGTH  &lt;= #{search.endLengthLevel}
            </if>
            <if test="search.minLength !=null and search.minLength!= '' ">
                and baseinfo.AVG_LENGTH  &gt;= #{search.minLength}
            </if>
            <!-- 强力 -->
            <if test="search.beginBreak !=null and search.beginBreak!= '' ">
                and baseinfo.break_value  &gt;= #{search.beginBreak}
            </if>
            <if test="search.endBreak !=null and search.endBreak!= '' ">
                and baseinfo.break_value  &lt;= #{search.endBreak}
            </if>
            <if test="search.minBreak !=null and search.minBreak!= '' ">
                and baseinfo.break_value  &gt;= #{search.minBreak}
            </if>
            <!-- 马值 -->
            <if test="search.beginMKL !=null and search.beginMKL!= '' ">
                and baseinfo.AVG_MKL  &gt;= #{search.beginMKL}
            </if>
            <if test="search.endMKL !=null and search.endMKL!= '' ">
                and baseinfo.AVG_MKL  &lt;= #{search.endMKL}
            </if>
            <if test="search.minMKL != null and search.minMKL != ''">
                and baseinfo.AVG_MKL &gt;= #{search.minMKL}
            </if>
            <!-- 长整 -->
            <if test="search.beginLenUniformity !=null and search.beginLenUniformity!= '' ">
                and baseinfo.uniformity_average_value  &gt;= #{search.beginLenUniformity}
            </if>
            <if test="search.endLenUniformity !=null and search.endLenUniformity!= '' ">
                and baseinfo.uniformity_average_value  &lt;= #{search.endLenUniformity}
            </if>
            <!-- 含杂 -->
            <if test="search.beginImpurityRate !=null and search.beginImpurityRate!= '' ">
                and baseinfo.impurity_rate  &gt;= #{search.beginImpurityRate}
            </if>
            <if test="search.endImpurityRate !=null and search.endImpurityRate!= '' ">
                and baseinfo.impurity_rate  &lt;= #{search.endImpurityRate}
            </if>
            <!-- 回潮 -->
            <if test="search.beginMoistureContent !=null and search.beginMoistureContent!= '' ">
                and baseinfo.moisture_rate  &gt;= #{search.beginMoistureContent}
            </if>
            <if test="search.endMoistureContent !=null and search.endMoistureContent!= '' ">
                and baseinfo.moisture_rate  &lt;= #{search.endMoistureContent}
            </if>
            <!-- rd% -->
            <if test="search.minRd !=null and search.minRd!= '' ">
                and baseinfo.AVG_RD  &gt;= #{search.minRd}
            </if>
            <if test="search.maxRd !=null and search.maxRd!= '' ">
                and baseinfo.AVG_RD  &lt;= #{search.maxRd}
            </if>
            <!-- +d -->
            <if test="search.minB !=null and search.minB!= '' ">
                and baseinfo.AVG_PLUS_B  &gt;= #{search.minB}
            </if>
            <if test="search.maxB !=null and search.maxB!= '' ">
                and baseinfo.AVG_PLUS_B  &lt;= #{search.maxB}
            </if>
            <!-- 加工单位 -->
            <if test="search.factoryName !=null and search.factoryName !='' ">
                <bind name="factoryName" value="'%'+ search.factoryName +'%'"/>
                and baseinfo.factory_name like #{factoryName}
            </if>
            <!-- 仓库 -->
            <if test="search.warehouseNameList!=null and search.warehouseNameList.size > 0">
                AND
                <foreach item="warehouseName" collection="search.warehouseNameList" open="(" separator="OR" close=")">
                    baseinfo.storage_whs_name LIKE CONCAT('%', #{warehouseName}, '%')
                </foreach>
            </if>
            <!-- 卖方交易商名称 -->
            <if test="search.traderNameList!=null and search.traderNameList.size > 0">
                AND
                <foreach item="traderName" collection="search.traderNameList" open="(" separator="OR" close=")">
                    stock.TRADER_NAME LIKE CONCAT('%', #{traderName}, '%')
                </foreach>
            </if>
            <!-- 批号 -->
            <if test="search.batchNo !=null and search.batchNo !='' ">
                <bind name="batchNo" value="'%'+ search.batchNo +'%'"/>
                and baseinfo.batch_no like #{batchNo}
            </if>
            <!-- 关键字查询 -->
            <if test="search.keyWordsStr !=null and search.keyWordsStr !='' ">
                <bind name="keyWordsStr" value="'%'+ search.keyWordsStr +'%'"/>
                AND (baseinfo.factory_name like #{keyWordsStr}
                OR  baseinfo.storage_whs_name like #{keyWordsStr}
                OR  stock.trader_name like #{keyWordsStr})
            </if>
            <if test="search.queryNum !=null and search.queryNum!= '' ">
                LIMIT  #{search.queryNum}
            </if>
        </where>
    </sql>

    <select id="getDataCountListByWarehouseName" resultType="com.basiscotton.manager.hall.vo.StockCountVo">
        SELECT
            dataInfo.storage_whs_name AS dataName,
            COUNT(dataInfo.id) AS stockNum
        FROM (
            <include refid="getDataCountListByField"/>
        ) dataInfo GROUP BY dataInfo.storage_whs_name ORDER BY stockNum DESC
    </select>

    <select id="getDataCountListByTraderName" resultType="com.basiscotton.manager.hall.vo.StockCountVo">
        SELECT
        dataInfo.trader_name AS dataName,
        COUNT(dataInfo.id) AS stockNum
        FROM (
        <include refid="getDataCountListByField"/>
        ) dataInfo GROUP BY dataInfo.trader_name ORDER BY stockNum DESC
    </select>

    <select id="getTraderAllSpotStockByPage"
            parameterType="com.basiscotton.manager.stock.vo.TraderAllStockReqVo"
            resultType="com.basiscotton.manager.stock.vo.TraderAllSpotStockResVo">
        SELECT
        stock.id,
        stock.stock_code,
        stock.resource_audit_status,
        stock.trade_status,
        stock.stock_price,
        stock.stock_validity_end,
        stock.negotiable,
        stock.transport_subsidy_apply_party,
        stock.stock_weight,
        stock.trader_code,
        stock.trader_name,
        baseinfo.warehouse_receipt_no,
        baseinfo.batch_no,
        baseinfo.quantity,
        baseinfo.marks_weight,
        baseinfo.conditioned_weight,
        baseinfo.COLOR_GRADE,
        baseinfo.AVG_LENGTH,
        baseinfo.break_value,
        baseinfo.uniformity_average_value,
        baseinfo.impurity_rate,
        baseinfo.moisture_rate,
        baseinfo.main_mkl,
        baseinfo.intact_place,
        baseinfo.storage_whs_name,
        baseinfo.product_year,
        baseinfo.whs_supervision_status,
        baseinfo.supervise_name,
        baseinfo.avg_mkl,
        stock.remark
        FROM
        trd_basis_stock stock
        LEFT JOIN trd_basis_stock_base_info baseinfo ON stock.stock_base_info_id = baseinfo.id
        <where>
            stock.trade_mode = 1
            <if test="search.traderCode !=null and search.traderCode !='' ">
                and baseinfo.trader_code = #{search.traderCode}
            </if>
            <if test="search.stockSource !=null and search.stockSource !='' ">
                and stock.stock_source = #{search.stockSource}
            </if>
            <if test="search.resourceAuditStatus !=null and search.resourceAuditStatus !='' ">
                and stock.resource_audit_status = #{search.resourceAuditStatus}
            </if>
            <if test="search.batchNo !=null and search.batchNo !='' ">
                and stock.batch_no = #{search.batchNo}
            </if>
            <if test="search.stockCode !=null and search.stockCode !='' ">
                and stock.stock_code = #{search.stockCode}
            </if>
            <if test="search.createUserName !=null and search.createUserName !='' ">
                and stock.create_user_name = #{search.createUserName}
            </if>
            <!--交易商名称-->
            <if test="search.traderName != null and search.traderName != ''">
                <bind name="traderName" value="'%'+search.traderName+'%'"/>
                AND stock.trader_name LIKE #{traderName}
            </if>
            <if test="search.tradeStatus!=null and search.tradeStatus.size > 0">
                AND stock.trade_status in
                <foreach item="status" collection="search.tradeStatus" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <!-- 仓单号 -->
            <if test="search.warehouseReceiptNo !=null and search.warehouseReceiptNo !='' ">
                <bind name="warehouseReceiptNo" value="'%'+ search.warehouseReceiptNo +'%'"/>
                and baseinfo.warehouse_receipt_no like #{warehouseReceiptNo}
            </if>
            <!-- 批号 -->
            <if test="search.batchNo !=null and search.batchNo !='' ">
                <bind name="batchNo" value="'%'+ search.batchNo +'%'"/>
                and baseinfo.batch_no like #{batchNo}
            </if>
            <!-- 仓库 -->
            <if test="search.storageWhsName !=null and search.storageWhsName !='' ">
                <bind name="storageWhsName" value="'%'+ search.storageWhsName +'%'"/>
                and baseinfo.storage_whs_name like #{storageWhsName}
            </if>
        </where>
        ORDER BY stock.stock_code DESC
    </select>

    <select id="getNowStockStatistics" resultType="com.basiscotton.manager.hall.vo.NowStockStatisticsVo">
        SELECT
            SUM( stockVo.stockStatistics ) AS stockStatistics,
            SUM( stockVo.quoteStatistics ) AS quoteStatistics,
            SUM( stockVo.tradeStatistics ) AS tradeStatistics
        FROM
            (
                SELECT
                    stock.trade_status,
                    stock.stock_weight AS stockStatistics,
                    CASE
                        WHEN quote.quote_status IS NOT NULL THEN
                            stock.stock_weight ELSE 0
                        END quoteStatistics,
                    CASE
                        WHEN stock.trade_status = 3 THEN
                            stock.stock_weight ELSE 0
                        END tradeStatistics
                FROM
                    trd_basis_stock stock
                        LEFT JOIN trd_basis_quote quote ON stock.id = quote.stock_id
                WHERE
                    stock.trade_mode = 1
                  AND stock.trade_status IN ( 2, 3 )
                  AND DATE_FORMAT( stock.create_time, '%Y-%m-%d' ) >= DATE_FORMAT( #{nowDate}, '%Y-%m-%d' )
            ) stockVo
    </select>
    <select id="getNowTradeStockTiTle" resultType="com.basiscotton.manager.hall.vo.NowTradeStockTitleVo">
        SELECT
            stock_code,
            trade_basis_price
        FROM
            trd_basis_delivery_target
        WHERE
            trade_mode = 1
          AND DATE_FORMAT(create_time, '%Y-%m-%d' ) = DATE_FORMAT( #{nowDate}, '%Y-%m-%d' )
        ORDER BY create_time DESC
    </select>

    <select id="getAllStockList" resultType="com.basiscotton.manager.hall.vo.BuyerStockResVo">
        SELECT
        stock.id,
        stock.stock_code,
        baseinfo.warehouse_receipt_no,
        baseinfo.batch_no,
        baseinfo.COLOR_GRADE,
        baseinfo.AVG_LENGTH,
        baseinfo.break_value,
        baseinfo.avg_break_rate BRUTEFORCE,
        baseinfo.uniformity_average_value,
        baseinfo.impurity_rate,
        baseinfo.moisture_rate,
        baseinfo.AVG_MKL,
        baseinfo.conditioned_weight,
        baseinfo.intact_place,
        baseinfo.storage_whs_name,
        baseinfo.product_year,
        stock.quote_type,
        stock.future_code,
        stock.stock_price,
        stock.stock_source,
        stock.negotiable,
        stock.transport_subsidy_apply_party,
        stock.stock_weight,
        stock.remark
        FROM
        trd_basis_stock stock
        LEFT JOIN trd_basis_stock_base_info baseinfo ON stock.stock_base_info_id = baseinfo.id
        <where>
            <if test="search.resourceAuditStatus !=null and search.resourceAuditStatus !='' ">
                and stock.resource_audit_status = #{search.resourceAuditStatus}
            </if>
            <if test="search.tradeMode !=null and search.tradeMode !='' ">
                and stock.trade_mode = #{search.tradeMode}
            </if>
            <if test="search.tradeStatus!=null and search.tradeStatus.size > 0">
                AND stock.trade_status in
                <foreach item="status" collection="search.tradeStatus" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <!-- 产地 -->
            <if test="search.intactPlace !=null and search.intactPlace !='' ">
                <bind name="intactPlace" value="'%'+ search.intactPlace +'%'"/>
                and baseinfo.intact_place like #{intactPlace}
            </if>
            <!-- 棉花年度 -->
            <if test="search.productYear !=null and search.productYear !='' ">
                and baseinfo.product_year = #{search.productYear}
            </if>
            <!-- 采摘方式 -->
            <if test="search.whsPickMode !=null and search.whsPickMode !='' ">
                and baseinfo.whs_pick_mode = #{search.whsPickMode}
            </if>
            <!--            &lt;!&ndash; 颜色级 &ndash;&gt;-->
            <!--            <if test="search.colorGrade!=null and search.colorGrade.size > 0">-->
            <!--                AND baseinfo.COLOR_GRADE in-->
            <!--                <foreach item="color" collection="search.colorGrade" open="(" separator="," close=")">-->
            <!--                    #{color}-->
            <!--                </foreach>-->
            <!--            </if>-->
            <!--            &lt;!&ndash; 颜色级占比筛选 &ndash;&gt;-->
            <!--            <if test="search.beginColorRate!=null and search.beginColorRate!= '' ">-->
            <!--                <if test="search.beginColorList!=null and search.beginColorList.size()>0">-->
            <!--                    and-->
            <!--                    <foreach item="bc" collection="search.beginColorList" open="(" separator=" or "-->
            <!--                             close=")" index="index">-->
            <!--                        baseinfo.${bc} >= #{search.beginColorRate}-->
            <!--                    </foreach>-->
            <!--                </if>-->

            <!--            </if>-->
            <!--            &lt;!&ndash; 且 &ndash;&gt;-->
            <!--            <if test="search.isJust">-->
            <!--                <if test="search.endColorRate!=null and search.endColorRate!= '' ">-->
            <!--                    <if test="search.endColorList!=null and search.endColorList.size()>0">-->
            <!--                        and-->
            <!--                        <foreach item="ec" collection="search.endColorList" open="(" separator=" or "-->
            <!--                                 close=")" index="index">-->
            <!--                            baseinfo.${ec} &lt;= #{search.endColorRate}-->
            <!--                        </foreach>-->
            <!--                    </if>-->
            <!--                </if>-->
            <!--            </if>-->
            <!--            &lt;!&ndash; 长度 &ndash;&gt;-->
            <!--            <if test="search.beginLengthLevel !=null and search.beginLengthLevel!= '' ">-->
            <!--                and baseinfo.AVG_LENGTH  &gt;= #{search.beginLengthLevel}-->
            <!--            </if>-->
            <!--            <if test="search.endLengthLevel !=null and search.endLengthLevel!= '' ">-->
            <!--                and baseinfo.AVG_LENGTH  &gt;= #{search.endLengthLevel}-->
            <!--            </if>-->
            <!--            &lt;!&ndash; 强力 &ndash;&gt;-->
            <!--            <if test="search.beginBreak !=null and search.beginBreak!= '' ">-->
            <!--                and baseinfo.break_value  &gt;= #{search.beginBreak}-->
            <!--            </if>-->
            <!--            <if test="search.endBreak !=null and search.endBreak!= '' ">-->
            <!--                and baseinfo.break_value  &gt;= #{search.endBreak}-->
            <!--            </if>-->
            <!--            &lt;!&ndash; 马值 &ndash;&gt;-->
            <!--            <if test="search.beginMKL !=null and search.beginMKL!= '' ">-->
            <!--                and baseinfo.MAIN_MKL  &gt;= #{search.beginMKL}-->
            <!--            </if>-->
            <!--            <if test="search.endMKL !=null and search.endMKL!= '' ">-->
            <!--                and baseinfo.MAIN_MKL  &gt;= #{search.endMKL}-->
            <!--            </if>-->
            <!--            &lt;!&ndash; 长整 &ndash;&gt;-->
            <!--            <if test="search.beginLenUniformity !=null and search.beginLenUniformity!= '' ">-->
            <!--                and baseinfo.uniformity_average_value  &gt;= #{search.beginLenUniformity}-->
            <!--            </if>-->
            <!--            <if test="search.endLenUniformity !=null and search.endLenUniformity!= '' ">-->
            <!--                and baseinfo.uniformity_average_value  &gt;= #{search.endLenUniformity}-->
            <!--            </if>-->
            <!--            &lt;!&ndash; 含杂 &ndash;&gt;-->
            <!--            <if test="search.beginImpurityRate !=null and search.beginImpurityRate!= '' ">-->
            <!--                and baseinfo.impurity_rate  &gt;= #{search.beginImpurityRate}-->
            <!--            </if>-->
            <!--            <if test="search.endImpurityRate !=null and search.endImpurityRate!= '' ">-->
            <!--                and baseinfo.impurity_rate  &gt;= #{search.endImpurityRate}-->
            <!--            </if>-->
            <!--            &lt;!&ndash; 回潮 &ndash;&gt;-->
            <!--            <if test="search.beginMoistureContent !=null and search.beginMoistureContent!= '' ">-->
            <!--                and baseinfo.moisture_rate  &gt;= #{search.beginMoistureContent}-->
            <!--            </if>-->
            <!--            <if test="search.endMoistureContent !=null and search.endMoistureContent!= '' ">-->
            <!--                and baseinfo.moisture_rate  &gt;= #{search.endMoistureContent}-->
            <!--            </if>-->
            <!-- 加工单位 -->
            <if test="search.factoryName !=null and search.factoryName !='' ">
                <bind name="factoryName" value="'%'+ search.factoryName +'%'"/>
                and baseinfo.factory_name like #{factoryName}
            </if>
            <!-- 仓库 -->
            <if test="search.storageWhsName !=null and search.storageWhsName !='' ">
                <bind name="storageWhsName" value="'%'+ search.storageWhsName +'%'"/>
                and baseinfo.storage_whs_name like #{storageWhsName}
            </if>
            <!-- 批号 -->
            <if test="search.batchNo !=null and search.batchNo !='' ">
                <bind name="batchNo" value="'%'+ search.batchNo +'%'"/>
                and baseinfo.batch_no like #{batchNo}
            </if>
            <!-- 字段排序 -->
            <if test="search.sortList!=null and search.sortList.size()>0">
                ORDER BY
                <foreach item="sortStr" collection="search.sortList" open="" separator=","
                         close="" index="index">
                    <choose>
                        <when test="sortStr == 'stock_codeasc'">
                            stock.stock_code asc
                        </when>
                        <when test="sortStr == 'stock_codedesc'">
                            stock.stock_code desc
                        </when>
                        <when test="sortStr == 'color_gradeasc'">
                            baseinfo.color_grade asc
                        </when>
                        <when test="sortStr == 'color_gradedesc'">
                            baseinfo.color_grade desc
                        </when>
                        <when test="sortStr == 'quanlityinfoasc'">
                            baseinfo.avg_length asc,baseinfo.break_value asc,baseinfo.uniformity_average_value asc,baseinfo.impurity_rate asc,baseinfo.moisture_rate asc
                        </when>
                        <when test="sortStr == 'quanlityinfodesc'">
                            baseinfo.avg_length desc,baseinfo.break_value desc,baseinfo.uniformity_average_value desc,baseinfo.impurity_rate desc,baseinfo.moisture_rate desc
                        </when>
                        <when test="sortStr == 'avg_mklasc'">
                            baseinfo.avg_mkl asc
                        </when>
                        <when test="sortStr == 'avg_mkldesc'">
                            baseinfo.avg_mkl desc
                        </when>
                        <when test="sortStr == 'stock_weightasc'">
                            stock.stock_weight asc
                        </when>
                        <when test="sortStr == 'stock_weightdesc'">
                            stock.stock_weight desc
                        </when>
                        <when test="sortStr == 'intact_placeasc'">
                            baseinfo.intact_place asc
                        </when>
                        <when test="sortStr == 'intact_placedesc'">
                            baseinfo.intact_place desc
                        </when>
                        <when test="sortStr == 'storage_whs_nameasc'">
                            baseinfo.storage_whs_name asc
                        </when>
                        <when test="sortStr == 'storage_whs_namedesc'">
                            baseinfo.storage_whs_name desc
                        </when>
                        <when test="sortStr == 'stock_priceasc'">
                            stock.stock_price asc
                        </when>
                        <when test="sortStr == 'stock_pricedesc'">
                            stock.stock_price desc
                        </when>
                        <when test="sortStr == 'zssasc'">
                            whsforward.zss asc
                        </when>
                        <when test="sortStr == 'zssdesc'">
                            whsforward.zss desc
                        </when>
                        <when test="sortStr == 'zmxasc'">
                            whsforward.zmx asc
                        </when>
                        <when test="sortStr == 'zmxdesc'">
                            whsforward.zmx desc
                        </when>
                        <otherwise>
                            ORDER BY stock.create_time DESC<!-- 默认排序 -->
                        </otherwise>
                    </choose>
                </foreach>
            </if>
        </where>
    </select>

</mapper>
