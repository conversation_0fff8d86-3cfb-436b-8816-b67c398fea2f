<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.basiscotton.base.mappers.BasisPricingCommandMapper">
  <resultMap id="BaseResultMap" type="com.basiscotton.base.entity.BasisPricingCommandEntity">
    <!--@mbg.generated-->
    <!--@Table trd_basis_pricing_command-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="target_id" jdbcType="BIGINT" property="targetId" />
    <result column="transaction_no" jdbcType="VARCHAR" property="transactionNo" />
    <result column="pricing_price" jdbcType="DECIMAL" property="pricingPrice" />
    <result column="pricing_valid_time" jdbcType="TIMESTAMP" property="pricingValidTime" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="warehouse_receipt_no" jdbcType="VARCHAR" property="warehouseReceiptNo" />
    <result column="future_code" jdbcType="VARCHAR" property="futureCode" />
    <result column="pricing_type" jdbcType="TINYINT" property="pricingType" />
    <result column="pricing_commander_code" jdbcType="VARCHAR" property="pricingCommanderCode" />
    <result column="pricing_commander_name" jdbcType="VARCHAR" property="pricingCommanderName" />
    <result column="pricing_taker_code" jdbcType="VARCHAR" property="pricingTakerCode" />
    <result column="pricing_taker_name" jdbcType="VARCHAR" property="pricingTakerName" />
    <result column="command_status" jdbcType="TINYINT" property="commandStatus" />
    <result column="command_create_time" jdbcType="TIMESTAMP" property="commandCreateTime" />
    <result column="command_effect_time" jdbcType="TIMESTAMP" property="commandEffectTime" />
    <result column="command_effect_type" jdbcType="TINYINT" property="commandEffectType" />
    <result column="pricing_complete_time" jdbcType="TIMESTAMP" property="pricingCompleteTime" />
    <result column="pricing_cancel_time" jdbcType="TIMESTAMP" property="pricingCancelTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, target_id, transaction_no, pricing_price, pricing_valid_time, batch_no, warehouse_receipt_no, 
    future_code, pricing_type, pricing_commander_code, pricing_commander_name, pricing_taker_code, 
    pricing_taker_name, command_status, command_create_time, command_effect_time, command_effect_type, 
    pricing_complete_time, pricing_cancel_time, create_time, create_user_id, create_user_name, 
    update_time, update_user_id, update_user_name
  </sql>

    <select id="getAllPricingCommandList" resultType="com.basiscotton.deliverycore.cache.bo.PricingCommand">
      SELECT
        id AS commandId,
        target_id,
        batch_no,
        future_code,
        pricing_price,
        command_status,
        command_create_time,
        command_effect_time
      FROM trd_basis_pricing_command
      <where>
        <if test="statusList != null">
          command_status IN
          <foreach item="status" collection="statusList" separator="," open="(" close=")">
            #{status}
          </foreach>
        </if>
      </where>
      ORDER BY command_create_time DESC
    </select>
</mapper>