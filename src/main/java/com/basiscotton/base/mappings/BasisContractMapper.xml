<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.basiscotton.base.mappers.BasisContractMapper">

    <select id="selectBasisContractByPage" resultType="com.basiscotton.manager.contract.vo.BasisContractResVo"
            parameterType ="com.basiscotton.manager.contract.vo.BasisContractParamVo">
        SELECT
            id,
            transaction_no,
            seller_trader_code,
            seller_trader_name,
            buyer_trader_code,
            buyer_trader_name,
            contract_status,
            delivery_type
        FROM
            trd_basis_contract t1
        <where>
            <if test ="paramVo.transactionNo != null and paramVo.transactionNo !=''">
                <bind name="transactionNo" value="'%'+ paramVo.transactionNo +'%'"/>
                and t1.transaction_no LIKE #{transactionNo}
            </if>
            <if test ="paramVo.sellerTraderCode != null and paramVo.sellerTraderCode !=''">
                <bind name="sellerTraderCode" value="'%'+ paramVo.sellerTraderCode +'%'"/>
                and t1.seller_trader_code LIKE #{sellerTraderCode}
            </if>
            <if test ="paramVo.sellerTraderName != null and paramVo.sellerTraderName !=''">
                <bind name="sellerTraderName" value="'%'+ paramVo.sellerTraderName +'%'"/>
                and t1.seller_trader_name LIKE #{sellerTraderName}
            </if>
            <if test ="paramVo.buyerTraderCode != null and paramVo.buyerTraderCode !=''">
                <bind name="buyerTraderCode" value="'%'+ paramVo.buyerTraderCode +'%'"/>
                and t1.buyer_trader_code LIKE #{buyerTraderCode}
            </if>
            <if test ="paramVo.buyerTraderName != null and paramVo.buyerTraderName !=''">
                <bind name="buyerTraderName" value="'%'+ paramVo.buyerTraderName +'%'"/>
                and t1.buyer_trader_name LIKE #{buyerTraderName}
            </if>
            <if test ="paramVo.contractStatus != null and paramVo.contractStatus !=''">
                <bind name="contractStatus" value="'%'+ paramVo.contractStatus +'%'"/>
                and t1.contract_status LIKE #{contractStatus}
            </if>
            <if test ="paramVo.deliveryType != null and paramVo.deliveryType !=''">
                <bind name="deliveryType" value="'%'+ paramVo.deliveryType +'%'"/>
                and t1.delivery_Type LIKE #{deliveryType}
            </if>
        </where>
        ORDER BY
            t1.update_time DESC
    </select>
</mapper>