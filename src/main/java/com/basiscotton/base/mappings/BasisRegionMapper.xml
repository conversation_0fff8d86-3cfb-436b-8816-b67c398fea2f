<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.basiscotton.base.mappers.BasisRegionMapper">

    <select id="selectRegionTreeList" resultType="com.basiscotton.common.region.vo.RegionTreeVo">
        SELECT
            ID,
            PARENT_ID,
            REGION_NAME,
            REGION_LEVEL,
            REGION_KEY,
            SEQ
        FROM
            trd_basis_region
        WHERE
            REGION_KEY LIKE #{parentKey}
        ORDER BY
            REGION_LEVEL ASC,SEQ ASC
    </select>
</mapper>