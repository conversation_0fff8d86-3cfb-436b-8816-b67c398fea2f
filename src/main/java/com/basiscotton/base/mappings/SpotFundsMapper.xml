<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.basiscotton.base.mappers.SpotFundsMapper">
  <resultMap id="BaseResultMap" type="com.basiscotton.base.entity.SpotFundsEntity">
    <!--@mbg.generated-->
    <!--@Table v_spot_funds-->
    <result column="CLIENT_CODE" jdbcType="VARCHAR" property="clientCode" />
    <result column="BALANCE" jdbcType="DECIMAL" property="balance" />
    <result column="AVAILABLE_BALANCE" jdbcType="DECIMAL" property="availableBalance" />
    <result column="FROZEN_FUNDS" jdbcType="DECIMAL" property="frozenFunds" />
    <result column="OTHER_FROZEN_FUNDS" jdbcType="DECIMAL" property="otherFrozenFunds" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    CLIENT_CODE, BALANCE, AVAILABLE_BALANCE, FROZEN_FUNDS, OTHER_FROZEN_FUNDS
  </sql>


  <select id="getSpotFundsByTraderCode" resultType="com.basiscotton.base.entity.SpotFundsEntity">
      SELECT
        CLIENT_CODE,
        BALANCE,
        AVAILABLE_BALANCE,
        FROZEN_FUNDS,
        (FROZEN_FUNDS - SPOT_FROZEN_FUNDS + OTHER_FROZEN_FUNDS) as OTHER_FROZEN_FUNDS,
        SPOT_FROZEN_FUNDS
      FROM
        v_spot_funds
      WHERE
        CLIENT_CODE = #{traderCode}
    </select>
</mapper>