<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.basiscotton.base.mappers.BasisOrderMapper">

    <select id="selectBasisOrderByPage" resultType="com.basiscotton.manager.order.vo.BasisOrderResVo"
            parameterType ="com.basiscotton.manager.order.vo.BasisOrderParamVo">
            SELECT
                id,
                contract_id,
                stock_id,
                stock_code,
                stock_base_info_id,
                quote_id,
                seller_custom_code,
                seller_custom_name,
                buyer_custom_code,
                buyer_custom_name,
                trade_price,
                trade_quantity,
                trade_date,
                seller_margin_standard,
                seller_margin_amount,
                seller_trade_fee_amount+seller_delivery_fee_amount AS seller_fee_amount,
                seller_trade_fee_standard+seller_delivery_fee_standard AS seller_fee_standard,
                basis_seller_margin_type,
                basis_seller_delivery_fee_type AS basis_seller_fee_type,
                buyer_margin_standard,
                buyer_margin_amount,
                buyer_trade_fee_amount+buyer_delivery_fee_amount AS buyer_fee_amount,
                buyer_trade_fee_standard+buyer_delivery_fee_standard AS buyer_fee_standard,
                basis_buyer_margin_type,
                basis_buyer_delivery_fee_type AS basis_buyer_fee_type,
                quote_type,
                pricing_party,
                future_code,
                fixed_price,
                CASE
                WHEN basis_price IS NOT NULL THEN basis_price
                WHEN basis_price_range IS NOT NULL THEN basis_price_range
                END basis_price,
                pricing_price,
                pricing_valid_time,
                whs_supervision_status,
                supervise_code,
                supervise_name,
                whs_pledge_channel
            FROM
                trd_basis_order t1
            <where>
                <if test ="paramVo.stockCode != null and paramVo.stockCode !=''">
                    <bind name="stockCode" value="'%'+ paramVo.stockCode +'%'"/>
                    and t1.stock_code LIKE #{stockCode}
                </if>
                <if test ="paramVo.sellerCustomCode != null and paramVo.sellerCustomCode !=''">
                    <bind name="sellerCustomCode" value="'%'+ paramVo.sellerCustomCode +'%'"/>
                    and t1.seller_custom_code LIKE #{sellerCustomCode}
                </if>
                <if test ="paramVo.sellerCustomName != null and paramVo.sellerCustomName !=''">
                    <bind name="sellerCustomName" value="'%'+ paramVo.sellerCustomName +'%'"/>
                    and t1.seller_custom_name LIKE #{sellerCustomName}
                </if>
                <if test ="paramVo.buyerCustomCode != null and paramVo.buyerCustomCode !=''">
                    <bind name="buyerCustomCode" value="'%'+ paramVo.buyerCustomCode +'%'"/>
                    and t1.buyer_custom_code LIKE #{buyerCustomCode}
                </if>
                <if test ="paramVo.buyerCustomName != null and paramVo.buyerCustomName !=''">
                    <bind name="buyerCustomName" value="'%'+ paramVo.buyerCustomName +'%'"/>
                    and t1.buyer_custom_name LIKE #{buyerCustomName}
                </if>
                <if test ="paramVo.startTradeDate != null and paramVo.startTradeDate !=''">
                    <bind name="startTime" value="paramVo.startTradeDate +' 00:00:00'"/>
                    and t1.trade_date  &gt;= #{startTime}
                </if>

                <if test="paramVo.endTradeDate != null and paramVo.endTradeDate != ''">
                    <bind name="endTime" value="paramVo.endTradeDate +' 23:59:59'"/>
                    and trade_date &lt;= #{endTime}
                </if>
             </where>
            ORDER BY
                t1.trade_date DESC,t1.stock_code DESC
    </select>
</mapper>