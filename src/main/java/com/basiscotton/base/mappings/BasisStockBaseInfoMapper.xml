<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.basiscotton.base.mappers.BasisStockBaseInfoMapper">


    <!-- 交易商端-查询交易商下仓单信息-->
    <select id="selectBasisWarehouseReceiptByTraderCode"
            resultType="com.basiscotton.manager.stock.vo.BasisWhsReceiptVo"
            parameterType="com.basiscotton.manager.stock.vo.BasisWhsReceiptParamVo">
        SELECT
            vwarehouse.CURRENT_TRADER_CODE AS traderCode,
            vwarehouse.CURRENT_TRADER_NAME AS traderName,
            vwarehouse.WAREHOUSE_RECEIPT_NUMBER AS warehouseReceiptNumber,
            vwarehouse.BATCH_NUMBER AS batchNumber,
            vwarehouse.STOCK_INT_QUANTITY AS quantity,
            vwarehouse.CONDITIONED_WEIGHT AS conditionedWeight,
            vwarehouse.MARK_WEIGHT AS markWeight,
            vwarehouse.STOCK_IN_WEIGHT AS weight,
            vwarehouse.COLOR_GRADE_CODE AS colorLevel,

            -- 长度/强力/长整/含杂/回潮
            -- vwarehouse.PRINCIPAL_PART_RANK AS lengthClass,
            -- vwarehouse.STRENGTH_SCALE AS strengthScale,
            -- vwarehouse.TIDY_AVG,
            -- vwarehouse.DIRTY,
            -- vwarehouse.MOISTURE_REGAIN AS moistureRegain,

            vwarehouse.MACRON AS macron,
            vwarehouse.PLACE_NAME as placeCode,
            vwarehouse.WHS_CODE AS wareHouseCode,
            vwarehouse.WHS_NAME AS wareHouseName,
            vwarehouse.PRODUCT_YEAR AS PRODUCT_YEAR,
            vwarehouse.WHS_SUPERVISION_STATUS AS TRD_SUPERVISION_STATUS,
            vwarehouse.LOAN_COMPANY_NAME AS loanCompanyName,

            vwarehouse.STORAGE_STATUS AS TRD_STORAGE_STATUS,
            vwarehouse.WHS_ID AS whsId,
            -- 马值 长度/强力/长整/含杂/回潮
            inspect.AVG_MKL AS avgMkl,
            inspect.AVG_LENGTH AS avgLength,
            inspect.AVG_BREAK_RATE AS breakValue,
            inspect.AVG_LEN_UNIFORMITY AS uniformityAverageValue,
            vwarehouse.DIRTY AS impurityRate,
            vwarehouse.MOISTURE_REGAIN AS moistureRate

        FROM
            v_xs_warehouse_info vwarehouse
        lEFT JOIN v_factory_batch_inspect inspect ON vwarehouse.BATCH_NUMBER = inspect.BATCH_CODE
        <where>
            vwarehouse.WAREHOUSE_RECEIPT_ENABLE_STATUS = 2
            AND vwarehouse.WAREHOUSE_RECEIPT_STATUS = 1 AND vwarehouse.STORAGE_STATUS = 1
            AND vwarehouse.WHS_TRANSACTION_STATUS = 1
            AND vwarehouse.QUANLITY_STATUS = 1
            AND (vwarehouse.BONDED_STATUS != 1 OR vwarehouse.BONDED_STATUS IS NULL)
            AND NOT EXISTS (
                                SELECT 1
                                FROM trd_basis_stock t
                                WHERE t.warehouse_receipt_no = vwarehouse.WAREHOUSE_RECEIPT_NUMBER
                                AND t.trade_status IN (1, 2)
                            )
            <!--已选择仓单号集合-->
            <if test='paramVo.whsNoList !=null and paramVo.whsNoList != "" '>
                AND vwarehouse.WAREHOUSE_RECEIPT_NUMBER NOT IN
                <foreach item="whsNo" collection="paramVo.whsNoList" index="index" separator="," open="(" close=")">
                    #{whsNo}
                </foreach>
            </if>
            <!--交易商代码-->
            <if test="paramVo.traderCode != null and paramVo.traderCode != ''">
                AND vwarehouse.CURRENT_TRADER_CODE = #{paramVo.traderCode}
            </if>
            <!--交易商名称-->
            <if test="paramVo.traderName != null and paramVo.traderName != ''">
                <bind name="traderName" value="'%'+paramVo.traderName+'%'"/>
                AND vwarehouse.CURRENT_TRADER_NAME LIKE #{traderName}
            </if>
            <!--仓单号-->
            <if test="paramVo.warehouseReceiptNumber != null and paramVo.warehouseReceiptNumber != ''">
                <bind name="warehouseReceiptNumber" value="'%'+paramVo.warehouseReceiptNumber+'%'"/>
                AND vwarehouse.WAREHOUSE_RECEIPT_NUMBER LIKE #{warehouseReceiptNumber}
            </if>
            <!--批号-->
            <if test='paramVo.batchNumberList !=null and paramVo.batchNumberList != "" '>
                AND
                <foreach collection="paramVo.batchNumberList" item="item" open="(" separator=" OR " close=")">
                    <bind name="itemStr" value="'%'+item+'%'"/>
                    vwarehouse.BATCH_NUMBER LIKE #{itemStr}
                </foreach>
            </if>
            <!--仓库名称-->
            <if test="paramVo.wareHouseName != null and paramVo.wareHouseName != ''">
                <bind name="wareHouseName" value="'%'+paramVo.wareHouseName+'%'"/>
                AND vwarehouse.WHS_NAME LIKE #{wareHouseName}
            </if>
        </where>
        ORDER BY vwarehouse.BATCH_NUMBER DESC
    </select>


    <select id="getBatchInfoVo" resultType="com.basiscotton.manager.stock.vo.BasisBatchInfoVo">
        SELECT
        info.CURRENT_TRADER_CODE as traderCode,
        info.CURRENT_TRADER_NAME as traderName,
        info.BATCH_NUMBER as BATCH_NO,
        info.WAREHOUSE_RECEIPT_NUMBER,
        info.CONDITIONED_WEIGHT,
        weight.GROSS_WEIGHT,
        info.MARK_WEIGHT,
        weight.PACKET_NUM,
        weight.REPOSITORY,
        info.WHS_PICK_MODE,
        weight.PLACE,
        info.PLACE_NAME,
        weight.PROCESS_COMPANY,
        info.FACTORY_CODE as PROCESS_CODE,
        inspect.AVG_LENGTH,
        inspect.AVG_BREAK_RATE,
        inspect.AVG_MKL,
        inspect.MKL_A,
        inspect.MKL_B1,
        inspect.MKL_B2,
        inspect.MKL_C1,
        inspect.MKL_C2,
        inspect.WHITE_COTTON_L1,
        inspect.WHITE_COTTON_L2,
        inspect.WHITE_COTTON_L3,
        inspect.WHITE_COTTON_L4,
        inspect.WHITE_COTTON_L5,
        inspect.WHITE_COTTON_L6,
        inspect.SPOT_COTTON_L1,
        inspect.SPOT_COTTON_L2,
        inspect.SPOT_COTTON_L3,
        inspect.SPOT_COTTON_L4,
        inspect.YELLOW_ISH_COTTON_L1,
        inspect.YELLOW_ISH_COTTON_L2,
        inspect.YELLOW_ISH_COTTON_L3,
        inspect.YELLOW_COTTON_L1,
        inspect.YELLOW_COTTON_L2,
        info.MOISTURE_REGAIN  AS MOISTURE_RATE,
        info.DIRTY  AS IMPURITY_RATE,
        inspect.AVG_LEN_UNIFORMITY,
        inspect.GINNING_QUALITY_RATE_P1,
        inspect.GINNING_QUALITY_RATE_P2,
        inspect.GINNING_QUALITY_RATE_P3,
        weight.INSPECT_DATE,
        info.WAREHOUSE_RECEIPT_STATUS,
        info.PRODUCT_YEAR,
        info.WHS_CODE,
        info.WHS_NAME,
        info.WHS_SUPERVISION_STATUS,
        info.WHS_PLEDGE_STATUS,
        info.WHS_PLEDGE_CHANNEL,
        info.LOAN_COMPANY_CODE,
        info.LOAN_COMPANY_NAME,
        info.STOCK_NAME,
        info.MARK_DESC,
        info.FACTORY_NAME,
        info.COLOR_GRADE_CODE,
        info.MOISTURE_REGAIN,
        info.DIRTY,
        info.TIDY_AVG,
        info.ROLLING_QUALITY_AVG,
        inspect.MAIN_LENGTH,
        inspect.MAIN_MKL,
        info.QUANTITY,
        inspect.MAX_MKL,
        inspect.MIN_MKL,
        inspect.AVG_RD,
        inspect.MIN_RD,
        inspect.MAX_RD,
        inspect.AVG_PLUS_B,
        inspect.MIN_PLUS_B,
        inspect.MAX_PLUS_B
        from
        v_xs_warehouse_info info
        LEFT JOIN v_factory_batch_weight weight ON info.BATCH_NUMBER = weight.BATCH_CODE
        lEFT JOIN v_factory_batch_inspect inspect ON info.BATCH_NUMBER = inspect.BATCH_CODE
        <where>
            info.WAREHOUSE_RECEIPT_NUMBER  IN
            <foreach item="ReceiptNo" collection="warehouseReceiptNumberList" index="index" separator="," open="(" close=")">
                #{ReceiptNo}
            </foreach>
        </where>
        ORDER BY info.WAREHOUSE_RECEIPT_NUMBER DESC
    </select>

    <select id="getBatchInfoVoByWhsNo" resultType="java.lang.Integer">
        SELECT
            COUNT(*)
        from
            v_xs_warehouse_info info
            LEFT JOIN v_factory_batch_weight weight ON info.BATCH_NUMBER = weight.BATCH_CODE
            lEFT JOIN v_factory_batch_inspect inspect ON info.BATCH_NUMBER = inspect.BATCH_CODE
        <where>
            info.WAREHOUSE_RECEIPT_STATUS = 1
            AND info.STORAGE_STATUS = 1
            AND info.WHS_TRANSACTION_STATUS = 1
            AND info.WAREHOUSE_RECEIPT_NUMBER = #{whsNo}
        </where>
    </select>

    <select id="selectBasisWarehouseReceipt" resultType="com.basiscotton.manager.resources.manage.vo.MBasisWhsReceiptVo">
        SELECT
        vwarehouse.CURRENT_TRADER_CODE AS traderCode,
        vwarehouse.CURRENT_TRADER_NAME AS traderName,
        vwarehouse.WAREHOUSE_RECEIPT_NUMBER AS warehouseReceiptNumber,
        vwarehouse.BATCH_NUMBER AS batchNumber,
        vwarehouse.STOCK_INT_QUANTITY AS quantity,
        vwarehouse.CONDITIONED_WEIGHT AS conditionedWeight,
        vwarehouse.MARK_WEIGHT AS markWeight,
        vwarehouse.STOCK_IN_WEIGHT AS weight,
        vwarehouse.COLOR_GRADE_CODE AS colorLevel,

        -- 长度/强力/长整/含杂/回潮
        vwarehouse.PRINCIPAL_PART_RANK AS lengthClass,
        vwarehouse.STRENGTH_SCALE AS strengthScale,
        vwarehouse.TIDY_AVG,
        vwarehouse.DIRTY,
        vwarehouse.MOISTURE_REGAIN AS moistureRegain,

        vwarehouse.MACRON AS macron,
        vwarehouse.PLACE_NAME as placeCode,
        vwarehouse.WHS_CODE AS wareHouseCode,
        vwarehouse.WHS_NAME AS wareHouseName,
        vwarehouse.PRODUCT_YEAR AS PRODUCT_YEAR,
        vwarehouse.WHS_SUPERVISION_STATUS AS TRD_SUPERVISION_STATUS,
        vwarehouse.LOAN_COMPANY_NAME AS loanCompanyName,

        vwarehouse.STORAGE_STATUS AS TRD_STORAGE_STATUS,
        vwarehouse.WHS_ID AS whsId

        FROM
        v_xs_warehouse_info vwarehouse
        <where>
            vwarehouse.WAREHOUSE_RECEIPT_ENABLE_STATUS = 2
            AND vwarehouse.WAREHOUSE_RECEIPT_STATUS = 1 AND vwarehouse.STORAGE_STATUS = 1
            AND vwarehouse.WHS_TRANSACTION_STATUS = 1
            AND (vwarehouse.BONDED_STATUS != 1 OR vwarehouse.BONDED_STATUS IS NULL)
            AND NOT EXISTS (
                                SELECT 1
                                FROM trd_basis_stock t
                                WHERE t.warehouse_receipt_no = vwarehouse.WAREHOUSE_RECEIPT_NUMBER
                                AND t.resource_audit_status IN (1, 2)
                            )
            <!--已选择仓单号集合-->
            <if test='paramVo.whsNoList !=null and paramVo.whsNoList != "" '>
                AND vwarehouse.WAREHOUSE_RECEIPT_NUMBER NOT IN
                <foreach item="whsNo" collection="paramVo.whsNoList" index="index" separator="," open="(" close=")">
                    #{whsNo}
                </foreach>
            </if>
            <!--交易商代码-->
            <if test="paramVo.traderCode != null and paramVo.traderCode != ''">
                <bind name="traderCode" value="'%'+paramVo.traderCode+'%'"/>
                AND vwarehouse.CURRENT_TRADER_CODE LIKE #{traderCode}
            </if>
            <!--交易商名称-->
            <if test="paramVo.traderName != null and paramVo.traderName != ''">
                <bind name="traderName" value="'%'+paramVo.traderName+'%'"/>
                AND vwarehouse.CURRENT_TRADER_NAME LIKE #{traderName}
            </if>
            <!--仓单号-->
            <if test="paramVo.warehouseReceiptNumber != null and paramVo.warehouseReceiptNumber != ''">
                <bind name="warehouseReceiptNumber" value="'%'+paramVo.warehouseReceiptNumber+'%'"/>
                AND vwarehouse.WAREHOUSE_RECEIPT_NUMBER LIKE #{warehouseReceiptNumber}
            </if>
            <!--批号-->
            <if test='paramVo.batchNumberList !=null and paramVo.batchNumberList != "" '>
                AND
                <foreach collection="paramVo.batchNumberList" item="item" open="(" separator=" OR " close=")">
                    <bind name="itemStr" value="'%'+item+'%'"/>
                    vwarehouse.BATCH_NUMBER LIKE #{itemStr}
                </foreach>
            </if>
            <!--仓库名称-->
            <if test="paramVo.wareHouseName != null and paramVo.wareHouseName != ''">
                <bind name="wareHouseNameStr" value="'%'+paramVo.wareHouseName+'%'"/>
                AND vwarehouse.WHS_NAME LIKE #{wareHouseNameStr}
            </if>
        </where>
        ORDER BY vwarehouse.BATCH_NUMBER DESC
    </select>

    <select id="getWhsReceiptVo" resultType="com.basiscotton.manager.stock.vo.BasisWhsReceiptVo">
        SELECT * FROM v_xs_warehouse_info where WAREHOUSE_RECEIPT_NUMBER = #{wareNo}
    </select>

    <select id="getBatchInspectVo" resultType="com.basiscotton.manager.hall.vo.BatchInspectVo">
        SELECT * FROM v_factory_batch_inspect where BATCH_CODE = #{batchNo} ORDER BY UPDATE_TIME DESC LIMIT 1
    </select>

    <select id="getBatchWeightVo" resultType="com.basiscotton.manager.hall.vo.BatchWeightVo">
        SELECT * FROM v_factory_batch_weight where BATCH_CODE = #{batchNo} ORDER BY UPDATE_TIME DESC LIMIT 1
    </select>

    <select id="getValidWhsReceiptVO" resultType="com.basiscotton.manager.stock.vo.BasisWhsReceiptVo">
        select *
        from v_xs_warehouse_info
        where WAREHOUSE_RECEIPT_NUMBER = #{warehouseReceiptNo}
          and WAREHOUSE_RECEIPT_ENABLE_STATUS = 2
          AND WAREHOUSE_RECEIPT_STATUS = 1
          AND STORAGE_STATUS = 1
          AND WHS_TRANSACTION_STATUS = 1
          AND (BONDED_STATUS != 1 OR BONDED_STATUS IS NULL)
    </select>
    <select id="getValidWhsReceiptNoList" resultType="java.lang.String">
        SELECT
            vwarehouse.WAREHOUSE_RECEIPT_NUMBER AS warehouseReceiptNumber
        FROM
            v_xs_warehouse_info vwarehouse
        <where>
            vwarehouse.WAREHOUSE_RECEIPT_ENABLE_STATUS = 2
            AND vwarehouse.WAREHOUSE_RECEIPT_STATUS = 1 AND vwarehouse.STORAGE_STATUS = 1
            AND vwarehouse.WHS_TRANSACTION_STATUS = 1
            AND vwarehouse.QUANLITY_STATUS = 1
            AND (vwarehouse.BONDED_STATUS != 1 OR vwarehouse.BONDED_STATUS IS NULL)
            AND NOT EXISTS (
                            SELECT 1
                            FROM trd_basis_stock t
                            WHERE t.warehouse_receipt_no = vwarehouse.WAREHOUSE_RECEIPT_NUMBER
                            AND t.trade_status IN (1, 2)
                            )
            AND vwarehouse.WAREHOUSE_RECEIPT_NUMBER  IN
            <foreach item="receiptNo" collection="warehouseReceiptNoList" index="index" separator="," open="(" close=")">
                #{receiptNo}
            </foreach>
        </where>
    </select>
</mapper>
