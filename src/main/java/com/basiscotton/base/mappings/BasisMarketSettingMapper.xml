<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.basiscotton.base.mappers.BasisMarketSettingMapper">
  <resultMap id="BaseResultMap" type="com.basiscotton.base.entity.MarketSettingEntity">
    <!--@mbg.generated-->
    <!--@Table trd_basis_market_setting-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="setting_name" jdbcType="VARCHAR" property="settingName" />
    <result column="basis_setting_type" jdbcType="VARCHAR" property="basisSettingType" />
    <result column="trade_start_time" jdbcType="TIME" property="tradeStartTime" />
    <result column="trade_end_time" jdbcType="TIME" property="tradeEndTime" />
    <result column="buyer_basis_margin_call_line" jdbcType="DECIMAL" property="buyerBasisMarginCallLine" />
    <result column="buyer_basis_margin_call_standard" jdbcType="DECIMAL" property="buyerBasisMarginCallStandard" />
    <result column="buyer_pricing_margin_call_line" jdbcType="DECIMAL" property="buyerPricingMarginCallLine" />
    <result column="buyer_pricing_margin_call_standard" jdbcType="DECIMAL" property="buyerPricingMarginCallStandard" />
    <result column="buyer_pricing_forced_liquidation_line" jdbcType="DECIMAL" property="buyerPricingForcedLiquidationLine" />
    <result column="seller_pricing_forced_liquidation_line" jdbcType="DECIMAL" property="sellerPricingForcedLiquidationLine" />
    <result column="warehouse_receipt_deposit_times" jdbcType="TINYINT" property="warehouseReceiptDepositTimes" />
    <result column="warehouse_receipt_deposit_onetime_amount" jdbcType="DECIMAL" property="warehouseReceiptDepositOnetimeAmount" />
    <result column="seller_basis_margin_type" jdbcType="TINYINT" property="sellerBasisMarginType" />
    <result column="seller_basis_margin_standard" jdbcType="DECIMAL" property="sellerBasisMarginStandard" />
    <result column="seller_trade_fee_type" jdbcType="TINYINT" property="sellerTradeFeeType" />
    <result column="seller_trade_fee_standard" jdbcType="DECIMAL" property="sellerTradeFeeStandard" />
    <result column="seller_delivery_fee_type" jdbcType="TINYINT" property="sellerDeliveryFeeType" />
    <result column="seller_delivery_fee_standard" jdbcType="DECIMAL" property="sellerDeliveryFeeStandard" />
    <result column="buyer_basis_margin_type" jdbcType="TINYINT" property="buyerBasisMarginType" />
    <result column="buyer_basis_margin_standard" jdbcType="DECIMAL" property="buyerBasisMarginStandard" />
    <result column="buyer_trade_fee_type" jdbcType="TINYINT" property="buyerTradeFeeType" />
    <result column="buyer_trade_fee_standard" jdbcType="DECIMAL" property="buyerTradeFeeStandard" />
    <result column="buyer_delivery_fee_type" jdbcType="TINYINT" property="buyerDeliveryFeeType" />
    <result column="buyer_delivery_fee_standard" jdbcType="DECIMAL" property="buyerDeliveryFeeStandard" />
    <result column="payment_seller_amount_ratio" jdbcType="DECIMAL" property="paymentSellerAmountRatio" />
    <result column="collection_buyer_amount_ratio" jdbcType="DECIMAL" property="collectionBuyerAmountRatio" />
    <result column="auto_pricing_time" jdbcType="INTEGER" property="autoPricingTime" />
    <result column="contract_generate_status" jdbcType="TINYINT" property="contractGenerateStatus" />
    <result column="audit_remark_status" jdbcType="TINYINT" property="auditRemarkStatus" />
    <result column="setting_status" jdbcType="VARCHAR" property="settingStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />

  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, setting_name, basis_setting_type, trade_start_time, trade_end_time
    buyer_basis_margin_call_line, buyer_basis_margin_call_standard, buyer_pricing_margin_call_line
    buyer_pricing_margin_call_standard, buyer_pricing_forced_liquidation_line, seller_pricing_forced_liquidation_line
    warehouse_receipt_deposit_times, warehouse_receipt_deposit_onetime_amount, seller_basis_margin_type
    seller_basis_margin_standard, seller_trade_fee_type, seller_trade_fee_standard
    seller_delivery_fee_type, seller_delivery_fee_standard, buyer_basis_margin_type
    buyer_basis_margin_standard, buyer_trade_fee_type, buyer_trade_fee_standard
    buyer_delivery_fee_type, buyer_delivery_fee_standard, payment_seller_amount_ratio
    collection_buyer_amount_ratio, auto_pricing_time, contract_generate_status
    audit_remark_status, setting_status, create_time, create_user_id, create_user_name
    update_time, update_user_id, update_user_id
  </sql>

  <select id="selectMonitorHoliday" resultType="com.basiscotton.common.tradetime.vo.MonitorHolidayVo">
      SELECT
          t1.HOLIDAY_DATE,
          t1.WORK_DAY
      FROM
          v_monitor_holidy t1
      WHERE
          t1.HOLIDAY_DATE>=CURDATE()
    </select>

  <select id="selectPriceIndex" resultType="com.basiscotton.riskControl.vo.PriceIndexVo">
      SELECT
          t1.COTTON_INDEX_NAME,
          t1.COTTON_INDEX_PRICE,
          t1.RELEASE_DATA
      FROM
          v_risk_price_index_info t1
    </select>
</mapper>