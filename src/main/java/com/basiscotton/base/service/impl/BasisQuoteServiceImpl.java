package com.basiscotton.base.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.BasisQuoteEntity;
import com.basiscotton.base.entity.BasisStockEntity;
import com.basiscotton.base.entity.MarketSettingEntity;
import com.basiscotton.base.enums.QuoteCancelType;
import com.basiscotton.base.enums.TradeMode;
import com.basiscotton.base.mappers.BasisQuoteMapper;
import com.basiscotton.base.service.BasisQuoteService;
import com.basiscotton.common.marketSeting.BasisMarketSettingService;
import com.basiscotton.common.marketSeting.SpotMarketSettingService;
import com.basiscotton.tradingcore.business.TradingStatusHelper;
import com.basiscotton.tradingcore.dto.BuyerCancelQuoteDTO;
import com.basiscotton.tradingcore.dto.BuyerChangeQuoteDTO;
import com.basiscotton.tradingcore.dto.SubmitQuoteDTO;
import com.sinosoft.dandelion.system.client.params.DataItem;
import lombok.RequiredArgsConstructor;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.basiscotton.base.BasisCottonConstants.quote_status_3;

@Service
@RequiredArgsConstructor
public class BasisQuoteServiceImpl extends ServiceImpl<BasisQuoteMapper, BasisQuoteEntity> implements BasisQuoteService {

    private final TradingStatusHelper tradingStatusHelper;

    public List<BasisQuoteEntity> getPendingQuoteByStockId(String stockId) {
        LambdaQueryWrapper<BasisQuoteEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BasisQuoteEntity::getStockId, stockId);
        queryWrapper.eq(BasisQuoteEntity::getQuoteStatus, BasisCottonConstants.quote_status_1);
        return list(queryWrapper);
    }

    @Override
    public List<BasisQuoteEntity> getAllQuoteByStockId(String stockId) {
        LambdaQueryWrapper<BasisQuoteEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BasisQuoteEntity::getStockId, stockId);
        queryWrapper.ne(BasisQuoteEntity::getQuoteStatus, BasisCottonConstants.quote_status_3);
        return list(queryWrapper);
    }

    @Override
    public Boolean hasBuyerPendingQuoteForStock(String stockId, String buyerCustomerCode) {
        LambdaQueryWrapper<BasisQuoteEntity> queryWrapper = Wrappers.lambdaQuery(BasisQuoteEntity.class);
        queryWrapper.eq(BasisQuoteEntity::getStockId, stockId);
        queryWrapper.eq(BasisQuoteEntity::getTraderCode, buyerCustomerCode);
        queryWrapper.eq(BasisQuoteEntity::getQuoteStatus, BasisCottonConstants.quote_status_1);
        int count = count(queryWrapper);
        return count > 0;
    }

    @Override
    public BasisQuoteEntity submitBuyerQuote(SubmitQuoteDTO submitQuoteDTO, BasisStockEntity stock, Boolean canQuoteMatch) {
        BasisQuoteEntity quote = createQuote(submitQuoteDTO, stock, canQuoteMatch);
        save(quote);
        return quote;
    }

    @Override
    public BasisQuoteEntity changeBuyerQuote(BuyerChangeQuoteDTO dto, Boolean canQuoteMatch) {
        BasisQuoteEntity updateQuote = new BasisQuoteEntity();
        updateQuote.setId(Long.parseLong(dto.getQuoteId()));
        updateQuote.setQuotePrice(dto.getQuotePrice());
        updateQuote.setQuoteTime(new Date());
        DataItem quoteStatus = Boolean.TRUE.equals(canQuoteMatch) ? tradingStatusHelper.getQuoteMatchedStatus()
                : tradingStatusHelper.getQuotePendingStatus();
        updateQuote.setQuoteStatus(quoteStatus);
        updateById(updateQuote);
        return getById(dto.getQuoteId());
    }

    @Override
    public BasisQuoteEntity updateQuoteMatch(Long quoteId) {
        BasisQuoteEntity update = new BasisQuoteEntity();
        update.setId(quoteId);
        update.setQuoteStatus(tradingStatusHelper.getQuoteMatchedStatus());
        updateById(update);
        return getById(quoteId);
    }

    @Override
    public BasisQuoteEntity cancelBuyerQuote(BuyerCancelQuoteDTO dto, QuoteCancelType quoteCancelType) {
        LambdaUpdateWrapper<BasisQuoteEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(BasisQuoteEntity::getId, dto.getQuoteId());
        updateWrapper.eq(BasisQuoteEntity::getTraderCode, dto.getBuyerCustomerCode());
        updateWrapper.set(BasisQuoteEntity::getQuoteStatus, quote_status_3);
        updateWrapper.set(BasisQuoteEntity::getQuoteCancelType, quoteCancelType.getValue());
        updateWrapper.set(BasisQuoteEntity::getUpdateTime, LocalDateTime.now());
        update(updateWrapper);
        return getById(dto.getQuoteId());
    }

    /**
     * 创建报价实体
     */
    private BasisQuoteEntity createQuote(SubmitQuoteDTO dto, BasisStockEntity stock, boolean canQuoteMatch) {
        BasisQuoteEntity quote = new BasisQuoteEntity();
        quote.setStockId(Long.parseLong(dto.getStockId()));
        quote.setStockCode(stock.getStockCode());
        quote.setStockBaseInfoId(stock.getStockBaseInfoId());
        quote.setUserName(dto.getBuyerCustomerName());
        quote.setTraderCode(dto.getBuyerCustomerCode());
        quote.setTraderName(dto.getBuyerCustomerName());
        quote.setQuotePrice(dto.getQuotePrice());
        quote.setQuoteWeight(stock.getStockWeight());
        quote.setQuoteTime(new Date());
        DataItem tradingStatus = canQuoteMatch
                ? tradingStatusHelper.getQuoteMatchedStatus()
                : tradingStatusHelper.getQuotePendingStatus();
        quote.setQuoteStatus(tradingStatus);


        MarketSettingEntity settingEntity = getMarketSettingEntity(dto);
        quote.setBuyerBasisMarginStandard(settingEntity.getBuyerBasisMarginStandard());
        quote.setBuyerBasisMarginType(settingEntity.getBuyerBasisMarginType());
        quote.setBuyerBasisMarginAmount(quote.getBuyerBasisMarginStandard().multiply(quote.getQuoteWeight()));
        quote.setBuyerTradeFeeType(settingEntity.getBuyerTradeFeeType());
        quote.setBuyerTradeFeeStandard(settingEntity.getBuyerTradeFeeStandard());
        quote.setBuyerTradeFeeAmount(quote.getQuoteWeight().multiply(settingEntity.getBuyerTradeFeeStandard()));
        quote.setBuyerDeliveryFeeStandard(settingEntity.getBuyerDeliveryFeeStandard());
        quote.setBuyerDeliveryFeeType(settingEntity.getBuyerDeliveryFeeType());
        quote.setBuyerDeliveryFeeAmount(quote.getQuoteWeight().multiply(settingEntity.getBuyerDeliveryFeeStandard()));
        return quote;
    }

    private MarketSettingEntity getMarketSettingEntity(SubmitQuoteDTO dto) {
        TradeMode tradeMode = dto.getTradeMode();
        MarketSettingEntity settingEntity;
        if (tradeMode == TradeMode.SPOT_LISTING) {
            settingEntity = SpotMarketSettingService.settingEntity;
        } else if (tradeMode == TradeMode.BASIS_PRICING) {
            settingEntity = BasisMarketSettingService.settingEntity;
        } else {
            throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, "未知的交易模式: " + tradeMode);
        }

        if (settingEntity == null) {
            throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, "行情设置不存在");
        }
        return settingEntity;
    }

    @Override
    public List<BasisQuoteEntity> disposeBuyerQuote(@Nonnull String stockId, @Nullable Long excludeQuoteId,
                                                    @Nonnull QuoteCancelType quoteCancelType) {
        Objects.requireNonNull(stockId, "处理失败报价, 商品 ID 不能为空");
        Objects.requireNonNull(quoteCancelType, "处理失败报价, 报价撤销类型不能为空");

        List<BasisQuoteEntity> pendingQuoteByStockId = getPendingQuoteByStockId(stockId);

        List<BasisQuoteEntity> updateList = pendingQuoteByStockId.stream()
                .filter(q -> !Objects.equals(q.getId(), excludeQuoteId))
                .collect(Collectors.toList());

        // 如果列表为空，直接返回
        if (updateList.isEmpty()) {
            return updateList;
        }

        List<Long> updateIds = updateList.stream().map(BasisQuoteEntity::getId).collect(Collectors.toList());

        LambdaUpdateWrapper<BasisQuoteEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(BasisQuoteEntity::getId, updateIds);
        updateWrapper.set(BasisQuoteEntity::getQuoteStatus, tradingStatusHelper.getQuoteCancelStatus());
        updateWrapper.set(BasisQuoteEntity::getQuoteCancelType, quoteCancelType.getValue());
        update(updateWrapper);
        return updateList;
    }
}
