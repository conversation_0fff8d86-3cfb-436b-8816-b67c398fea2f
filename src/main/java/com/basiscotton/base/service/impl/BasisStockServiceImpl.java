package com.basiscotton.base.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.basiscotton.base.entity.BasisStockEntity;
import com.basiscotton.base.mappers.BasisStockBaseInfoMapper;
import com.basiscotton.base.mappers.BasisStockMapper;
import com.basiscotton.base.service.BasisStockService;
import com.basiscotton.manager.stock.vo.BasisWhsReceiptVo;
import com.basiscotton.tradingcore.business.TradingStatusHelper;
import com.basiscotton.tradingcore.dto.ChangeStockPriceDTO;
import com.basiscotton.tradingcore.dto.QuoteDTO;
import com.basiscotton.tradingcore.dto.WithdrawStockDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import static com.basiscotton.base.BasisCottonConstants.trade_status_2;
import static com.basiscotton.base.BasisCottonConstants.trade_status_4;

@Service
@RequiredArgsConstructor
@Slf4j
public class BasisStockServiceImpl extends ServiceImpl<BasisStockMapper, BasisStockEntity> implements BasisStockService {

    private final TradingStatusHelper tradingStatusHelper;

    private final BasisStockBaseInfoMapper basisStockBaseInfoMapper;

    @Override
    public void updateStockMatched(String stockId, QuoteDTO dto) {
        LambdaUpdateWrapper<BasisStockEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(BasisStockEntity::getId, stockId);
        // 报价有效期不为空时更新
        updateWrapper.set(Objects.nonNull(dto.getStockValidityEnd()), BasisStockEntity::getStockValidityEnd, dto.getStockValidityEnd());
        updateWrapper.set(BasisStockEntity::getTradeStatus, tradingStatusHelper.getStockMatchedStatus());
        updateWrapper.set(BasisStockEntity::getUpdateTime, LocalDateTime.now());
        update(updateWrapper);
    }

    @Override
    public BasisStockEntity updateStockPriceAndValidityEnd(ChangeStockPriceDTO dto) {
        LambdaUpdateWrapper<BasisStockEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(BasisStockEntity::getId, dto.getStockId());
        // 报价有效期不为空时更新
        updateWrapper.set(BasisStockEntity::getStockPrice, dto.getStockPrice());
        updateWrapper.set(BasisStockEntity::getStockValidityEnd, dto.getValidityEnd());
        update(updateWrapper);
        return getById(dto.getStockId());
    }

    @Override
    public void updateStockWithdraw(WithdrawStockDTO dto) {
        LambdaUpdateWrapper<BasisStockEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(BasisStockEntity::getId, dto.getStockId());
        updateWrapper.set(BasisStockEntity::getUpdateTime, LocalDateTime.now());
        updateWrapper.set(BasisStockEntity::getUpdateUserId, dto.getOperatorId());
        updateWrapper.set(BasisStockEntity::getUpdateUserName, dto.getOperatorName());
        updateWrapper.set(BasisStockEntity::getTradeStatus, trade_status_4);
        updateWrapper.set(BasisStockEntity::getDelistingReason, dto.getCancelType().getValue());
        update(updateWrapper);
    }

    @Override
    public BasisStockEntity getStockAndCheckTradingStatus(String stockId) {
        BasisStockEntity stock = getById(stockId);
        if (stock == null) {
            log.warn("商品不存在, 商品 ID：{}", stockId);
            throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, "报价商品不存在");
        }
        // 验证商品状态是否可交易
        tradingStatusHelper.validateStockTradable(stock.getResourceAuditStatus(), stock.getTradeStatus());
        // 验证基差价格
        tradingStatusHelper.validatePrice(stock.getStockPrice(), "商品基差");

        return stock;
    }

    @Override
    public Boolean isStockValid(BasisStockEntity stock) {
        // 按照有效条件查询商品基础信息, 如果基础信息不为空则认为商品有效
        BasisWhsReceiptVo validWhsReceiptVO = basisStockBaseInfoMapper.getValidWhsReceiptVO(stock.getWarehouseReceiptNo());
        return validWhsReceiptVO != null;
    }

    @Override
    public List<BasisStockEntity> getAllListingStock() {
        LambdaQueryWrapper<BasisStockEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(BasisStockEntity::getId);
        queryWrapper.eq(BasisStockEntity::getTradeStatus, trade_status_2);
        return list(queryWrapper);
    }
}
