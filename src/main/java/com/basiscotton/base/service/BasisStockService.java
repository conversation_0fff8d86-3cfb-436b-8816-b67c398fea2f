package com.basiscotton.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.basiscotton.base.entity.BasisStockEntity;
import com.basiscotton.tradingcore.dto.ChangeStockPriceDTO;
import com.basiscotton.tradingcore.dto.QuoteDTO;
import com.basiscotton.tradingcore.dto.WithdrawStockDTO;

import java.util.List;

public interface BasisStockService extends IService<BasisStockEntity> {

    /**
     * 买家报价、买家修改报价、卖家修改报价后更新商品最新报价, 如果报价可撮合则更新商品交易状态
     *
     * @param stockId        商品ID
     * @param dto 报价信息
     */
    void updateStockMatched(String stockId, QuoteDTO dto);

    /**
     * 卖方更新商品价格和有效期
     *
     * @param dto 更新商品信息
     */
    BasisStockEntity updateStockPriceAndValidityEnd(ChangeStockPriceDTO dto);

    /**
     * 卖家撤回商品
     * @param dto 撤回商品信息
     */
    void updateStockWithdraw(WithdrawStockDTO dto);

    /**
     * 获取商品信息
     * @param stockId 商品ID
     * @return 商品信息
     */
    BasisStockEntity getStockAndCheckTradingStatus(String stockId);

    /**
     * 校验商品关联仓单是否有效
     *
     * @param stock 商品信息
     * @return true-有效，false-无效
     */
    Boolean isStockValid(BasisStockEntity stock);

    /**
     * 获取所有上市商品
     */
    List<BasisStockEntity> getAllListingStock();
}
