package com.basiscotton.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.basiscotton.base.entity.BasisQuoteEntity;
import com.basiscotton.base.entity.BasisStockEntity;
import com.basiscotton.base.enums.QuoteCancelType;
import com.basiscotton.tradingcore.dto.BuyerCancelQuoteDTO;
import com.basiscotton.tradingcore.dto.BuyerChangeQuoteDTO;
import com.basiscotton.tradingcore.dto.SubmitQuoteDTO;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.List;

public interface BasisQuoteService extends IService<BasisQuoteEntity> {

    /**
     * 根据商品id查询所有未成交的报价
     *
     * @param stockId 商品id
     * @return 列表
     */
    List<BasisQuoteEntity> getPendingQuoteByStockId(String stockId);

    /**
     * 根据商品id查询所有未成交的报价
     *
     * @param stockId 商品id
     * @return 列表
     */
    List<BasisQuoteEntity> getAllQuoteByStockId(String stockId);

    /**
     * 判断买家是否存在未成交的报价（用于判断是否需要冻结保证金）
     *
     * @param stockId           商品 ID
     * @param buyerCustomerCode 买家客户代码
     * @return true-存在未成交的报价，false-不存在未成交的报价
     */
    Boolean hasBuyerPendingQuoteForStock(String stockId, String buyerCustomerCode);

    /**
     * 提交买家报价
     *
     * @param submitQuoteDTO 报价参数
     * @param stock          商品信息
     * @param canQuoteMatch  是否允许报价成交
     */
    BasisQuoteEntity submitBuyerQuote(SubmitQuoteDTO submitQuoteDTO, BasisStockEntity stock, Boolean canQuoteMatch);

    /**
     * 修改买家报价
     *
     * @param buyerChangeQuoteDTO 报价信息
     * @param canQuoteMatch       报价能否撮合
     */
    BasisQuoteEntity changeBuyerQuote(BuyerChangeQuoteDTO buyerChangeQuoteDTO, Boolean canQuoteMatch);

    /**
     * 报价更新撮合状态
     *
     * @param quoteId 报价ID
     */
    BasisQuoteEntity updateQuoteMatch(Long quoteId);

    /**
     * 取消买家报价
     *
     * @param buyerCancelQuoteDTO 取消报价参数
     * @param quoteCancelType     取消报价方式
     */
    BasisQuoteEntity cancelBuyerQuote(BuyerCancelQuoteDTO buyerCancelQuoteDTO, QuoteCancelType quoteCancelType);

    /**
     * 处置买家报价, 其它买家成交、卖家撤销商品、系统撤销商品
     *
     * @param stockId        商品 ID
     * @param excludeQuoteId 排除的报价 ID, 为空时处理商品下所有未成交的报价
     */
    List<BasisQuoteEntity> disposeBuyerQuote(@Nonnull String stockId, @Nullable Long excludeQuoteId, @Nonnull QuoteCancelType quoteCancelType);
}
