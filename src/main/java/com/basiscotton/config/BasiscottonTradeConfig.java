package com.basiscotton.config;

import com.dtflys.forest.springboot.annotation.ForestScan;
import org.harry.dandelion.framework.cache.ICache;
import org.harry.dandelion.framework.cache.redis.RedisCacheProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

@Configuration
@ComponentScan({ "org.harry", "com.sinosoft","com.cottoneasy","com.basiscotton","com.applet"})
@ForestScan(basePackages = {"com.sinosoft.dandelion.fms.proxy","com.cottoneasy.storage.trade.client","com.sinosoft.cnce.sc.api.proxy","com.cottoneasy.trade.client","com.cottoneasy.finance.api","com.stockcotton.trade.basis.api.navigationDisplay.proxy"})
public class BasiscottonTradeConfig {

    @Bean("basisTradeBusinessLock")
    public ICache buildOnlineUserCache(RedisCacheProvider provider) {
        return provider.buildCache("basisTradeBusinessLock", null);
    }
}