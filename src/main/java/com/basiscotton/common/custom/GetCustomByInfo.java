package com.basiscotton.common.custom;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.common.apiservice.IBasisTradeUCService;
import com.sinosoft.dandelion.system.client.model.Custom;
import org.harry.dandelion.framework.core.params.PageParameter;
import org.harry.dandelion.framework.core.params.Pagination;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.harry.dandelion.framework.rep.support.page.PipPagination;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * @Title: getCustomByInfo.java
 * @Description: 根据交易商代码查询交易商信息
 * <AUTHOR>
 * @date 2021年12月29日
 * @version V1.0
 */
@Service("consultTradingManage.getCustomByInfo.1")
@ApiRequestObject(value = "查询交易商信息", name = "getCustomByInfo",  groups= {"仓单管理"},params= {
		@ApiParamMeta(key = "code", desc = "交易商代码", type = String.class),
		@ApiParamMeta(key = "name", desc = "交易商名称", type = String.class),
	})
@ApiResponseObject(params = {
		@ApiParamMeta(key = "pagination", desc = "交易商信息", type = Custom.class,pagination = true)
})
public class GetCustomByInfo implements IBusinessService {

	@Resource
	private IBasisTradeUCService iBasisTradeUCService;
    @Override
    public void doVerify(ServiceHandlerContext context){

	}

    @Override
    public void doWork(ServiceHandlerContext context){

		//获取查询条件
		String code = context.getValueObject(String.class, "code");
		String name = context.getValueObject(String.class, "name");
		PageParameter pageParameter=new PageParameter();
		pageParameter.setPageNum(1);
		pageParameter.setPageSize(10);
		QueryWrapper<Custom> queryWrapper = new QueryWrapper<Custom>();
		//构造查询条件
		if(StringUtil.isNotEmpty(code)) {
			queryWrapper.and(i ->i.like("CODE", code).or().like("NAME", code));
		}
		if(StringUtil.isNotEmpty(name)) {
			queryWrapper.and(i ->i.like("CODE", name).or().like("NAME", name));
		}
		List<String> customTypeList = new ArrayList<String>();
		customTypeList.add(BasisCottonConstants.custom_type_1);
		customTypeList.add(BasisCottonConstants.custom_type_9);
		customTypeList.add(BasisCottonConstants.custom_type_10);
		customTypeList.add(BasisCottonConstants.custom_type_11);
		queryWrapper.in("TYPE", customTypeList);
		queryWrapper.orderByAsc("CODE");
		Pagination<Custom> pagination=new PipPagination<Custom>(pageParameter);
		pagination=iBasisTradeUCService.getCustomsByCodeOrName(queryWrapper, pageParameter);
		// 创建响应实体
		this.createSuccessResponse(context);
		context.getResponseBody().setData("pagination", pagination);

    }

}
