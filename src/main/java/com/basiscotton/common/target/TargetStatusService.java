package com.basiscotton.common.target;

import org.springframework.stereotype.Service;

/**
 * @Title: TargetStatusService.java
 * @Description: 仓单、标的状态验证公共服务
 * <AUTHOR>
 * @date 2025/8/20
 * @version V1.0
 */
@Service
public class TargetStatusService {

	/*
	 * @Description: 仓单状态判断
	 * @param:
	 * @param: null
	 * @return:
	 **/
	public boolean verifyWarehouseReceiptNoStatus(String whsNo){
		return true;
	}

	/*
	 * @Description: 标的状态判断
	 * @param:
	 * @param: null
	 * @return:
	 **/
	public boolean verifyTargetStatus(Long contractId, Long targetId){
		return true;
	}

	/*
	 * @Description: 验证点价时间
	 * @param:
	 * @param: null
	 * @return:
	 **/
	public boolean verifyPricingTime(Long contractId, Long targetId){
		return true;
	}
	
}
