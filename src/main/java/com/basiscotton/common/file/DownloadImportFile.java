package com.basiscotton.common.file;

import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.document.actions.export.pdf.ExportPdfUtils;
import org.springframework.stereotype.Service;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

/**
 * 
 * @Title: DownloadImportFile.java
 * @Description: 下载导入模板
 * <AUTHOR>
 * @date 2021年11月29日
 * @version V1.0
 */
@Service("admin.downloadImportFile.1")
@ApiRequestObject(value = "下载导入模板", name = "downloadImportFile", groups = {"协商交易-公共接口"}, params = {
        @ApiParamMeta(key = "templateName", desc = "模板名称", type = String.class)
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "filePath", desc = "预览地址", type = String.class)
})
public class DownloadImportFile implements IBusinessService {

    @Override
    public void doVerify(ServiceHandlerContext context) {
		//获取参数
		String templateName = context.getValueObject(String.class, "templateName");
		if (templateName == null) {
			throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "参数不能为空");
		}
    }

    @Override
    public void doWork(ServiceHandlerContext context) {
    	//获取参数
    	String templateName = context.getValueObject(String.class, "templateName");
		HttpServletResponse response = context.getHttpServletResponse();
		String FilePath = ExportPdfUtils.getInstance().getClassPathStaticFile("static/import/"+templateName+".xlsx").getAbsolutePath();
		ServletOutputStream out = null;
		FileInputStream in = null;
		try {
			in = new FileInputStream(new File(FilePath));
			response.setContentType("application/force-download;charset=UTF-8");
			response.setCharacterEncoding("utf-8");
			response.setHeader("Content-disposition", "attachment;filename=" + templateName);
			out = response.getOutputStream();
			// 读取文件流
			int len = 0;
			byte[] buffer = new byte[1024 * 10];
			while ((len = in.read(buffer)) != -1) {
				out.write(buffer, 0, len);
			}
			out.flush();
		} catch (Exception e) {
			throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, "文件路径错误！");
		} finally {
			try {
				response.flushBuffer();
			} catch (IOException e) {
				throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, e.toString());
			}
			try {
				out.close();
				in.close();
			} catch (Exception e1) {
				throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, e1.toString());
			} 
		}
	}
}
