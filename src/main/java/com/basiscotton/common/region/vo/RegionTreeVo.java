package com.basiscotton.common.region.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.basiscotton.manager.hall.vo.InspectDataReqVo;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @Title: RegionTreeVo.java
 * @Description: 区域结构数据
 * <AUTHOR>
 * @date 2025/5/30
 * @version V1.0
 */
@Data
public class RegionTreeVo extends InspectDataReqVo {

	@JSONField(serializeUsing = ToStringSerializer.class)
	private String id;

	@JSONField(serializeUsing = ToStringSerializer.class)
	private String parentId;

	private String regionName;

	private Integer regionLevel;

	private String regionKey;

	private Integer seq;

	private List<RegionTreeVo> children = new ArrayList<RegionTreeVo>();
}

