package com.basiscotton.common.region.service;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.basiscotton.base.entity.BasisRegionEntity;
import com.basiscotton.base.mappers.BasisRegionMapper;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Title: GetRegionList.java
 * @Description: 查询基差区域数据
 * <AUTHOR>
 * @date 2025/5/29
 * @version V1.0
 */
@Service("basisCommon.getRegionList.1")
@ApiRequestObject(value = "查询基差区域数据", name = "getRegionList", groups = {"交易商端-基差交易-公共服务"}, params = {
        @ApiParamMeta(key = "parentId", desc = "主键", type = String.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "regionList", desc = "查询分页列表", type = BasisRegionEntity.class,pagination = true),
})
public class GetRegionList implements IBusinessService {

    @Resource
    private BasisRegionMapper basisRegionMapper;


    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        String parentId = context.getStringValue("parentId");
        LambdaQueryWrapper<BasisRegionEntity> queryWrapper = Wrappers.lambdaQuery(BasisRegionEntity.class);
        queryWrapper.eq(BasisRegionEntity::getParentId,parentId);
        queryWrapper.orderByAsc(BasisRegionEntity::getSeq);
        List<BasisRegionEntity> regionList = basisRegionMapper.selectList(queryWrapper);
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("regionList", regionList);
    }
}




