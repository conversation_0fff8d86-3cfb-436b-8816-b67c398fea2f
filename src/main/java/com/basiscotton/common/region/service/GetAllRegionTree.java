package com.basiscotton.common.region.service;


import com.basiscotton.base.mappers.BasisRegionMapper;
import com.basiscotton.common.region.vo.RegionTreeVo;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Title: GetAllRegion.java
 * @Description: 查询基差区域全部数据
 * <AUTHOR>
 * @date 2025/5/29
 * @version V1.0
 */
@Service("basisCommon.getAllRegionTree.1")
@ApiRequestObject(value = "查询基差区域数据", name = "getAllRegionTree", groups = {"交易商端-基差交易-公共服务"}, params = {
        @ApiParamMeta(key = "parentKey", desc = "主键", type = String.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "regionTreeVoList", desc = "查询分页列表", type = RegionTreeVo.class,pagination = true),
})
public class GetAllRegionTree implements IBusinessService {

    @Resource
    private BasisRegionMapper basisRegionMapper;


    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        String parentKey = context.getStringValue("parentKey");
        List<RegionTreeVo> regionList = basisRegionMapper.selectRegionTreeList("%"+parentKey+"%");

        // 将所有节点放入 map，便于查找
        Map<String, RegionTreeVo> regionMap = new HashMap<>();
        for (RegionTreeVo vo : regionList) {
            regionMap.put(vo.getId(), vo);
        }

        List<RegionTreeVo> regionTreeVoList = new ArrayList<>();

        for (RegionTreeVo vo : regionList) {
            if (vo.getParentId() == null || "100".equals(vo.getParentId() )) {
                // 如果是根节点
                regionTreeVoList.add(vo);
            } else {
                // 否则找到父节点并加入其 children 中
                RegionTreeVo parent = regionMap.get(vo.getParentId());
                if (parent != null) {
                    parent.getChildren().add(vo);
                }
            }
        }

        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("regionTreeVoList", regionTreeVoList);
    }
}




