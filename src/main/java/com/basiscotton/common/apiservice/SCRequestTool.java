package com.basiscotton.common.apiservice;

import com.basiscotton.base.enums.TradeMode;
import com.sinosoft.cnce.sc.api.service.TXCodeService;
import com.sinosoft.cnce.sc.base.constant.SCAPICodeConstants;
import com.sinosoft.cnce.sc.base.enumeration.BusinessModuleEnum;
import com.sinosoft.cnce.sc.base.enumeration.FinancialEntityAccountTypeEnum;
import com.sinosoft.cnce.sc.base.vo.SCRequestVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.Objects;

/**
 * 结算通用工具类
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class SCRequestTool {

    private final TXCodeService txCodeService;

    /**
     * 构建查询账户余额
     *
     * @return
     */
    public static SCRequestVO buildQueryAccountCapital() {
        return SCRequestTool.buildQueryRequest(SCAPICodeConstants.CUSTOM_BALANCE_SC1001, FinancialEntityAccountTypeEnum.FINANCIAL_ENTITY_ACCOUNT_TYPE_2.getCode());
    }

    /**
     * 构建查询保证金余额
     *
     * @return
     */
    public static SCRequestVO buildQueryMarginAccountCapital() {
        return SCRequestTool.buildQueryRequest(SCAPICodeConstants.CUSTOM_BALANCE_SC1001, FinancialEntityAccountTypeEnum.FINANCIAL_ENTITY_ACCOUNT_TYPE_1.getCode());
    }
//
//	/**
//	 * 构建查询账户余额
//	 * @return
//	 */
//	public static SCRequestVO buildQueryAccountCapital() {
//		return SCRequestTool.buildQueryRequest(SCAPICodeConstants.CUSTOM_BALANCE_SC1001,FinancialEntityAccountTypeEnum.FINANCIAL_ENTITY_ACCOUNT_TYPE_2.getCode());
//	}
//

    /**
     * 构建冻结资金
     *
     * @return
     */
    public static SCRequestVO buildFreezeCapital() {
        return SCRequestTool.buildQueryRequest(SCAPICodeConstants.FREEZE_SC2001, FinancialEntityAccountTypeEnum.FINANCIAL_ENTITY_ACCOUNT_TYPE_1.getCode());
    }
//
//	/**
//	 * 扣除手续费
//	 * @return
//	 */
//	public static SCRequestVO buildDeductionCharge() {
//		return SCRequestTool.buildQueryRequest(SCAPICodeConstants.TRANSACTION_CHARGE_T141,FinancialEntityAccountTypeEnum.FINANCIAL_ENTITY_ACCOUNT_TYPE_2.getCode());
//	}
//
//	/**
//	 * 构建退还手续费vo
//	 * @return
//	 */
//	public static SCRequestVO buildBackChargeAsType1() {
//		return SCRequestTool.buildQueryRequest(SCAPICodeConstants.HEDGE_C602,FinancialEntityAccountTypeEnum.FINANCIAL_ENTITY_ACCOUNT_TYPE_1.getCode());
//	}
//
//	public static SCRequestVO buildBackChargeAsType2() {
//		return SCRequestTool.buildQueryRequest(SCAPICodeConstants.HEDGE_C602,FinancialEntityAccountTypeEnum.FINANCIAL_ENTITY_ACCOUNT_TYPE_2.getCode());
//	}
//
//	/**
//	 * 收买方货款
//	 * @return
//	 */
//	public static SCRequestVO buildTwoCollectBuyerAmount() {
//		return SCRequestTool.buildQueryRequest(SCAPICodeConstants.BUYER_PAYMENT_T211,FinancialEntityAccountTypeEnum.FINANCIAL_ENTITY_ACCOUNT_TYPE_2.getCode());
//	}
//
//	/**
//	 * 两方合同付首付款
//	 * @return
//	 */
//	public static SCRequestVO buildTwoFirstPayCapital() {
//		return SCRequestTool.buildQueryRequest(SCAPICodeConstants.FIRST_PAYMENT_SELLER_T212,FinancialEntityAccountTypeEnum.FINANCIAL_ENTITY_ACCOUNT_TYPE_2.getCode());
//	}
//
//	/**
//	 * 两方合同付非首付款
//	 * @return
//	 */
//	public static SCRequestVO buildTwoNoFirstPayCapital() {
//		return SCRequestTool.buildQueryRequest(SCAPICodeConstants.BALANCE_PAYMENT_SELLER_T214,FinancialEntityAccountTypeEnum.FINANCIAL_ENTITY_ACCOUNT_TYPE_2.getCode());
//	}
//
//	/**
//	 * 三方合同收买方货款
//	 * @return
//	 */
//	public static SCRequestVO buildThreeCollectBuyerAmount() {
//		return SCRequestTool.buildQueryRequest(SCAPICodeConstants.TRANSACTION_T221,FinancialEntityAccountTypeEnum.FINANCIAL_ENTITY_ACCOUNT_TYPE_2.getCode());
//	}
//
//	/**
//	 * 三方合同付首付款
//	 * @return
//	 */
//	public static SCRequestVO buildThreeFirstPayCapital() {
//		return SCRequestTool.buildQueryRequest(SCAPICodeConstants.TRIANGULAR_CONTRACT_FIRST_PAYMENT_T222,FinancialEntityAccountTypeEnum.FINANCIAL_ENTITY_ACCOUNT_TYPE_2.getCode());
//	}
//
//	/**
//	 * 三方合同付非首付款
//	 * @return
//	 */
//	public static SCRequestVO buildThreeNoFirstPayCapital() {
//		return SCRequestTool.buildQueryRequest(SCAPICodeConstants.TRIANGULAR_BALANCE_PAYMENT_T223,FinancialEntityAccountTypeEnum.FINANCIAL_ENTITY_ACCOUNT_TYPE_2.getCode());
//	}
//
//	/**
//	 * 三方合同付中间方货款
//	 * @return
//	 */
//	public static SCRequestVO buildThreePayMiddleCapital() {
//		return SCRequestTool.buildQueryRequest(SCAPICodeConstants.TRIANGULAR_MIDDLE_BALANCE_PAYMENT_T224,FinancialEntityAccountTypeEnum.FINANCIAL_ENTITY_ACCOUNT_TYPE_2.getCode());
//	}
//
//	/**
//	 * 构建退还买方资金
//	 * @return
//	 */
//	public static SCRequestVO buildBackBuyerAmountCapital() {
//		return SCRequestTool.buildQueryRequest(SCAPICodeConstants.PAYABLE_SC2004,FinancialEntityAccountTypeEnum.FINANCIAL_ENTITY_ACCOUNT_TYPE_2.getCode());
//	}
//
//	/**
//	 * 构建增加买方资金
//	 * @return
//	 */
//	public static SCRequestVO buildAddBuyerAmountCapital() {
//		return SCRequestTool.buildQueryRequest(SCAPICodeConstants.PAYABLE_SC2004,FinancialEntityAccountTypeEnum.FINANCIAL_ENTITY_ACCOUNT_TYPE_2.getCode());
//	}
//
//	/**
//	 * 构建开票申请
//	 * @return
//	 */
//	public static SCRequestVO buildCapitalInvoice() {
//		return SCRequestTool.buildQueryRequest(SCAPICodeConstants.INVOICE_APPLY_SC5008,FinancialEntityAccountTypeEnum.FINANCIAL_ENTITY_ACCOUNT_TYPE_2.getCode());
//	}
//
//	/**
//	 * 正常质押未融资
//	 * @return
//	 */
//	public static SCRequestVO buildTwoNoFirstPayPledgeAndFinancingCapital() {
//		return SCRequestTool.buildQueryRequest(SCAPICodeConstants.LOAN_REPAYMENT_PLEDGE_T213,FinancialEntityAccountTypeEnum.FINANCIAL_ENTITY_ACCOUNT_TYPE_2.getCode());
//	}
//
//	/**
//	 * 子公司质押已融资
//	 * @return
//	 */
//	public static SCRequestVO buildTwoNoFirstPayOrderFinancingCapital() {
//		return SCRequestTool.buildQueryRequest(SCAPICodeConstants.CONTRACT_FIRST_PAYMENT_T244,FinancialEntityAccountTypeEnum.FINANCIAL_ENTITY_ACCOUNT_TYPE_2.getCode());
//	}
//
//	/**
//	 * 划扣保证金
//	 * @return
//	 */
//	public static SCRequestVO buildDeductionMargin() {
//		return SCRequestTool.buildQueryRequest(SCAPICodeConstants.TWO_PARTY_TRANSACTION_T145_1,FinancialEntityAccountTypeEnum.FINANCIAL_ENTITY_ACCOUNT_TYPE_1.getCode());
//	}

    /**
     * 构建查询参数对象
     *
     * @return
     */
    private static SCRequestVO buildQueryRequest(String msgId, String financialEntityAccountType) {
        return SCRequestTool.buildCommomRequest(msgId, financialEntityAccountType);
    }

    /**
     * 构建公用属性
     *
     * @return
     */
    private static SCRequestVO buildCommomRequest(String msgId, String financialEntityAccountType) {
        SCRequestVO requestVo = new SCRequestVO();
        requestVo.setVersion("1");
        requestVo.setFeAccountCode(financialEntityAccountType);
        requestVo.setBusinessModuleId(BusinessModuleEnum.NEGOTIATE.getBusinessModuleId());
        requestVo.setMsgId(msgId);
        return requestVo;
    }

    /**
     * 构建SC请求对象, 如果交易码为空, 会自动从结算中心获取
     *
     * @param txCode                 交易码
     * @param tradeMode              交易模式
     * @param financialEntityAccount 财务主体
     * @param msgId                  消息ID
     * @return SC请求对象
     */
    public SCRequestVO buildSCRequestVO(@Nullable Long txCode, @Nonnull TradeMode tradeMode,
                                        @Nonnull FinancialEntityAccountTypeEnum financialEntityAccount,
                                        @Nonnull String msgId) {
        SCRequestVO requestVo = new SCRequestVO();

        if (Objects.isNull(txCode)) {
            txCode = txCodeService.getTXCode();
        }

        requestVo.setTxCode(txCode);
        requestVo.setMsgId(msgId);
        requestVo.setFeAccountCode(financialEntityAccount.getCode());
        if (tradeMode == TradeMode.SPOT_LISTING) {
            requestVo.setBusinessModuleId(BusinessModuleEnum.SPOT_LISTING.businessModuleId);
        }
        if (tradeMode == TradeMode.BASIS_PRICING) {
            requestVo.setBusinessModuleId(BusinessModuleEnum.WAREHOUSE_RECEIPT_TRADE.businessModuleId);
        }
        return requestVo;
    }


}
