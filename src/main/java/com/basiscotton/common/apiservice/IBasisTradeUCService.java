package com.basiscotton.common.apiservice;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.sinosoft.dandelion.system.client.model.Custom;
import org.harry.dandelion.framework.core.params.PageParameter;
import org.harry.dandelion.framework.core.params.Pagination;

/**
 * @Title: IBasisTradeUCService.java
 * @Description: 交易-用户服务
 * <AUTHOR>
 * @date 2025/7/14
 * @version V1.0
 */
public interface IBasisTradeUCService {

	/**
	 * 根据代码或名称模糊查询交易商
	 * @param codeOrName 代码或者名称
	 * @return
	 */
	public Pagination<Custom> getCustomsByCodeOrName(Wrapper<Custom> paramWrapper,PageParameter page);
	
}
