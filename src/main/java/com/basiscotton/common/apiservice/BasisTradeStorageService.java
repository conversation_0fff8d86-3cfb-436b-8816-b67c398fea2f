package com.basiscotton.common.apiservice;

import com.cottoneasy.storage.trade.base.vo.response.BaseResponse;
import com.cottoneasy.storage.trade.client.TraderProxy;
import lombok.extern.slf4j.Slf4j;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class BasisTradeStorageService implements IBasisTradeStorageService {

	@Resource
	private TraderProxy traderProxy;

	/**
     * 变更仓单状态1:标记2:取消标记
     */
    @Override
    public List<BaseResponse> updateWarehouseReceiptStatus(String warehouseReceiptNos, Integer mark, Map<String,Object> businessReq, String businessService){
    	log.info("变更仓单状态");
    	//添加接口调用记录
     	List<BaseResponse> baseResponse;
     	try {
			log.info("变更仓单状态");
			baseResponse = traderProxy.markTradering(warehouseReceiptNos, mark);
 		} catch (Exception e) {
 			throw new BusinessException(Constants.serviceErrorCode, e.getMessage());
 		}
 		return baseResponse;
    }
}
