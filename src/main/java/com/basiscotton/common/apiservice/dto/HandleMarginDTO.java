package com.basiscotton.common.apiservice.dto;

import com.basiscotton.base.entity.BasisQuoteEntity;
import com.basiscotton.base.entity.BasisStockEntity;
import com.basiscotton.base.enums.TradeMode;
import com.sinosoft.cnce.sc.base.enumeration.FinancialEntityAccountTypeEnum;
import lombok.Builder;
import lombok.Value;
import lombok.extern.slf4j.Slf4j;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.function.Function;

@Value
@Builder
@Slf4j
public class HandleMarginDTO {

    /**
     * 客户编号
     */
    String customerCode;

    /**
     * 要处理的保证金金额
     */
    BigDecimal amount;

    /**
     * 财务主体
     */
    FinancialEntityAccountTypeEnum feAccountType;

    /**
     * 业务编号
     */
    String businessId;

    TradeMode tradeMode;

    public String getClientAccountCode() {
        return this.feAccountType.getCode() + this.customerCode;
    }

    public void check() {
        Objects.requireNonNull(customerCode, "客户编号不能为空");
        Objects.requireNonNull(amount, "保证金金额不能为空");
        Objects.requireNonNull(tradeMode, " 交易模式不能为空");
        Objects.requireNonNull(businessId, "业务 ID 不能为空");
    }

    private static FinancialEntityAccountTypeEnum getFinancialEntityAccountType(String buyerTradeFeeType) {
        FinancialEntityAccountTypeEnum financialEntityAccount;
        switch (buyerTradeFeeType) {
            case "1":
                financialEntityAccount = FinancialEntityAccountTypeEnum.FINANCIAL_ENTITY_ACCOUNT_TYPE_1;
                break;
            case "2":
                financialEntityAccount = FinancialEntityAccountTypeEnum.FINANCIAL_ENTITY_ACCOUNT_TYPE_2;
                break;
            case "3":
                financialEntityAccount = FinancialEntityAccountTypeEnum.FINANCIAL_ENTITY_ACCOUNT_TYPE_301;
                break;
            default:
                log.warn("非法的报价手续费支付类型, 错误信息: {}", buyerTradeFeeType);
                throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, "非法的报价手续费支付类型");
        }
        return financialEntityAccount;
    }

    public static Function<BasisQuoteEntity, HandleMarginDTO> quoteToHandleMarginDTO(TradeMode tradeMode) {
        return quote -> HandleMarginDTO.builder()
                .customerCode(quote.getTraderCode())
                .amount(quote.getBuyerBasisMarginAmount())
                .feAccountType(getFinancialEntityAccountType(quote.getBuyerTradeFeeType().getCode()))
                .businessId(quote.getId().toString())
                .tradeMode(tradeMode)
                .build();
    }
    public static Function<BasisQuoteEntity, HandleMarginDTO> quoteToHandleMarginDTOTradeFee(TradeMode tradeMode) {
        return quote -> HandleMarginDTO.builder()
                .customerCode(quote.getTraderCode())
                .amount(quote.getBuyerTradeFeeAmount())
                .feAccountType(getFinancialEntityAccountType(quote.getBuyerTradeFeeType().getCode()))
                .businessId(quote.getId().toString())
                .tradeMode(tradeMode)
                .build();
    }

    public static Function<BasisStockEntity, HandleMarginDTO> quoteToHandleMarginDTOFromStock(TradeMode tradeMode) {
        return stock -> HandleMarginDTO.builder()
                .customerCode(stock.getTraderCode())
                .amount(stock.getSellerFreezeMarginAmount())
                .feAccountType(getFinancialEntityAccountType(stock.getSellerBasisMarginType().getCode()))
                .businessId(stock.getId().toString())
                .tradeMode(tradeMode)
                .build();
    }

    public static Function<BasisStockEntity, HandleMarginDTO> quoteToHandleMarginDTOFromStockTradFee(TradeMode tradeMode) {
        return stock -> HandleMarginDTO.builder()
                .customerCode(stock.getTraderCode())
                .amount(stock.getSellerFreezeTradeFeeAmount())
                .feAccountType(getFinancialEntityAccountType(stock.getSellerBasisMarginType().getCode()))
                .businessId(stock.getId().toString())
                .tradeMode(tradeMode)
                .build();
    }
}
