package com.basiscotton.common.apiservice.dto;

import com.basiscotton.base.enums.TradeMode;
import com.sinosoft.cnce.sc.base.enumeration.BusinessModuleEnum;
import com.sinosoft.cnce.sc.base.enumeration.FinancialEntityAccountTypeEnum;
import lombok.Builder;
import lombok.Value;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Objects;

@Value
@Builder
public class HandleTradeFeeDTO {

    /**
     * 交易模式
     */
    @NotNull
    TradeMode tradeMode;

    /**
     * 交易手续费
     */
    @NotNull
    BigDecimal tradeFee;

    /**
     * 交易员代码
     */
    @NotNull
    String traderCode;

    /**
     * 财务主体类型
     */
    @NotNull
    FinancialEntityAccountTypeEnum financialEntityAccount;

    /**
     * 业务模块
     */
    @NotNull
    BusinessModuleEnum businessModule;

    /**
     * 商品代码
     */
    String stockCode;

    /**
     * 业务 ID
     */
    Integer businessId;

    public void check() {
        Objects.requireNonNull(tradeMode, "交易模式不能为空");
        Objects.requireNonNull(tradeFee, "交易手续费不能为空");
        Objects.requireNonNull(traderCode, "交易员代码不能为空");
        Objects.requireNonNull(financialEntityAccount, "财务主体类型不能为空");
        Objects.requireNonNull(businessModule, "业务模块不能为空");
        Objects.requireNonNull(stockCode, "商品代码不能为空");
    }

}
