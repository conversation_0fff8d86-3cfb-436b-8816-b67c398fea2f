package com.basiscotton.common.apiservice;

import com.basiscotton.base.entity.BasisDeliveryTargetEntity;
import com.basiscotton.common.apiservice.dto.HandleMarginDTO;
import com.basiscotton.common.apiservice.dto.HandleTradeFeeDTO;
import com.sinosoft.cnce.sc.base.vo.SCResponseVO;
import com.sinosoft.cnce.sc.base.vo.query.CustomerFundsVO;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: IBasisTradeSettlementService.java
 * @Description: 交易-结算服务
 * @date 2025/7/14
 */
public interface IBasisTradeSettlementService {

    /**
     * 查询交易商账户可用资金
     *
     * @param traderCode 交易商代码
     * @return
     */
    public CustomerFundsVO queryAccountCapital(String traderCode);

    /**
     * 查询交易商保证金账户可用资金
     *
     * @param traderCode 交易商代码
     * @return
     */
    public CustomerFundsVO queryMarginAccountCapital(String traderCode);

    /**
     * 冻结资金接口
     *
     * @param traderCode 交易商代码
     * @param freeAmount 冻结金额
     * @return
     */
    public SCResponseVO freezeCapital(String businessSeq, String traderCode, BigDecimal freeAmount, String accountType, Map<String, Object> businessReq, String businessService);

    /**
     * 释放资金接口
     *
     * @param traderCode 交易商代码
     * @param freeAmount 释放金额
     * @return
     */
    public SCResponseVO freeCapital(String businessSeq, String traderCode, BigDecimal freeAmount, String accountType, Map<String, Object> businessReq, String businessService);

    /**
     * 冻结保证金
     *
     * @param dtoList 要处理的保证金信息
     */
    void freezeMargin(List<HandleMarginDTO> dtoList);

    /**
     * 释放保证金
     *
     *@param dtoList 要处理的保证金信息
     */
    void freeMargin(List<HandleMarginDTO> dtoList);

    /**
     * 结算交易手续费
     * @return 结算结果
     */
    SCResponseVO settleTradeFee(HandleTradeFeeDTO handleTradeFeeDTO, Long txCode);
}
