package com.basiscotton.common.apiservice;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.ClientFrozenFundsDetailsEntity;
import com.basiscotton.base.entity.ClientFrozenFundsFlowInfoEntity;
import com.basiscotton.base.entity.ClientSubFundsEntity;
import com.basiscotton.base.mappers.ClientFrozenFundsDetailsMapper;
import com.basiscotton.base.mappers.ClientFrozenFundsFlowInfoMapper;
import com.basiscotton.base.mappers.ClientSubFundsMapper;
import com.basiscotton.common.apiservice.dto.HandleMarginDTO;
import com.basiscotton.common.apiservice.dto.HandleTradeFeeDTO;
import com.basiscotton.locks.redislocks.TradeLockUtils;
import com.basiscotton.tradingcore.cache.SettleCodeCacheService;
import com.sinosoft.cnce.sc.api.service.TXCodeService;
import com.sinosoft.cnce.sc.api.service.common.FundControl4RestService;
import com.sinosoft.cnce.sc.api.service.common.QueryService;
import com.sinosoft.cnce.sc.api.service.group.OtherService;
import com.sinosoft.cnce.sc.base.constant.SCAPICodeConstants;
import com.sinosoft.cnce.sc.base.enumeration.BusinessModuleEnum;
import com.sinosoft.cnce.sc.base.enumeration.ResponseCodeEnum;
import com.sinosoft.cnce.sc.base.vo.SCRequestVO;
import com.sinosoft.cnce.sc.base.vo.SCResponseVO;
import com.sinosoft.cnce.sc.base.vo.control.FreezeSC2001VO;
import com.sinosoft.cnce.sc.base.vo.query.CustomerFundsVO;
import com.sinosoft.cnce.sc.base.vo.transaction.common.ReleaseAndPayTransactionChargeT141VO;
import com.sinosoft.dandelion.system.client.params.DataItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.harry.dandelion.framework.common.utils.DateUtils;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BasisTradeSettlementService implements IBasisTradeSettlementService {

    @Resource
    private TXCodeService txCodeService;

    @Resource
    private QueryService queryService;

    @Resource
    private FundControl4RestService fundControl4RestService;

    @Resource
    private TradeLockUtils tradeLockUtils;

    @Resource
    private ClientSubFundsMapper clientSubFundsMapper;

    @Resource
    private ClientFrozenFundsDetailsMapper clientFrozenFundsDetailsMapper;

    @Resource
    private ClientFrozenFundsFlowInfoMapper clientFrozenFundsFlowInfoMapper;

    @Resource
    private SCRequestTool scRequestTool;

    @Resource
    private OtherService otherService;

    @Resource
    private SettleCodeCacheService settleCodeCacheService;

    /**
     * 查询交易商账户可用资金
     */
    @Override
    public CustomerFundsVO queryAccountCapital(String traderCode) {
        try {
            log.debug("依据客户结算码 [{}]，查询资金余额", traderCode);
            if (StringUtil.isEmpty(traderCode)) {
                throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "调用查询客户可用资金服务时，缺少参数客户结算码！");
            }
            Long startTime = System.currentTimeMillis();
            //构造查询参数
            CustomerFundsVO customerFundsVO = queryService.requestSC1001(SCRequestTool.buildQueryAccountCapital(), traderCode);
            Long endTime = System.currentTimeMillis();
            log.debug("查询客户可用资金结束，总耗时: {} s", (endTime - startTime) / 1000);
            return customerFundsVO;
        } catch (Exception e) {
            throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, e.toString());
        }
    }

    /**
     * 查询交易商保证金账户可用资金
     *
     * @param traderCode 交易商代码
     */
    @Override
    public CustomerFundsVO queryMarginAccountCapital(String traderCode) {
        try {
            if (StringUtil.isEmpty(traderCode)) {
                throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "调用查询客户保证金可用资金服务时，缺少参数客户结算码！");
            }
            Long startTime = System.currentTimeMillis();
            log.debug("依据客户结算码 [{}]，查询保证金余额", traderCode);
            //构造查询参数
            CustomerFundsVO customerFundsVO = new CustomerFundsVO();
            if (!(BasisCottonConstants.DS_CUSTOM_CODE.equals(traderCode))) {
                SCRequestVO scRequestVO = SCRequestTool.buildQueryMarginAccountCapital();
                scRequestVO.setBusinessModuleId(BusinessModuleEnum.WAREHOUSE_RECEIPT_TRADE.getBusinessModuleId());
                customerFundsVO = queryService.requestSC1001(scRequestVO, traderCode);
            }
            Long endTime = System.currentTimeMillis();
            log.debug("查询客户保证金可用资金结束，总耗时: {} s", (endTime - startTime) / 1000);
            return customerFundsVO;
        } catch (Exception e) {
            throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, e.toString());
        }
    }

    /**
     * 冻结资金接口
     */
    @Override
    public SCResponseVO freezeCapital(String businessSeq, String traderCode, BigDecimal freeAmount,
                                      String accountType, Map<String, Object> businessReq, String businessService) {
        if (StringUtil.isEmpty(traderCode)) {
            throw new BusinessException(Constants.serviceErrorCode, "调用资金冻结服务时，缺少参数客户结算码！");
        }
        Long startTime = System.currentTimeMillis();
        log.debug("依据客户结算码 [{}]，冻结资金[{}]", traderCode, freeAmount);
        //构造查询参数
        FreezeSC2001VO requestParam = new FreezeSC2001VO();
        requestParam.setAmount(freeAmount);
        requestParam.setBusinessId(businessSeq);
        requestParam.setClientCode(traderCode);
        requestParam.setOperation("1");
        requestParam.setRemarks(DateUtils.getMillis() + "");
        SCRequestVO scRequestVO = SCRequestTool.buildFreezeCapital();
        Long txCode = this.txCodeService.getTXCode();
        scRequestVO.setTxCode(txCode);
        scRequestVO.setFeAccountCode(accountType);
        scRequestVO.setBusinessModuleId(BusinessModuleEnum.WAREHOUSE_RECEIPT_TRADE.getBusinessModuleId());
        //资金为0时，不调用结算接口
        SCResponseVO scResponseVO = new SCResponseVO();
        if (freeAmount.compareTo(BigDecimal.ZERO) > 0) {
            scResponseVO = fundControl4RestService.requestSC2001(scRequestVO, requestParam);
        }
        Long endTime = System.currentTimeMillis();
        log.debug("冻结资金结束，总耗时: {} s", (endTime - startTime) / 1000);
        if (StringUtils.isNotEmpty(scResponseVO.getCode()) && !(scResponseVO.getCode().equals(ResponseCodeEnum.SETTLEMENT_SUCCESS.getCode()))) {
            log.error("冻结资金异常: {}", scResponseVO.getMessage());
            throw new BusinessException(Constants.serviceErrorCode, "结算失败" + scResponseVO.getMessage());
        }
        return scResponseVO;
    }

    /**
     * 释放资金接口
     */
    @Override
    public SCResponseVO freeCapital(String businessSeq, String traderCode, BigDecimal freeAmount, String accountType, Map<String, Object> businessReq, String businessService) {
        if (StringUtil.isEmpty(traderCode)) {
            throw new BusinessException(Constants.serviceErrorCode, "调用释放冻结服务时，缺少参数客户结算码！");
        }
        Long startTime = System.currentTimeMillis();
        log.debug("依据客户结算码 [{}]，释放资金[{}]", traderCode, freeAmount);
        //构造查询参数
        FreezeSC2001VO requestParam = new FreezeSC2001VO();
        requestParam.setAmount(freeAmount);
        requestParam.setBusinessId(businessSeq);
        requestParam.setClientCode(traderCode);
        requestParam.setOperation("2");
        requestParam.setRemarks(DateUtils.getMillis() + "");
        SCRequestVO scRequestVO = SCRequestTool.buildFreezeCapital();
        Long txCode = this.txCodeService.getTXCode();
        scRequestVO.setTxCode(txCode);
        scRequestVO.setFeAccountCode(accountType);
        scRequestVO.setBusinessModuleId(BusinessModuleEnum.WAREHOUSE_RECEIPT_TRADE.getBusinessModuleId());
        //资金为0时，不调用结算接口
        SCResponseVO scResponseVO = new SCResponseVO();
        if (freeAmount.compareTo(BigDecimal.ZERO) > 0) {
            scResponseVO = fundControl4RestService.requestSC2001(scRequestVO, requestParam);
        }
        Long endTime = System.currentTimeMillis();
        log.debug("释放资金结束，总耗时: {} s", (endTime - startTime) / 1000);
        if (StringUtils.isNotEmpty(scResponseVO.getCode()) && !(scResponseVO.getCode().equals(ResponseCodeEnum.SETTLEMENT_SUCCESS.getCode()))) {
            log.error("释放资金异常: {}", scResponseVO.getMessage());
            throw new BusinessException(Constants.serviceErrorCode, "结算失败" + scResponseVO.getMessage());
        }
        return scResponseVO;
    }

    @Override
    @DS("SC")
    public void freezeMargin(@Nonnull List<HandleMarginDTO> dtoList) {
        processMargin(dtoList, true);
    }


    @Override
    @DS("SC")
    public void freeMargin(@Nonnull List<HandleMarginDTO> dtoList) {
        processMargin(dtoList, false);
    }

    /**
     * 结算手续费
     *
     * @param handleTradeFeeDTO 参数
     * @param txCode            交易码, 如果是重试, 则传入原交易码
     * @return 结算响应结果
     */
    @Override
    public SCResponseVO settleTradeFee(@Nonnull HandleTradeFeeDTO handleTradeFeeDTO, @Nullable Long txCode) {
        Objects.requireNonNull(handleTradeFeeDTO, "结算交易手续费, 参数不能为空");
        handleTradeFeeDTO.check();

        SCRequestVO scRequestVO = scRequestTool.buildSCRequestVO(txCode,
                handleTradeFeeDTO.getTradeMode(),
                handleTradeFeeDTO.getFinancialEntityAccount(),
                SCAPICodeConstants.TRANSACTION_CHARGE_T141
        );

        ReleaseAndPayTransactionChargeT141VO requestParam = new ReleaseAndPayTransactionChargeT141VO();
        requestParam.setClientCode(handleTradeFeeDTO.getTraderCode());
        requestParam.setUnfrozeAmount(handleTradeFeeDTO.getTradeFee());
        requestParam.setTransactionCharge(handleTradeFeeDTO.getTradeFee());
        requestParam.setBusinessId(handleTradeFeeDTO.getBusinessId());
        requestParam.setContractNo(handleTradeFeeDTO.getStockCode());

        return otherService.requestT141(scRequestVO, requestParam);
    }

    private void processMargin(List<HandleMarginDTO> dtoList, boolean isFreeze) {
        Objects.requireNonNull(dtoList, "要处理的保证金信息不能为空");
        if (dtoList.isEmpty()) {
            log.info("没有要处理的保证金信息");
            return;
        }

        // 参数校验
        dtoList.forEach(HandleMarginDTO::check);

        // 获取客户账户并排序
        List<String> clientAccountCodeList = getClientAccountCodeList(dtoList);

        try {
            // 获取锁
            tradeLockUtils.addWaitLock(clientAccountCodeList, 300000L, 200L);

            for (HandleMarginDTO handleMarginDTO : dtoList) {
                String settleCode = settleCodeCacheService.get(handleMarginDTO.getCustomerCode());
                String clientAccountCode = handleMarginDTO.getFeAccountType().getCode() + settleCode;
                ClientSubFundsEntity clientSubFunds = clientSubFundsMapper.selectById(clientAccountCode);
                if (clientSubFunds == null) {
                    throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "客户子账户不存在");
                }
                // 处理资金账户冻结资金计算
                processClientSubFundsMargin(clientSubFunds, handleMarginDTO.getAmount(), isFreeze);
                clientSubFundsMapper.updateById(clientSubFunds);
                // 处理冻结资金流水
                processFrozenFundsFlow(handleMarginDTO, settleCode, clientAccountCode, handleMarginDTO.getAmount(), isFreeze);
                // 处理冻结资金详情
                processFrozenFundsDetails(handleMarginDTO, clientAccountCode, settleCode, isFreeze);
            }
        } finally {
            tradeLockUtils.unWaitLock(clientAccountCodeList);
        }

    }

    /**
     * 处理客户子账户保证金
     *
     * @param clientSubFunds 客户子账户实体
     * @param amount         金额
     * @param isFreeze       是否冻结
     */
    private void processClientSubFundsMargin(ClientSubFundsEntity clientSubFunds, BigDecimal amount, boolean isFreeze) {
        if (isFreeze) {
            if (clientSubFunds.getAvailableBalance().compareTo(amount) < 0) {
                log.warn("客户资金不足, 客户账户代码: {}, 可用资金: {}, 金额: {}", clientSubFunds.getClientAccountCode(),
                        clientSubFunds.getAvailableBalance(),
                        amount);
                throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, "客户资金不足");
            }
            clientSubFunds.setFrozenFunds(clientSubFunds.getFrozenFunds().add(amount));
            clientSubFunds.setAvailableBalance(clientSubFunds.getAvailableBalance().subtract(amount));
            clientSubFunds.setIdleFunds(clientSubFunds.getIdleFunds().subtract(amount));
        } else {
            if (clientSubFunds.getFrozenFunds().compareTo(amount) < 0) {
                log.warn("客户冻结资金不足, 客户账户代码: {}, 冻结资金: {}, 金额: {}", clientSubFunds.getClientAccountCode(),
                        clientSubFunds.getFrozenFunds(),
                        amount);
                throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, "客户冻结资金不足");
            }
            clientSubFunds.setFrozenFunds(clientSubFunds.getFrozenFunds().subtract(amount));
            clientSubFunds.setAvailableBalance(clientSubFunds.getAvailableBalance().add(amount));
            clientSubFunds.setIdleFunds(clientSubFunds.getIdleFunds().add(amount));
        }
    }

    /**
     * 处理冻结资金流水
     *
     * @param dto               保证金冻结信息
     * @param settleCode        结算码
     * @param clientAccountCode 客户资金账户代码
     * @param frozenFunds       冻结金额
     * @param isFreeze          是否冻结
     */
    public void processFrozenFundsFlow(@Nonnull HandleMarginDTO dto,
                                       @Nonnull String settleCode,
                                       @Nonnull String clientAccountCode,
                                       @Nonnull BigDecimal frozenFunds,
                                       boolean isFreeze) {
        dto.check();

        ClientFrozenFundsFlowInfoEntity frozenFundsFlowInfo = new ClientFrozenFundsFlowInfoEntity();
        frozenFundsFlowInfo.setClientAccountCode(clientAccountCode);
        frozenFundsFlowInfo.setClientCode(dto.getCustomerCode());
        frozenFundsFlowInfo.setSettleCode(settleCode);
        frozenFundsFlowInfo.setMsgId(SCAPICodeConstants.FREEZE_SC2001);
        frozenFundsFlowInfo.setFeAccountCode(dto.getFeAccountType().getCode());
        frozenFundsFlowInfo.setBusinessModuleId(new DataItem(dto.getTradeMode().getBusinessModuleId(), null));
        frozenFundsFlowInfo.setBusinessId(dto.getBusinessId());
        DataItem type = isFreeze ? new DataItem("1", null) : new DataItem("2", null);
        frozenFundsFlowInfo.setClientFrozenFundsFlowType(type);
        frozenFundsFlowInfo.setAmount(dto.getAmount());
        frozenFundsFlowInfo.setFrozenFunds(frozenFunds);
        frozenFundsFlowInfo.setNote("");
        clientFrozenFundsFlowInfoMapper.insert(frozenFundsFlowInfo);
    }

    /**
     * 处理冻结资金明细
     *
     * @param dto               处理冻结资金明细参数
     * @param clientAccountCode 客户资金账户代码
     * @param settleCode        结算码
     * @param isFreeze          是否冻结
     */
    public void processFrozenFundsDetails(HandleMarginDTO dto,
                                          String clientAccountCode,
                                          String settleCode,
                                          boolean isFreeze) {
        // 处理冻结明细
        LambdaQueryWrapper<ClientFrozenFundsDetailsEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ClientFrozenFundsDetailsEntity::getClientAccountCode, clientAccountCode);
        queryWrapper.eq(ClientFrozenFundsDetailsEntity::getBusinessModuleId, dto.getTradeMode().getBusinessModuleId());
        ClientFrozenFundsDetailsEntity frozenFundsDetail = clientFrozenFundsDetailsMapper.selectOne(queryWrapper);

        if (isFreeze) {
            if (frozenFundsDetail == null) {
                // 首次冻结
                frozenFundsDetail = new ClientFrozenFundsDetailsEntity();
                frozenFundsDetail.setClientAccountCode(clientAccountCode);
                frozenFundsDetail.setClientCode(dto.getCustomerCode());
                frozenFundsDetail.setSettleCode(settleCode);
                frozenFundsDetail.setFeAccountCode(dto.getFeAccountType().getCode());
                frozenFundsDetail.setBusinessModuleId(dto.getTradeMode().getDataItem());
                frozenFundsDetail.setFrozenFunds(dto.getAmount());
                clientFrozenFundsDetailsMapper.insert(frozenFundsDetail);
            } else {
                frozenFundsDetail.setFrozenFunds(frozenFundsDetail.getFrozenFunds().add(dto.getAmount()));
                frozenFundsDetail.setUpdateTime(new Date());
                clientFrozenFundsDetailsMapper.updateById(frozenFundsDetail);
            }
        } else {
            if (frozenFundsDetail == null) {
                log.warn("释放保证金, 无冻结资金记录, 业务 ID: {}", dto.getBusinessId());
                throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, "无冻结资金记录");
            } else {
                if (frozenFundsDetail.getFrozenFunds().compareTo(dto.getAmount()) < 0) {
                    log.warn("释放保证金, 冻结资金不足, 业务 ID: {}", dto.getBusinessId());
                    throw new BusinessException(Constants.SERVICE_DOWORK_EXCEPTION_CODE, "冻结资金不足");
                }
                frozenFundsDetail.setFrozenFunds(frozenFundsDetail.getFrozenFunds().subtract(dto.getAmount()));
                frozenFundsDetail.setUpdateTime(new Date());
                clientFrozenFundsDetailsMapper.updateById(frozenFundsDetail);
            }
        }
    }

    private List<String> getClientAccountCodeList(List<HandleMarginDTO> dtoList) {
        return dtoList.stream()
                .map(HandleMarginDTO::getClientAccountCode)
                .sorted()
                .collect(Collectors.toList());
    }
}
