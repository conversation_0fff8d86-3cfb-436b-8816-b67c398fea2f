package com.basiscotton.common.apiservice;

import com.cottoneasy.storage.trade.base.vo.response.BaseResponse;

import java.util.List;
import java.util.Map;

/**
 * @Title: ITradeStorageService.java
 * @Description: 交易-仓储服务
 * <AUTHOR>
 * @date 2025/7/14
 * @version V1.0
 */
public interface IBasisTradeStorageService {

	/**
     * 变更仓单状态
     *
     * @param warehouseReceiptNo 仓单号
     * @return
     */
    List<BaseResponse> updateWarehouseReceiptStatus(String warehouseReceiptNos, Integer mark, Map<String,Object> businessReq, String businessService);

}
