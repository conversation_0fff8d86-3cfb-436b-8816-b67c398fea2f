package com.basiscotton.common.apiservice;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.sinosoft.dandelion.system.client.model.Custom;
import com.sinosoft.dandelion.system.client.service.CustomService;
import org.harry.dandelion.framework.core.params.PageParameter;
import org.harry.dandelion.framework.core.params.Pagination;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class BasisTradeUCService implements IBasisTradeUCService {
	
	@Resource
	private CustomService customService;
	
	/**
	 * 根据代码或名称模糊查询交易商
	 */
	@Override
	public Pagination<Custom> getCustomsByCodeOrName(Wrapper<Custom> paramWrapper,PageParameter page){
		Pagination<Custom> pagination = new Pagination<Custom>();
		pagination = customService.queryCustoms(paramWrapper, page);
		return pagination;
	}
	
}
