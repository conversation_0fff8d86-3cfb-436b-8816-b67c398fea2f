package com.basiscotton.common.tradetime.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: MonitorHolidayVo.java
 * @Description: 节假日VO类
 * @date 2025/8/22 13:50
 */
@ApiModel(value = "监控系统节假日")
@Data
public class MonitorHolidayVo  implements Serializable {

    @Column(name ="HOLIDAY_DATE")
    @ApiModelProperty(value = "节假日日期")
    private String holidayDate;

    @Column(name ="WORK_DAY")
    @ApiModelProperty(value = "日期类型：1:工作日 2：非工作日")
    private String workDay;
}
