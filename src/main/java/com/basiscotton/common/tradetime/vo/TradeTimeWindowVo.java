package com.basiscotton.common.tradetime.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: TradeTimeWindowVo.java
 * @Description: 交易时间窗口
 * @date 2025/8/22 15:05
 */
@ApiModel(value = "交易时间窗口")
@Data
public class TradeTimeWindowVo  implements Serializable {

    @ApiModelProperty(value = "是否交易日")
    private boolean TradeDate;

    /**
     * 交易开始时间
     */
    @ApiModelProperty(value="交易开始时间")
    private LocalTime tradeStartTime;

    /**
     * 交易结束时间
     */
    @ApiModelProperty(value="交易结束时间")
    private LocalTime tradeEndTime;

    @ApiModelProperty(value="挂牌系统状态 1正常 2暂停")
    private String spotSystemStatus;

    @ApiModelProperty(value="最大有效提交天数")
    private Integer maxValiditySubmitDays;
}
