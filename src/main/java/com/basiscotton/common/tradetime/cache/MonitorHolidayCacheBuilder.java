package com.basiscotton.common.tradetime.cache;

import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.mappers.BasisMarketSettingMapper;
import com.basiscotton.common.tradetime.vo.MonitorHolidayVo;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: MonitorHolidayCache.java
 * @Description: 监控系统节假日缓存
 * @date 2025/9/1 08:31
 */
@Configuration
@Slf4j
public class MonitorHolidayCacheBuilder {
    @Resource
    private BasisMarketSettingMapper basisMarketSettingMapper;


    @Primary
    @Bean
    public Cache<String ,List<MonitorHolidayVo>> monitorHolidayCache(){
        Cache<String ,List<MonitorHolidayVo>> cache = Caffeine.newBuilder()
                .initialCapacity(50)
                .maximumSize(100)
                .expireAfterWrite(1, TimeUnit.HOURS)
                .recordStats()
                .build(this::loadMonitorHolidayList);
        log.info("节假日缓存构建完成！");
        return cache;
    }

    public List<MonitorHolidayVo> loadMonitorHolidayList(String key) {
        log.debug("重新加载节假日数据，key: {}", key);
        return basisMarketSettingMapper.selectMonitorHoliday();
    }

    // 提供获取节假日列表的公共方法
    public List<MonitorHolidayVo> getHolidayList() {
        return this.monitorHolidayCache().get(BasisCottonConstants.HOLIDAY_CACHE_KEY, this::loadMonitorHolidayList);
    }
}
