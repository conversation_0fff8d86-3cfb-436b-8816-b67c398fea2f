package com.basiscotton.common.tradetime.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.MarketSettingEntity;
import com.basiscotton.base.mappers.BasisMarketSettingMapper;
import com.basiscotton.common.tradetime.cache.MonitorHolidayCacheBuilder;
import com.basiscotton.common.tradetime.vo.MonitorHolidayVo;
import com.basiscotton.common.tradetime.vo.TradeTimeWindowVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: SpotTradeTimeWindowService.java
 * @Description: 现货交易时间窗口服务
 * @date 2025/8/29 08:24
 */
@Service
@Slf4j
public class SpotTradeTimeWindowService {

    @Resource
    private BasisMarketSettingMapper basisMarketSettingMapper;



    @Resource
    private MonitorHolidayCacheBuilder holidayCacheManager;

    /**
     * 定义一个静态变量，用于存储设置信息
     */
    final static TradeTimeWindowVo tradeTimeWindowVo = new TradeTimeWindowVo();


    /*
     * @Description: 初始化缓存
     **/
    @PostConstruct
    public void initTradeTimeWindow(){
        try{
            //1当日是否可交易
            List<MonitorHolidayVo> monitorHolidaylist = holidayCacheManager.getHolidayList();
            tradeTimeWindowVo.setTradeDate(isTodayTradeDate(monitorHolidaylist));
            //2交易的时间
            LambdaQueryWrapper<MarketSettingEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(MarketSettingEntity::getBasisSettingType, BasisCottonConstants.basis_setting_type_3);
            queryWrapper.eq(MarketSettingEntity::getSettingStatus, BasisCottonConstants.setting_status_1);
            MarketSettingEntity settingEntity = basisMarketSettingMapper.selectOne(queryWrapper);
            if(settingEntity != null) {
                tradeTimeWindowVo.setSpotSystemStatus(settingEntity.getSpotSystemStatus().getCode());
                tradeTimeWindowVo.setTradeStartTime(settingEntity.getTradeStartTime());
                tradeTimeWindowVo.setTradeEndTime(settingEntity.getTradeEndTime());
                tradeTimeWindowVo.setMaxValiditySubmitDays(settingEntity.getMaxValiditySubmitDays());
            }

        }catch (Exception e){
            log.error("初始化市场设置信息失败！", e);
        }

    }

    /*
     * @Description: 验证交易时间范围
     * //非交易日，则返回
     **/
    public String verifyTradeTimeWindow(){
        String  result = BasisCottonConstants.spot_system_status_1;
        List<MonitorHolidayVo> monitorHolidaylist = holidayCacheManager.getHolidayList();
        tradeTimeWindowVo.setTradeDate(isTodayTradeDate(monitorHolidaylist));
        //防御性判断
        if(tradeTimeWindowVo!=null  && !tradeTimeWindowVo.getSpotSystemStatus().equals(BasisCottonConstants.spot_system_status_1)){
            result = BasisCottonConstants.spot_system_status_2;
        }
        if(!tradeTimeWindowVo.isTradeDate()){
            result = BasisCottonConstants.spot_system_status_3;
        }else{
            //取当前时间 LocalTime
            LocalTime now = LocalTime.now();
            //判断tradeTimeWindowVo中的开始与结束时间范围
            if(now.isBefore(tradeTimeWindowVo.getTradeStartTime()) || now.isAfter(tradeTimeWindowVo.getTradeEndTime())){
                result = BasisCottonConstants.spot_system_status_4;
            }
        }
        return result;
    }


    /*
     * @Description: 刷新交易时间,提供给市场设置来调用
     * @param:
     * @param: null
     * @return:
     **/
    public void refreshTradeTimeWindow(){
        initTradeTimeWindow();
    }

    /**
     * 内部方法，根据监控系统的节假日及周一至周五，判断当日是否可交易
     */
    private boolean isTodayTradeDate(List<MonitorHolidayVo> monitorHolidaylist){

        boolean isTradeDate = true;

        //定义当日
        LocalDate today = LocalDate.now();
        String todayStr = today.toString();
        DayOfWeek todayOfWeek = today.getDayOfWeek();
        //判断为周末,则不是交易日
        if(todayOfWeek == DayOfWeek.SATURDAY || todayOfWeek == DayOfWeek.SUNDAY){
            isTradeDate = false;
        }

        for (MonitorHolidayVo monitorHolidayVo : monitorHolidaylist) {
            //日期相同且为工作日，则为交易日
            if (todayStr.equals(monitorHolidayVo.getHolidayDate()) && monitorHolidayVo.getWorkDay().equals(BasisCottonConstants.WORK_DAY)) {
                isTradeDate = true;
            }
            //日期相同且为非工作日，则为非交易日
            if (todayStr.equals(monitorHolidayVo.getHolidayDate()) && monitorHolidayVo.getWorkDay().equals(BasisCottonConstants.NON_WORK_DAY)) {
                isTradeDate = false;
            }
        }
        return true;
    }

    /**
     * 验证资源提交的最大有效期--按工作日计算
     */
    public LocalDate getMaxSubmitTimeWithWorkDay(){
        //从今天开始，排除周末与节假日，有效期为15天，找到实际的最大有效日期
        LocalDate maxValiditySubmitDate = LocalDate.now();
        List<MonitorHolidayVo> monitorHolidaylist = holidayCacheManager.getHolidayList();
        for (int i = 0; i < tradeTimeWindowVo.getMaxValiditySubmitDays(); i++) {
                while (true){
                    boolean isTradeDate = true;
                    //定义当日
                    LocalDate today = maxValiditySubmitDate;
                    String todayStr = today.toString();
                    DayOfWeek todayOfWeek = today.getDayOfWeek();
                    //判断为周末,则不是交易日
                    if(todayOfWeek == DayOfWeek.SATURDAY || todayOfWeek == DayOfWeek.SUNDAY){
                        isTradeDate = false;
                    }

                    for (MonitorHolidayVo monitorHolidayVo : monitorHolidaylist) {
                        //日期相同且为工作日，则为交易日
                        if (todayStr.equals(monitorHolidayVo.getHolidayDate()) && monitorHolidayVo.getWorkDay().equals(BasisCottonConstants.WORK_DAY)) {
                            isTradeDate = true;
                        }
                        //日期相同且为非工作日，则为非交易日
                        if (todayStr.equals(monitorHolidayVo.getHolidayDate()) && monitorHolidayVo.getWorkDay().equals(BasisCottonConstants.NON_WORK_DAY)) {
                            isTradeDate = false;
                        }
                    }
                    maxValiditySubmitDate = maxValiditySubmitDate.plusDays(1);
                    if(isTradeDate){
                        break;
                    }
               }
        }
        return maxValiditySubmitDate;

    }
    /**
     * 验证资源提交的最大有效期--按自然日计算
     */
    public LocalDate getMaxSubmitTimeWithNaturalDay(){
        return LocalDate.now().plusDays(tradeTimeWindowVo.getMaxValiditySubmitDays());
    }


}
