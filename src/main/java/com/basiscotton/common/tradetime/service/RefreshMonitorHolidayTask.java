package com.basiscotton.common.tradetime.service;

import lombok.extern.slf4j.Slf4j;
import org.harry.dandelion.framework.scheduler.quartz.job.DandelionJob;
import org.quartz.JobExecutionContext;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: RefreshMonitorHoliday.java
 * @Description: 刷新监控系统节假日的定时任务  于0:10判断是否为节假日
 *
 * @date 2025/8/22 14:58
 */
@Slf4j
public class RefreshMonitorHolidayTask extends DandelionJob {
    @Resource
    private BasisTradeTimeWindowService basisTradeTimeWindowService;
    @Override
    public void process(JobExecutionContext context) throws Exception {
        basisTradeTimeWindowService.initTradeTimeWindow();
    }
}
