package com.basiscotton.common.tradetime.service;

import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.deliverycore.cache.bo.PricingCommand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalTime;


/**
 * @Title: TradeTimeService.java
 * @Description: 交易时间窗口验证服务
 * <AUTHOR>
 * @date 2025/8/20
 * @version V1.0
 */
@Service
@Slf4j
public class PricingTimeWindowService{

    @Resource
    private BasisTradeTimeWindowService basisTradeTimeWindowService;
	/*
	 * @Description: 验证在交易时间范围内，并且点价接收时间必须为点价有效期当日15点前
	 * //非交易日，则返回
	 **/
	public String verifyPricingTimeWindow(PricingCommand pricingCommand){
        String  result = basisTradeTimeWindowService.verifyTradeTimeWindow();
        if(result.equals(BasisCottonConstants.TRADE_NORMAL)){
            if(pricingCommand.getPricingValidEndTime().toLocalDate().equals(LocalDate.now())){
                //当前时间早于15:00
                if(LocalTime.now().isBefore(LocalTime.of(15,0))){
                    result = BasisCottonConstants.NOT_TRADE_TIME;
                }
            }
        }
        return result;
	}


}
