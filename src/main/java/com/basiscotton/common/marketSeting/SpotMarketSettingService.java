package com.basiscotton.common.marketSeting;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.MarketSettingEntity;
import com.basiscotton.base.mappers.BasisMarketSettingMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: SpotMarketSettingService.java
 * @Description: 现货挂牌市场设置服务
 * @date 2025/8/29 08:25
 */
@Service
@Slf4j
public class SpotMarketSettingService {
    @Resource
    private BasisMarketSettingMapper basisMarketSettingMapper;
    /**
     * 定义一个静态变量，用于存储设置信息
     */
    public static volatile MarketSettingEntity settingEntity;

    /**
     * 系统启动时，默认构造设置信息,init方法
     */
    @PostConstruct
    public void initMarketSetting() {
        try{
            LambdaQueryWrapper<MarketSettingEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(MarketSettingEntity::getBasisSettingType, BasisCottonConstants.basis_setting_type_3);
            queryWrapper.eq(MarketSettingEntity::getSettingStatus, BasisCottonConstants.setting_status_1);
            settingEntity = basisMarketSettingMapper.selectOne(queryWrapper);
        }catch (Exception e){
            log.error("初始化市场设置信息失败！", e);
        }
    }

    /**
     * 提供刷新方法
     */
    public void refreshMarketSeting() {
        initMarketSetting();
    }
}
