package com.basiscotton.common.idempotence;

import com.basiscotton.common.idempotence.cache.TimedValue;
import com.github.benmanes.caffeine.cache.Cache;
import lombok.extern.slf4j.Slf4j;
import org.harry.dandelion.framework.core.message.IMessageBody;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.ObjectOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: IdempotentAspect.java
 * @Description: web请求幂等性切面
 * @date 2025/9/2 08:56
 */

@Component
@Slf4j
public class IdempotentService {

    @Resource
    Cache<String, TimedValue<String>> webRequestDigestCache;

    public boolean idempotentVerify(ServiceHandlerContext context,  int expireTime)  {
        String digest = null;
        Boolean absent = false ;
        try {
            IMessageBody body = context.getRequestBody();
            String tradeCode = context.getCurrentUserCustomCode();
            // 计算请求体摘要
            String bodyStr = serializeIMessageBody( body) ;
            String digestStr = tradeCode+bodyStr;
            log.info("请求体摘要明文：{}", digestStr);
            digest = DigestUtils.md5DigestAsHex(digestStr.getBytes(StandardCharsets.UTF_8));
            log.info("请求体摘要hash：{}", digest);
            log.info("缓存：{}",getCache(digest));
            // setIfAbsent 返回 true
            if(getCache(digest)!= null){
                if(getCache(digest).equals(digest)) {
                    absent = true;
                }
                log.info("缓存请求摘要hash：{}",getCache(digest));
                log.info("缓存请求是否重复："+getCache(digest).equals(digest));
            }else  {
                putWithCustomExpiry(digest, digest, expireTime, TimeUnit.SECONDS);
            }

        } catch (Exception e) {
            //throw e;
            log.error("幂等性校验异常",e);
        }
        return absent;
    }
    private String serializeIMessageBody(IMessageBody body) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ObjectOutputStream oos = new ObjectOutputStream(baos);
        oos.writeObject(body);
        oos.close();

        byte[] bytes = baos.toByteArray();
        return Base64.getEncoder().encodeToString(bytes);
    }

    // 放入缓存时指定过期时间
    public void putWithCustomExpiry(String key, String value, long duration, TimeUnit unit) {
        TimedValue<String> timedValue = new TimedValue<>(value, unit.toNanos(duration));
        webRequestDigestCache.put(key, timedValue);
    }

    // 获取缓存值
    public String getCache(String key) {
        TimedValue<String> timedValue = webRequestDigestCache.getIfPresent(key);
        if (timedValue != null) {
            // Caffeine 会自动处理过期
            return timedValue.getValue().toString();
        }
        return null;
    }
    // 移除缓存
    public void remove(String key) {
        webRequestDigestCache.invalidate(key);
    }
}
