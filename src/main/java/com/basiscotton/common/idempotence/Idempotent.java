package com.basiscotton.common.idempotence;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: Idempotent.java
 * @Description: 幂等性接口
 * @date 2025/9/2 08:58
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Idempotent {
    int expire() default 120;   // 秒
}
