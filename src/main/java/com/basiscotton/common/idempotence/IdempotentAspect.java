package com.basiscotton.common.idempotence;


import com.basiscotton.base.ExceptionEnums;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 * @version V1.0
 * @Title: IdempotentAspect.java
 * @Description: web请求幂等性切面
 * @date 2025/9/2 08:56
 */
@Aspect
@Component
public class IdempotentAspect {

    @Resource
    private IdempotentService idempotentService;
    @Around("@annotation(idempotent)")
    public Object around(ProceedingJoinPoint joinPoint, Idempotent idempotent) throws Throwable {
        ServiceHandlerContext context = null;
        // 获取方法参数
        Object[] args = joinPoint.getArgs();
        if (args != null && args.length > 0) {
            for (Object arg : args) {
                if (arg instanceof ServiceHandlerContext) {
                    ServiceHandlerContext argContext = (ServiceHandlerContext) arg;
                    context = argContext;
                    break;
                }
            }
        }
        if (context == null) {
            return joinPoint.proceed(); // 如果没有找到context，正常执行
        }
        boolean absent = idempotentService.idempotentVerify(context, idempotent.expire());
        if (absent) {
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, ExceptionEnums.S0005.getEMsg());
        }
        return joinPoint.proceed();
    }
}
