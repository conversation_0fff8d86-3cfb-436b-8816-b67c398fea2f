package com.basiscotton.common.idempotence.cache;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.Expiry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: WebRequestDigestCacheBuilder.java
 * @Description: web请求摘要缓存
 * @date 2025/9/2 15:38
 */
@Component
@Slf4j
public class WebRequestDigestCacheBuilder {
    // 正确注入缓存实例

    @Primary
    @Bean
    public Cache<String, TimedValue<String>> webRequestDigestCache(){
        Cache<String, TimedValue<String>> cache = Caffeine.newBuilder()
                .initialCapacity(1000)
                .maximumSize(1000000)
                .expireAfter(new CaffeineExpiry())
                .recordStats()
                .build();
        log.info("web请求摘要缓存构建完成！");
        return cache;
    }

    class CaffeineExpiry implements  Expiry<String, TimedValue<String>> {
        @Override
        public long expireAfterCreate(String key, TimedValue<String> value, long currentTime) {
            long duration = value.getExpiryTimeNanos() - currentTime;
            System.out.println("expireAfterCreate" +key+":"+ duration);
            // 确保不超过最大过期时间（例如5分钟）
            return  Math.max(0, duration);
        }

        @Override
        public long expireAfterUpdate(String key, TimedValue<String> value,
        long currentTime, long currentDuration) {
            long duration = value.getExpiryTimeNanos() - currentTime;
            System.out.println("expireAfterUpdate" +key+":"+ duration);
            return  Math.max(0, duration);
        }

        @Override
        public long expireAfterRead(String key, TimedValue<String> value,
        long currentTime, long currentDuration) {
            System.out.println("expireAfterRead" +key+":"+ currentDuration);
            return  Math.max(0, currentDuration);
        }
    }
}
