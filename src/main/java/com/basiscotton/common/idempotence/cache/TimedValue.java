package com.basiscotton.common.idempotence.cache;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: TimedValue.java
 * @Description:
 * @date 2025/9/9 20:18
 */
public class TimedValue<S> {
    private final String value;
    private final long expiryTimeNanos;

    public TimedValue(String value, long durationNanos) {
        this.value = value;
        this.expiryTimeNanos = System.nanoTime() + durationNanos;
    }

    public String getValue() {
        return value;
    }

    public long getExpiryTimeNanos() {
        return expiryTimeNanos;
    }
}
