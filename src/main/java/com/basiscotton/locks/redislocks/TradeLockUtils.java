package com.basiscotton.locks.redislocks;

import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.harry.dandelion.framework.cache.ICache;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.common.ILockService;
import org.harry.dandelion.framework.core.common.RuntimeContext;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.function.Function;

/**
 * @description 锁工具类
 * <AUTHOR>
 * @date 2024/11/14 11:43
 * @version 1.0.0
 */
@Component
@NoArgsConstructor
@Slf4j
public class TradeLockUtils {


    @Resource
    protected RedissonClient redissonclient;

    @Resource
    private ILockService lockService;

    @Resource(name="basisTradeBusinessLock")
    private ICache cache;


    private static final Long TIMEOUT_TIME = 30000L;
    private static final Long RETRY_INTERVAL_TIME = 0L;

    /**
     * 排他锁
     * @param keyList key集合
     */
    public void addLock(List<String> keyList) {
        keyList.forEach(key -> {
            RLock lock = redissonclient.getLock(key);
            Function<Object, Object> clearFunc = Object -> {
                log.debug("清理锁: key={}", key);
                if (lock != null && lock.isHeldByCurrentThread()){
                    lock.unlock();
                    log.debug("锁已释放: key={}", key);
                }
                return null;
            };
            RuntimeContext.addClearFunction(key, clearFunc);
            boolean lockResult = lock.tryLock();
            if (!lockResult) {
                throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "选择的数据中存在正在处理中的数据，请等待数据处理完成！");
            }
        });
    }

    /**
     * 排他锁
     * @param key key
     */
    public void addLock(String key,boolean cacheStatus) {

        RLock lock = redissonclient.getLock(key);
        boolean lockResult = lock.tryLock();
        if (cacheStatus) this.addCache(key  );

        Function<Object, Object> clearFunc = Object -> {
            log.debug("清理锁和缓存: key={}", key);
            if (lock != null && lock.isHeldByCurrentThread()){
                lock.unlock();
                log.debug("锁已释放: key={}", key);
            }
            if (cache.exists(key)) {
                cache.evict(key);
                log.debug("缓存已清理: key={}", key);
            }
            return null;
        };
        RuntimeContext.addClearFunction(key, clearFunc);
        if (!lockResult) {
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "选择的数据正在处理中，请勿重复提交！");
        }


    }


    /**
     * 等待锁
     *
     * @param key key
     */
    public void addWaitLock(String key, Long timeout, Long retryIntervalTime) {

        lockService.lock(key, timeout, retryIntervalTime);
    }

    /**
     * 等待锁
     *
     * @param keyList key 列表
     */
    public void addWaitLock(List<String> keyList, Long timeout, Long retryIntervalTime) {
        for (String key : keyList) {
            lockService.lock(key, timeout, retryIntervalTime);
        }
    }

    /**
     * 等待锁
     * @param key key
     */
    public void unWaitLock(String key) {
        lockService.unLock(key);
    }

    /**
     * 等待锁
     * @param keyList key 列表
     */
    public void unWaitLock(List<String> keyList) {
        for (String key : keyList) {
            lockService.unLock(key);
        }
    }


    /**
     * 添加缓存
     * @param key key
     */
    public void addCache(String key) {
        // 加缓存
        cache.put(key, "start");
    }

    public boolean existsKey(String key) {
        return cache.exists(key);
    }




}
