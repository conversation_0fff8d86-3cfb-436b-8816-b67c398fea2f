package com.basiscotton.locks.redislocks;

import org.harry.dandelion.framework.core.utils.StringUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;


@Service
public class BusinessLockService {

    @Resource
    private TradeLockUtils tradeLockUtils;

    public void addLock(boolean cacheStatus, String businessId) {
        if (StringUtil.isNotEmpty(businessId)) {
            tradeLockUtils.addLock(_key(businessId),cacheStatus);
        }
    }

    public void addLock(List<String> businessIdList) {
        List<String> lockKeyList = businessIdList.stream()
                .map(id -> _key(id))
                .collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());;
        tradeLockUtils.addLock(lockKeyList);

    }

    public void addWaitLock(String businessId, Long timeout, Long retryIntervalTim) {
        tradeLockUtils.addWaitLock(businessId, timeout, retryIntervalTim);
    }

    public void unWaitLock(String businessId) {
        tradeLockUtils.unWaitLock(businessId);
    }

    private String _key(String key) {
        return "basiscotton:" + key;
    }
}
