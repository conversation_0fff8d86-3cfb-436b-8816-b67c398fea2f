package com.basiscotton.locks.reentrantlocks.model;

import java.util.concurrent.locks.ReentrantLock;

/**
 * Date: 2024/3/19 10:02
 * Description: 资金账户类，仅用于锁实现
 *
 * <AUTHOR>
 */
public class FundsAccount {

    //构建方法
    public FundsAccount(String clientAccountCode) {
        this.clientAccountCode = clientAccountCode;
    }

    /**客户资金账户代码*/
    private String clientAccountCode;

    /**锁对象,公平锁(同时避免等待线程同时唤醒),禁止序列化;*/
    public ReentrantLock accountLock = new ReentrantLock(true);


}
