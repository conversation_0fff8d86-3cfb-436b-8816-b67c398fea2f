package com.basiscotton.locks.reentrantlocks.cache;


import com.basiscotton.locks.reentrantlocks.cache.constant.CacheEnum;
import com.basiscotton.locks.reentrantlocks.model.FundsAccount;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * Date: 2024/3/19 16:08
 * Description: 资金账户锁缓存
 *
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class FundsAccountCacheBuilder {
    @Primary
    @Bean
    public Cache<String, FundsAccount> fundsAccountCache(){
        Cache<String, FundsAccount> cache = Caffeine.newBuilder()
                .initialCapacity(CacheEnum.FundsAccountLockCache.getInitialCapacity())
                .maximumSize(CacheEnum.FundsAccountLockCache.getMaximumSize())
               //不过期.expireAfterAccess(CacheEnum.FundsAccountLockCache.getExpires(), TimeUnit.SECONDS)
                .recordStats()
                .build();
        log.info("资金账户锁缓存构建完成！");
        return cache;
    }


}
