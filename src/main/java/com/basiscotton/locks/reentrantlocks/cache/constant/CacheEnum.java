package com.basiscotton.locks.reentrantlocks.cache.constant;

/**
 * Date: 2024/3/26 11:38
 * Description: 缓存配置项的枚举类
 *
 * <AUTHOR>
 */
public enum CacheEnum {
    //资金账户锁缓存
    FundsAccountLockCache("fundsAccountLockCache", CacheConstants.EXPIRES_NO,500,1000),

    ;



    private String name;
    private long expire;

    private int initialCapacity;

    private int maximumSize;

    CacheEnum(String name, long expire,int initialCapacity, int maximumSize){
        this.name = name;
        this.expire = expire;
        this.initialCapacity = initialCapacity;
        this.maximumSize = maximumSize;
    }

     String getName() {
        return name;
    }

     public long getExpires() {
        return expire;
    }

    public int getInitialCapacity() {
        return initialCapacity;
    }

     public int getMaximumSize() {
        return maximumSize;
    }
}
