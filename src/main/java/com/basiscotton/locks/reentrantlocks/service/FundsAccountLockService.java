package com.basiscotton.locks.reentrantlocks.service;

import com.basiscotton.locks.reentrantlocks.constant.LockConstants;
import com.basiscotton.locks.reentrantlocks.model.FundsAccount;
import com.github.benmanes.caffeine.cache.Cache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;
import java.util.stream.Stream;
/**
 * Date: 2024/3/19 14:07
 * Description: 资金账户锁服务，基于jvm锁实现，借助caffeine缓存
 * 重点：为保证增加资金账户的唯一性，使用synchronized加锁后处理,原子性写入并不能保证对象不被覆盖
 * 实现
 * 1.加锁初始化资金账户
 * 2.对单一资金账户加锁,原子性在缓存中增加单一资金账户
 * 3.对单一资金账户解锁
 * 4.对双资金账户加锁;适应资金冻结行为，排序方法，加解锁上对账户，无论正序还是倒序，均无法完全照顾到时间公平性
 *   反而是正序账户的（账户对）更优先，但为避免锁的混乱，正序加锁，正序解锁
 *   ReentrantLock为了减少性能消耗，仍设置为公平锁
 * 5.对双资金账户解锁 适应资金解冻行为，
 * 6、加锁单一账户，带超时时间
 * 7、加锁双账户，带超时时间
 * 8、清除缓存
 * <AUTHOR>
 */
@Component
@Slf4j
public class FundsAccountLockService {

    @Resource
    Cache<String, FundsAccount> fundsAccountCache;

    /**
     * 1.初始化所有资金账户,加锁执行
     * 该方法会为指定的客户账户创建一个资金账户，并将其存入缓存中，以便后续处理。
     */
    public synchronized void initloanFundsAccount(List<String> list) {
        //初始化资金账户
        if (list == null || list.isEmpty()) {
            throw new IllegalArgumentException("客户资金账户列表不能为空。");

        }

        if (fundsAccountCache.stats().loadCount() > 0) {
            throw new IllegalArgumentException("客户资金账户初始化前，请先清空。");
        }
        list.stream().forEach(clientAccountCode -> {
            try {
                FundsAccount fundsAccount = new FundsAccount(clientAccountCode);
                fundsAccountCache.put(clientAccountCode, fundsAccount);
            } catch (Exception e) {
                log.error("初始化资金账户缓存失败。", e);
                throw new RuntimeException("初始化资金账户失败。");
            }
        });
        log.info("初始化资金账户成功。");
    }

    /**
     * 对单一资金账户加锁，线程安全
     * 加锁实现,后面要紧跟try块，及临界区代码
     * @param clientAccountCode
     */
    public void lockFundsAccount(String clientAccountCode) {
        FundsAccount fundsAccount = fundsAccountCache.get(clientAccountCode, k -> new FundsAccount(clientAccountCode));
        fundsAccount.accountLock.lock();
    }

    /**
     * 5.对单一资金账户解锁
     *
     * @param clientAccountCode
     */
    public void unlockFundsAccount(String clientAccountCode) {
        FundsAccount fundsAccount = fundsAccountCache.getIfPresent(clientAccountCode);
        if (fundsAccount != null) {

            // 解锁实现,要求在业务代码的finally块中调用。
            // 注意：解锁操作必须执行，否则线程会一直持有锁
            // 加锁的代码和对应的解锁的代码必须定义在同一个类中
            // 防御性编码，防止lock前出现异常(缓存)，finally块中解锁失败；这样lock在try块前最后行或块中第一行都可以
            if (fundsAccount.accountLock.isHeldByCurrentThread()) {
                // System.out.println(Thread.currentThread().getId() + "解锁" + fundsAccount.hashCode() + "成功" + System.nanoTime());
                fundsAccount.accountLock.unlock();
            }
        } else {
            log.error("资金账户不存在，资金账户：{}", clientAccountCode);
            throw new RuntimeException("资金账户不存在。");
        }

    }


    /**
     * 对双资金账户加锁
     *
     * @param clientAccountCode1
     * @param clientAccountCode2
     */
    public void lockFundsAccount(String clientAccountCode1, String clientAccountCode2) {
        if (clientAccountCode1.equals(clientAccountCode2)) {
            throw new RuntimeException("资金账户不能相同。");
        }
        //排序加锁
        try {
            Stream stream = Stream.of(clientAccountCode1, clientAccountCode2).sorted();
            stream.forEach(e -> {
                FundsAccount fundsAccount = fundsAccountCache.get(e.toString(), k -> new FundsAccount(e.toString()));
                fundsAccount.accountLock.lock();
                //System.out.println(Thread.currentThread().getId() + "加锁" + fundsAccount.hashCode() + "成功" + System.nanoTime());
            });
        } catch (Exception e) {
            log.error("资金账户加锁失败：{},{}。", clientAccountCode1,clientAccountCode2);
            throw new RuntimeException("资金账户加锁失败。");
        }
    }

    /**
     * 对双资金账户进行解锁
     *
     * @param clientAccountCode1
     * @param clientAccountCode2
     */
    public void unlockFundsAccount(String clientAccountCode1, String clientAccountCode2) {

        if (clientAccountCode1.equals(clientAccountCode2)) {
            throw new RuntimeException("资金账户不能相同。");
        }
        //排序解锁
        try {
            Stream stream = Stream.of(clientAccountCode1, clientAccountCode2).sorted();
            stream.forEach(e -> {
                FundsAccount fundsAccount = fundsAccountCache.getIfPresent(e.toString());
                if (fundsAccount != null) {
                    //System.out.println(Thread.currentThread().getId() + "解锁" + fundsAccount.hashCode() + "成功" + System.nanoTime());
                    if (fundsAccount.accountLock.isHeldByCurrentThread()) {
                        fundsAccount.accountLock.unlock();
                    }
                } else {
                    throw new RuntimeException("资金账户不存在。");
                }
            });

        } catch (Exception e) {
            log.error("资金账户加锁失败：{},{}。", clientAccountCode1,clientAccountCode2);
            throw new RuntimeException("资金账户解锁失败。");
        }
    }

    /**
     * 单一账户加锁，在限时内
     *
     * @param clientAccountCode
     * @return
     */
    public boolean lockFundsAccountWithTimeOut(String clientAccountCode, long timeout, TimeUnit timeUnit) {
        boolean lockAcquired = false;
        FundsAccount fundsAccount = fundsAccountCache.get(clientAccountCode, k -> new FundsAccount(clientAccountCode));
        try {
            lockAcquired = fundsAccount.accountLock.tryLock(timeout,timeUnit);
        }catch (InterruptedException e) {
            log.error("资金账户加锁失败：{}", clientAccountCode);
            throw new RuntimeException("资金账户加锁失败。");
        }
        return lockAcquired;
    }

    /**
     * 尝试为两个不同的资金账户加锁，确保在给定的时间内能够同时获取到这两个账户的锁。
     * 如果其中一个或两个账户相同，或者在指定超时时间内无法获取到任一账户的锁，则操作失败。
     *
     * @param clientAccountCode1 第一个资金账户代码
     * @param clientAccountCode2 第二个资金账户代码
     * @return 如果成功同时获取到两个账户的锁，则返回true；否则返回false。
     */
    public boolean lockFundsAccountWithTimeOut(String clientAccountCode1, String clientAccountCode2) {
        // 检查两个账户代码是否相同，如果相同则抛出异常
        if (clientAccountCode1.equals(clientAccountCode2)) {
            throw new RuntimeException("资金账户不能相同。");
        }
        try {
            // 通过对账户代码进行排序，确保始终以相同的顺序尝试加锁
            List<String> list = Stream.of(clientAccountCode1, clientAccountCode2).sorted().collect(Collectors.toList());
            // 获取第一个账户的锁
            ReentrantLock lock1 = fundsAccountCache.get(list.get(0), k -> new FundsAccount(list.get(0))).accountLock;
            // 获取第二个账户的锁
            ReentrantLock lock2 = fundsAccountCache.get(list.get(1), k -> new FundsAccount(list.get(1))).accountLock;

            // 尝试获取第一个锁，如果超时则返回false
            boolean lock1Acquired = lock1.tryLock(LockConstants.timeout, LockConstants.unit);
            if (!lock1Acquired) {
                return false;
            }

            // 在持有第一个锁的情况下尝试获取第二个锁，如果超时则释放第一个锁并返回false
            boolean lock2Acquired = lock2.tryLock(LockConstants.timeout, LockConstants.unit);
            if (!lock2Acquired) {
                if (lock1.isHeldByCurrentThread()) {
                    lock1.unlock();
                }
                return false;
            }

            // 如果成功获取到两个锁，返回true
            return true;

        } catch (InterruptedException e) {
            // 如果操作被中断，记录错误并抛出一个运行时异常
            log.error("资金账户加锁失败：{},{}。", clientAccountCode1,clientAccountCode2);
            throw new RuntimeException("资金账户解锁失败。");
        }

    }

    public boolean lockFundsAccountWithTimeOut(String clientAccountCode1, String clientAccountCode2, long timeout, TimeUnit timeUnit) {
        // 检查两个账户代码是否相同，如果相同则抛出异常
        if (clientAccountCode1.equals(clientAccountCode2)) {
            throw new RuntimeException("资金账户不能相同。");
        }
        try {
            // 通过对账户代码进行排序，确保始终以相同的顺序尝试加锁
            List<String> list = Stream.of(clientAccountCode1, clientAccountCode2).sorted().collect(Collectors.toList());
            // 获取第一个账户的锁
            ReentrantLock lock1 = fundsAccountCache.get(list.get(0), k -> new FundsAccount(list.get(0))).accountLock;
            // 获取第二个账户的锁
            ReentrantLock lock2 = fundsAccountCache.get(list.get(1), k -> new FundsAccount(list.get(1))).accountLock;

            // 尝试获取第一个锁，如果超时则返回false
            boolean lock1Acquired = lock1.tryLock(timeout, timeUnit);
            if (!lock1Acquired) {
                return false;
            }

            // 在持有第一个锁的情况下尝试获取第二个锁，如果超时则释放第一个锁并返回false
            boolean lock2Acquired = lock2.tryLock(timeout, timeUnit);
            if (!lock2Acquired) {
                if (lock1.isHeldByCurrentThread()) {
                    lock1.unlock();
                }
                return false;
            }

            // 如果成功获取到两个锁，返回true
            return true;

        } catch (InterruptedException e) {
            // 如果操作被中断，记录错误并抛出一个运行时异常
            log.error("资金账户加锁失败：{},{}。", clientAccountCode1,clientAccountCode2);
            throw new RuntimeException("资金账户解锁失败。");
        }

    }


    public void clearFundsAccountCache() {
        fundsAccountCache.invalidateAll();
    }
}