package com.basiscotton;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableAsync;

import java.net.InetAddress;
import java.net.UnknownHostException;

@Slf4j
@EnableAsync
@SpringBootApplication(exclude = FreeMarkerAutoConfiguration.class)
public class BasiscottonTradeServiceSystemApplication extends SpringBootServletInitializer
{
	
	@Override
	protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(BasiscottonTradeServiceSystemApplication.class);
    }
	
	public static void main(String[] args) throws UnknownHostException{
		ConfigurableApplicationContext application = SpringApplication.run(BasiscottonTradeServiceSystemApplication.class, args);
		Environment env = application.getEnvironment();
		String ip = InetAddress.getLocalHost().getHostAddress();
		String port = env.getProperty("server.port");
		String path = env.getProperty("server.servlet.context-path");
		log.info("\n----------------------------------------------------------\n\t" +
			"Application CncottonTradeService   is running! Access URLs:\n\t" +
			"Local(本机后台服务访问地址): \thttp://localhost:" + port + path + "/\n\t" +
			"External(外网服务访问地址): \thttp://" + ip + ":" + port + path + "/\n\t" +
			"Swagger-UI(API在线调试文档): \thttp://" + ip + ":" + port + path + "/doc.html#/home\n\t"+
			"----------------------------------------------------------");
	}
}

