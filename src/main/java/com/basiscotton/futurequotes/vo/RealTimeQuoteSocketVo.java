package com.basiscotton.futurequotes.vo;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: RealTimeQuoteVo.java
 * @Description: 实时行情VO类，封装了行情数据；hq=hot quotes
 * @date 2025/5/16 16:15
 */
@Data
public class RealTimeQuoteSocketVo implements Serializable {

    /**
     * 合约编码
     * 开盘价
     * 最新价
     * 最高价
     * 最低价
     * 收盘价
     * 结算价
     * 涨停板
     * 跌停板
     * 总成交量
     * 最后修改时间
     */
    @ApiModelProperty(value = "合约编码")
    @JSONField(name = "合约编码")
    private String futureCode;

    @ApiModelProperty(value = "开盘价")
    @JSONField(name = "开盘价")
    private BigDecimal openPrice;

    @ApiModelProperty(value = "最新价")
    @JSONField(name = "最新价")
    private BigDecimal lastPrice;

    @ApiModelProperty(value = "最高价")
    @JSONField(name = "最高价")
    private BigDecimal highPrice;

    @ApiModelProperty(value = "最低价")
    @JSONField(name = "最低价")
    private BigDecimal lowPrice;

    @ApiModelProperty(value = "收盘价")
    @JSONField(name = "收盘价")
    private BigDecimal closePrice;

    @ApiModelProperty(value = "结算价")
    @JSONField(name = "结算价")
    private BigDecimal clearPrice;

    @ApiModelProperty(value = "涨停板")
    @JSONField(name = "涨停板")
    private BigDecimal highLimit;

    @ApiModelProperty(value = "跌停板")
    @JSONField(name = "跌停板")
    private BigDecimal lowLimit;

    @ApiModelProperty(value = "总成交量")
    @JSONField(name = "总成交量")
    private BigDecimal totalVolume;

    @ApiModelProperty(value = "最后修改时间")
    @JSONField(name = "最后修改时间")
    private String updateTime;

}
