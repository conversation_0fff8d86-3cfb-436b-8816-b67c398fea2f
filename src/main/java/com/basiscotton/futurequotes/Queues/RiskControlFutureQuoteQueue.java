package com.basiscotton.futurequotes.Queues;

import com.basiscotton.futurequotes.vo.RealTimeQuoteVo;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: RiskControlFutureQuoteQueue.java
 * @Description: 先交割后点价风控用期货行情
 * @date 2025/8/19 18:31
 */
@Component
public class RiskControlFutureQuoteQueue {
    private static final FutureQuoteQueue riskControlFutureQuoteQueue = new FutureQuoteQueue();
    public void addFirst(RealTimeQuoteVo realTimeQuoteVo) {
        riskControlFutureQuoteQueue.addFirst(realTimeQuoteVo);
    }
    public RealTimeQuoteVo pollLast() {
        return riskControlFutureQuoteQueue.pollLast();
    }
    public void clear() {
        riskControlFutureQuoteQueue.clear();
    }
}
