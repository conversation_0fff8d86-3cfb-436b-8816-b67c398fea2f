package com.basiscotton.futurequotes.Queues;

import com.basiscotton.futurequotes.vo.RealTimeQuoteVo;

import java.util.concurrent.LinkedBlockingDeque;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: FutureQuoteQueue.java
 * @Description: 行情数据队列
 * @date 2025/8/19 17:00
 */
public class FutureQuoteQueue {
    //无界链表队列，存放行情数据， 使用分离锁：头节点和尾节点操作可以并发进行
    private final LinkedBlockingDeque<RealTimeQuoteVo> deque = new LinkedBlockingDeque<>();

    //从头上添加数据
    public void addFirst(RealTimeQuoteVo realTimeQuoteVo) {
        deque.addFirst(realTimeQuoteVo);
    }

    //从尾部取出数据
    public RealTimeQuoteVo pollLast() {
        return deque.pollLast();
    }

    //清空数据，慎用
    public void clear() {
        deque.clear();
    }


}
