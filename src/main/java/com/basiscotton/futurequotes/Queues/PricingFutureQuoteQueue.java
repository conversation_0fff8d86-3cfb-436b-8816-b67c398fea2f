package com.basiscotton.futurequotes.Queues;

import com.basiscotton.futurequotes.vo.RealTimeQuoteVo;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: PricingFutureQuoteQueue.java
 * @Description: 点价用期货行情队列
 * @date 2025/8/19 18:27
 */
@Component
public class PricingFutureQuoteQueue {

    private static final FutureQuoteQueue pricingFutureQuoteQueue = new FutureQuoteQueue();

    public void addFirst(RealTimeQuoteVo realTimeQuoteVo) {
        pricingFutureQuoteQueue.addFirst(realTimeQuoteVo);
    }
    public RealTimeQuoteVo pollLast() {
        return pricingFutureQuoteQueue.pollLast();
    }
    public void clear() {
        pricingFutureQuoteQueue.clear();
    }
}
