package com.basiscotton.futurequotes.task;

import com.basiscotton.futurequotes.Queues.PricingFutureQuoteQueue;
import com.basiscotton.futurequotes.Queues.RiskControlFutureQuoteQueue;
import com.basiscotton.futurequotes.webApi.RequestFutureQuotes;
import com.basiscotton.futurequotes.vo.RealTimeQuoteVo;
import com.github.benmanes.caffeine.cache.Cache;
import lombok.extern.slf4j.Slf4j;
import org.harry.dandelion.framework.scheduler.quartz.job.DandelionJob;
import org.quartz.JobExecutionContext;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: AutoGetQuotesTask.java
 * @Description: 测试环境自动获取行情的定时任务-生产环境不运行
 * @date 2025/5/22 11:45
 */
@Slf4j
public class AutoGetFutureQuotesTask extends DandelionJob {
    @Resource
    private RequestFutureQuotes requestFutureQuotes;

    @Resource
    private Cache<String, RealTimeQuoteVo> futureQuotesCache;
    @Resource
    private PricingFutureQuoteQueue pricingFutureQuoteQueue;
    @Resource
    private RiskControlFutureQuoteQueue riskControlFutureQuoteQueue;

    @Override
    public void process(JobExecutionContext context) throws Exception {
        log.info("AutoGetQuotesTask start");
        List<RealTimeQuoteVo> realTimeQuoteVoList = requestFutureQuotes.getQuotesDataJson();
        if (realTimeQuoteVoList != null && realTimeQuoteVoList.size() > 0) {
            for (RealTimeQuoteVo realTimeQuoteVo : realTimeQuoteVoList) {
                futureQuotesCache.put(realTimeQuoteVo.getFutureCode(), realTimeQuoteVo);
                pricingFutureQuoteQueue.addFirst(realTimeQuoteVo);
                riskControlFutureQuoteQueue.addFirst(realTimeQuoteVo);
                log.info("AutoGetFutureQuotesTask instrumentId:{},lastPrice:{}", realTimeQuoteVo.getFutureCode(), realTimeQuoteVo.getLastPrice());
            }
        }
    }
}
