package com.basiscotton.futurequotes.socketApi;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @version V1.0
 * @Title: NettyClientStarter.java
 * @Description: netty的socket客户端的启动类
 * @date 2025/5/18 21:40
 */
@Component
public class MoniSocketServerStarter {//implements CommandLineRunner

   @Value("${socket.server.host}")
   private String host;

   @Value("${socket.server.port}")
   private int port;


   public void run(String... args) {
    new Thread(() -> {
        MoniSocketServer server = new MoniSocketServer();
        server.start(port);
    }).start();
   }
  }
