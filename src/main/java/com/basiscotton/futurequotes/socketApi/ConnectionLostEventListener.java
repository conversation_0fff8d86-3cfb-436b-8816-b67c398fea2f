package com.basiscotton.futurequotes.socketApi;

import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: ConnectionLostEventListener.java
 * @Description: 监听器处理重连逻辑
 * @date 2025/6/10 13:59
 */

@Component
public class ConnectionLostEventListener {

    @Resource
    private FutureQuoteNettyClient futureQuoteNettyClient;

    @Async
    @EventListener
    public void handleConnectionLost(ConnectionLostEvent event) {
        futureQuoteNettyClient.onConnectionLost(); // 触发重连
    }
}
