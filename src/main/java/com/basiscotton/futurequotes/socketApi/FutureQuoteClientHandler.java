package com.basiscotton.futurequotes.socketApi;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.basiscotton.futurequotes.Queues.PricingFutureQuoteQueue;
import com.basiscotton.futurequotes.Queues.RiskControlFutureQuoteQueue;
import com.github.benmanes.caffeine.cache.Cache;
import com.basiscotton.futurequotes.vo.RealTimeQuoteSocketVo;
import com.basiscotton.futurequotes.vo.RealTimeQuoteVo;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: FutureQuoteClientHandler.java
 * @Description: 客户端处理类
 * @date 2025/5/18 21:35
 * 注：增加接收时间，收到新行情时判断时间间隔，超1小时，则清一遍缓存，将旧合约品种清除掉？
 */
@ChannelHandler.Sharable
@Slf4j
@Component
public class FutureQuoteClientHandler extends SimpleChannelInboundHandler<ByteBuf> {

    @Resource
    private Cache<String, RealTimeQuoteVo> futureQuotesCache;
    @Resource
    private ApplicationEventPublisher eventPublisher;
    @Resource
    private PricingFutureQuoteQueue pricingFutureQuoteQueue;
    @Resource
    private RiskControlFutureQuoteQueue riskControlFutureQuoteQueue;

    //最后接收时间  long型的时间戳
   // private static long lastReceiveTime = System.currentTimeMillis();

    @Override
    public void channelActive(ChannelHandlerContext ctx) {
        log.info("客户端已连接到服务端");
    }

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, ByteBuf msg) {
        try {

            byte[] bytes = new byte[msg.readableBytes()];
            msg.readBytes(bytes);
            String response = new String(bytes);
            log.info("收到服务端消息: " + response);

            List<RealTimeQuoteVo> realTimeQuoteVoList = new ArrayList<>();
            realTimeQuoteVoList = convertJsonStrToRealTimeQuoteVo(response);
            if (realTimeQuoteVoList.size() > 0) {
                // 缓存数据
                for (RealTimeQuoteVo quoteVo : realTimeQuoteVoList) {
                    //如果最新价为0而，则忽略-在最后交易阶段可能会出现0的行情
                    if (quoteVo.getLastPrice().compareTo(BigDecimal.ZERO) != 0) {
                        futureQuotesCache.put(quoteVo.getFutureCode(), quoteVo);
                        pricingFutureQuoteQueue.addFirst(quoteVo);
                        riskControlFutureQuoteQueue.addFirst(quoteVo);
                    }
                    //log.info("缓存数据：" + quoteVo.toString());
                }
            }
        }  catch (Exception e) {
            e.printStackTrace();
        }
    }
    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
        log.warn("与服务端连接断开");
        eventPublisher.publishEvent(new ConnectionLostEvent(this)); // 自定义事件
        ctx.fireChannelInactive();
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        cause.printStackTrace();
        ctx.close();
    }

    /**
     * 将jsonStr转换成RealTimeQuoteVo
     */
    public List<RealTimeQuoteVo> convertJsonStrToRealTimeQuoteVo(String jsonStrs) {
        List<RealTimeQuoteVo> realTimeQuoteVoList = new ArrayList<>();
        //jsonStr按回车进行分组
        String[] jsonStrList = jsonStrs.split("\n");
        for(String jsonStr : jsonStrList) {
            // 解析第一个JSON字符串
            JSONObject jsonObject = JSON.parseObject(jsonStr);
            //System.out.println("Message: " + jsonObject.getString("msg"));
            JSONArray dataArray = jsonObject.getJSONArray("data");
            for (Object obj : dataArray) {
                // 将JSON对象转换为目标类对象
                RealTimeQuoteSocketVo quoteSocketVo = JSON.toJavaObject((JSONObject) obj, RealTimeQuoteSocketVo.class);
                RealTimeQuoteVo quoteVo = new RealTimeQuoteVo();
                //  将数据拷贝给quoteVo
                BeanUtils.copyProperties(quoteSocketVo,quoteVo);
                realTimeQuoteVoList.add(quoteVo);
            }
        }
        return realTimeQuoteVoList;
    }


}
