package com.basiscotton.futurequotes.socketApi;

import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.net.*;
import java.util.*;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: moniSocketServer.java
 * @Description: 模拟SocketServer的服务端
 * @date 2025/5/18 12:43
 */
@Slf4j
public class MoniSocketServer {
    // 存储所有活跃的客户端输出流
    private static final List<PrintWriter> clientWriters = Collections.synchronizedList(new ArrayList<>());
    //private static final String MESSAGE = "{\"msg\":\"正常\",\"data\":[{\"合约编码\":\"CF601\",\"最新价\":13200,\"结算价\":13120,\"涨停板\":12320,\"跌停板\":13900,\"最后修改时间\":\"09:00:00\"}]}";
    private static final String MESSAGE1 = "{\"msg\":\"正常\",\"data\":[{\"合约编码\":\"CF601\",\"最新价\":13200,\"结算价\":13120,\"涨停板\":12320,\"跌停板\":13900,\"最高价\":16598,\"最低价\":13911,\"最后修改时间\":\"09:00:00\"}]}";
    private static final String MESSAGE2 = "{\"msg\":\"正常\",\"data\":[{\"合约编码\":\"CF602\",\"最新价\":16500,\"结算价\":11860,\"涨停板\":16560,\"跌停板\":13950,\"最高价\":16599,\"最低价\":13912,\"最后修改时间\":\"09:01:00\"}]}";
    public static void main(String[] args) {
        int port = 18081; // 监听的端口号
        // 启动广播消息的线程
        new Thread(monitoredMessageBroadcaster).start();
        try (ServerSocket serverSocket = new ServerSocket(port)) {
            log.info("服务器已启动，等待客户端连接...");
            while (true) {
                Socket clientSocket = serverSocket.accept(); // 接受客户端连接
                log.info("客户端已连接: " + clientSocket.getInetAddress());
                // 获取输出流并加入列表
                PrintWriter out = new PrintWriter(clientSocket.getOutputStream(), true);
                clientWriters.add(out);
                // 启动线程处理客户端通信（可选）
                new ClientHandler(clientSocket, out).start();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
    public void start(int port) {
        // 启动广播消息的线程
        new Thread(monitoredMessageBroadcaster).start();
        try (ServerSocket serverSocket = new ServerSocket(port)) {
            log.info("服务器已启动，等待客户端连接...");
            while (true) {
                Socket clientSocket = serverSocket.accept(); // 接受客户端连接
                log.info("客户端已连接: " + clientSocket.getInetAddress());
                // 获取输出流并加入列表
                PrintWriter out = new PrintWriter(clientSocket.getOutputStream(), true);
                clientWriters.add(out);
                // 启动线程处理客户端通信（可选）
                new ClientHandler(clientSocket, out).start();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    // 广播消息的线程任务
    private static final Runnable monitoredMessageBroadcaster = () -> {
        while (true) {
            synchronized (clientWriters) {
                List<PrintWriter> toRemove = new ArrayList<>();
                for (PrintWriter writer : clientWriters) {
                    if (writer.checkError()) {
                        // 如果出现错误，标记为移除
                        toRemove.add(writer);
                    } else {
                        writer.println(MESSAGE1); // 发送消息
                        writer.println(MESSAGE2);
                    }
                }
                clientWriters.removeAll(toRemove); // 移除无效连接
            }

            try {
                Thread.sleep(500); // 每200毫秒发送一次
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    };

    static class ClientHandler extends Thread {
        private final Socket clientSocket;
        private final PrintWriter out;

        public ClientHandler(Socket socket, PrintWriter out) {
            this.clientSocket = socket;
            this.out = out;
        }

        public void run() {
            try (
                BufferedReader in = new BufferedReader(new InputStreamReader(clientSocket.getInputStream()))
            ) {
                String inputLine;
                while ((inputLine = in.readLine()) != null) {
                    log.info("收到客户端"+clientSocket.getInetAddress()+"消息: " + inputLine);
                }
            } catch (IOException e) {
                log.info("客户端断开连接: " + clientSocket.getInetAddress());
            } finally {
                try {
                    clientSocket.close(); // 关闭客户端连接
                } catch (IOException e) {
                    e.printStackTrace();
                }
                clientWriters.remove(out); // 从列表中移除该客户端
            }
        }
    }
}
