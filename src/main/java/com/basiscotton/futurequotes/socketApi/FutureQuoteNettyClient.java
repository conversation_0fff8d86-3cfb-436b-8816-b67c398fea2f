package com.basiscotton.futurequotes.socketApi;
import io.netty.bootstrap.Bootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.codec.LineBasedFrameDecoder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: FutureQuoteNettyClient.java
 * @Description: netty客户端集成
 * @date 2025/5/18 21:38
 */
@Slf4j
@Component
public class FutureQuoteNettyClient {

    @Value("${socket.server.host}")
    private String host;

    @Value("${socket.server.port}")
    private int port;

    @Resource
    private FutureQuoteClientHandler futureQuoteClientHandler;

    // 用于控制重连间隔
    private final ScheduledExecutorService reconnectScheduler = Executors.newSingleThreadScheduledExecutor();
    private EventLoopGroup group;
    private volatile boolean isStopped = false;
    private volatile Channel channel;
    private Bootstrap bootstrap;

    public void start() throws Exception {
        if (isStopped) return;

        log.info("正在启动 Netty 客户端...");
        group = new NioEventLoopGroup();

        bootstrap = new Bootstrap();
        bootstrap.group(group)
                .channel(NioSocketChannel.class)
                .option(ChannelOption.SO_KEEPALIVE, true)
                .handler(new ChannelInitializer<SocketChannel>() {
                    @Override
                    protected void initChannel(SocketChannel ch) {
                        ch.pipeline().addLast(new LineBasedFrameDecoder(1024));
                        ch.pipeline().addLast(futureQuoteClientHandler);
                    }
                });

        connect();
    }
    private void connect() {
        if (isStopped) return;

        log.info("尝试连接服务端 {}:{}", host, port);
        bootstrap.connect(host, port).addListener((ChannelFutureListener) future -> {
            if (future.isSuccess()) {
                log.info("连接服务端成功");
                channel = future.channel();
            } else {
                log.warn("连接服务端失败，准备重试...");
                // 延迟3秒后重试
                reconnectScheduler.schedule(() -> connect(), 3, TimeUnit.SECONDS);
            }
        });
    }
    /**
     * 提供给外部调用的停止方法
     */
    public void stop() {
        isStopped = true;

        if (channel != null && channel.isActive()) {
            channel.close();
        }

        if (group != null) {
            group.shutdownGracefully();
        }

        log.info("Netty 客户端已关闭");
    }

    /**
     * 当连接断开时触发重连（可从 handler 调用）
     */
    public void onConnectionLost() {
        if (!isStopped) {
            log.warn("检测到连接断开，准备重新连接...");
            connect();
        }
    }
    public static void main(String[] args) throws Exception {
    new FutureQuoteNettyClient().start();
   }

}
