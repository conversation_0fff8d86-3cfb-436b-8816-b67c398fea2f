package com.basiscotton.futurequotes.socketApi;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: FutureQuoteNettyClientStarter.java
 * @Description: netty的socket客户端的启动类
 * @date 2025/5/18 21:40
 */
@Component
public class FutureQuoteNettyClientStarter implements CommandLineRunner {

  @Value("${socket.server.host}")
  private String host;

  @Resource
  private FutureQuoteNettyClient futureQuoteNettyClient;

   @Override
   public void run(String... args) {
       //测试环境将hosts配置为default,避免测试环境启动socketClient
       if (!host.equals("default")) {
           new Thread(() -> {
               try {
                   futureQuoteNettyClient.start();
               } catch (Exception e) {
                   e.printStackTrace();
               }
           }).start();
       }
     }
  }
