package com.basiscotton.futurequotes.webService;

import com.github.benmanes.caffeine.cache.Cache;
import com.basiscotton.futurequotes.vo.RealTimeQuoteVo;
import com.basiscotton.futurequotes.webApi.RequestFutureQuotes;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: GetQuotesTest.java
 * @Description: 测试环境手工获取期货行情
 * @date 2025/5/23 10:58
 */
@Service("futureQuotes.getFutureQuotesTest.1")
@ApiRequestObject(value = "测试环境手工获取期货行情", name = "getQuotesTest", groups = {"基差-行情"}, params = {
        @ApiParamMeta(key = "futureCode", desc = "合约编号", type = String.class)
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "realTimeQuoteVoList", desc = "行情信息", type = RealTimeQuoteVo.class, multipart = true),
})
public class GetFutureQuotesTest implements IBusinessService {
    @Resource
    private Cache<String, RealTimeQuoteVo> quotesCache;
    @Resource
    private RequestFutureQuotes requestFutureQuotes;
       @Override
       public void doVerify(ServiceHandlerContext context) {

       }

       @Override
       public void doWork(ServiceHandlerContext context) {

           String futureCode = context.getValueObject(String.class, "futureCode");
           log.info("AutoGetQuotesTask start");
           List<RealTimeQuoteVo> realTimeQuoteVoList = requestFutureQuotes.getQuotesDataJson();
           if (realTimeQuoteVoList != null && realTimeQuoteVoList.size() > 0) {
               for (RealTimeQuoteVo realTimeQuoteVo : realTimeQuoteVoList) {
                   quotesCache.put(realTimeQuoteVo.getFutureCode(), realTimeQuoteVo);
                   log.info("AutoGetQuotesTask instrumentId:{},lastPrice:{}", realTimeQuoteVo.getFutureCode(), realTimeQuoteVo.getLastPrice());
               }
           }

          //创建响应实体
          this.createSuccessResponse(context);
          context.getResponseBody().getDataSet().put("realTimeQuoteVoList",realTimeQuoteVoList);
       }
}
