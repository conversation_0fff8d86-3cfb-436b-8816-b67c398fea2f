package com.basiscotton.futurequotes.cache;

/**
 * Date: 2025/5/15 12:01
 * Description: 缓存配置项的枚举类
 *
 * <AUTHOR>
 */
public enum CacheEnum {
    //资金账户锁缓存
    //FundsAccountLockCache("fundsAccountLockCache", CacheConstants.EXPIRES_NO,500,1000),

    //棉花期货行情缓存
    CottonRTQCache("CottonRTQCache", CacheConstants.EXPIRES_NO,6,6),

    //本库交易码缓存
   // LTXCodeCache("LTXCodeCache", CacheConstants.EXPIRES_24_hour,500,10000),
    ;



    private String name;
    private long expire;

    private int initialCapacity;

    private int maximumSize;

    CacheEnum(String name, long expire,int initialCapacity, int maximumSize){
        this.name = name;
        this.expire = expire;
        this.initialCapacity = initialCapacity;
        this.maximumSize = maximumSize;
    }

     String getName() {
        return name;
    }

     public long getExpires() {
        return expire;
    }

    public int getInitialCapacity() {
        return initialCapacity;
    }

     public int getMaximumSize() {
        return maximumSize;
    }
}
