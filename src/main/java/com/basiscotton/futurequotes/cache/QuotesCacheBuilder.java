package com.basiscotton.futurequotes.cache;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.Expiry;
import com.basiscotton.futurequotes.vo.RealTimeQuoteVo;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.index.qual.NonNegative;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;


/**
 * <AUTHOR>
 * @version V1.0
 * @Title: hqCacheBuilder.java
 * @Description: 行情缓存服务
 * @date 2025/5/16 11:56
 */
@Component
@Slf4j
public class QuotesCacheBuilder {

    @Primary
    @Bean
    public Cache<String, RealTimeQuoteVo> futureQuotesCache(){

        Cache<String, RealTimeQuoteVo> cache = Caffeine.newBuilder()
                .initialCapacity(CacheEnum.CottonRTQCache.getInitialCapacity())
                .maximumSize(CacheEnum.CottonRTQCache.getMaximumSize())
                //.expireAfterAccess(CacheEnum.CottonRTQCache.getExpires(), TimeUnit.SECONDS)
                //.expireAfter(new CaffeineExpiry())
                .recordStats()
                .build();
        //cache.put(CacheConstants.prefix, new RealTimeQuoteVo());
        log.info("棉花期货行情缓存构建完成！");
        return cache;
    }

    /**
     * 重写caffeine的过期策略
     * 注意：caffeine默认long currentDuration为纳秒，否则可使用转换秒到纳秒转换：TimeUnit.SECONDS.toNanos（seconds）
     */
    class CaffeineExpiry implements Expiry<String, Object> {

        @Override
        public long expireAfterCreate(@NonNull String s, @NonNull Object o, long currentTime) {
            return getRemainingTimeOfDay();
        }

        @Override
        public long expireAfterUpdate(@NonNull String s, @NonNull Object o, long currentTime, @NonNegative long currentDuration) {
            return getRemainingTimeOfDay();
        }

        @Override
        public long expireAfterRead(@NonNull String s, @NonNull Object o, long currentTime, @NonNegative long currentDuration) {
            return currentDuration;
        }


    }
    /**
     * 获取当天剩余时间的纳秒数。
     * 此函数不接受任何参数。
     *
     * @return 当天剩余时间的纳秒数。
     */
    private static long getRemainingTimeOfDay() {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        // 将当前时间设定为当天的最后时刻
        LocalDateTime endOfDay = now.with(LocalTime.MAX);
        // 计算当前时间与当天最后时刻的时间差
        Duration duration = Duration.between(now, endOfDay);
        // 将时间差转换为纳秒并返回
        return duration.toNanos();
    }


}
