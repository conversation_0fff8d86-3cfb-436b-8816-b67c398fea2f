package com.basiscotton.futurequotes.webApi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.basiscotton.futurequotes.vo.RealTimeQuoteVo;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.message.response.ResponseMessage;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: RequestQuotes.java
 * @Description: 请求行情数据-http形式
 * @date 2025/5/22 15:00
 */
@Service
@Slf4j
public class RequestFutureQuotes {

    @Value("${futureQuotes.queryUrl}")
    private String queryUrl;
    @Value("${futureQuotes.account.username}")
    private String username;

    @Value("${futureQuotes.account.password}")
    private String password;

    public List<RealTimeQuoteVo> getQuotesDataJson(){
        OkHttpClient client = new OkHttpClient();
        String json = "{}";
        RequestBody body = RequestBody.create(MediaType.get("application/json; charset=utf-8"), json);
        //String credential = Credentials.basic(username, password);
        Request request = new Request.Builder()
                .url(queryUrl)
                .post(body)
                 //.header("Authorization", credential)
                .header("Content-Type", " application/json;charset=utf-8")
                .header("username", username)
                .header("password", password)
                .build();
        List<RealTimeQuoteVo> realTimeQuoteVoList = new ArrayList<>();
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful() && response.body() != null) {
                String queryInfo = response.body().string();
                log.debug("数据期货行情数据: {}", queryInfo);
                realTimeQuoteVoList = parseRealTimeQuoteVoList(queryInfo);
            }
        } catch (Exception e) {
            log.error("数据期货行情数据异常 异常原因:{} {}", e.getMessage(), e);
            e.printStackTrace();
        }
        //如果realTimeQuoteVoList,则生成默认数据
        /**
         *5.22号数据
         {"msg":"正常","data":[{"合约编码":"CF505","最新价":12820,"结算价":12675,"涨停板":11885,"跌停板":13405,"最后修改时间":"09:00:02"}]}
         {"msg":"正常","data":[{"合约编码":"CF507","最新价":12895,"结算价":12750,"涨停板":11945,"跌停板":13475,"最后修改时间":"09:00:02"}]}
         {"msg":"正常","data":[{"合约编码":"CF509","最新价":13100,"结算价":12970,"涨停板":12145,"跌停板":13705,"最后修改时间":"09:00:02"}]}
         {"msg":"正常","data":[{"合约编码":"CF511","最新价":13170,"结算价":13040,"涨停板":12235,"跌停板":13805,"最后修改时间":"09:00:02"}]}
         {"msg":"正常","data":[{"合约编码":"CF601","最新价":13265,"结算价":13125,"涨停板":12320,"跌停板":13900,"最后修改时间":"09:00:02"}]}
         {"msg":"正常","data":[{"合约编码":"CF603","最新价":13295,"结算价":13165,"涨停板":12310,"跌停板":13890,"最后修改时间":"09:00:02"}]}
         */
        if(realTimeQuoteVoList == null || realTimeQuoteVoList.size()==0){
            RealTimeQuoteVo realTimeQuoteVo = new RealTimeQuoteVo();
            realTimeQuoteVo.setFutureCode("CF505");
            realTimeQuoteVo.setLastPrice(new BigDecimal(12820));
            realTimeQuoteVo.setClearPrice(new BigDecimal(12675));
            realTimeQuoteVo.setHighLimit(new BigDecimal(11885));
            realTimeQuoteVo.setLowLimit(new BigDecimal(13405));
            realTimeQuoteVo.setUpdateTime(LocalDateTime.parse("09:00:02"));
            realTimeQuoteVoList.add(realTimeQuoteVo);
            realTimeQuoteVo = new RealTimeQuoteVo();
            realTimeQuoteVo.setFutureCode("CF507");
            realTimeQuoteVo.setLastPrice(new BigDecimal(12895));
            realTimeQuoteVo.setClearPrice(new BigDecimal(12750));
            realTimeQuoteVo.setHighLimit(new BigDecimal(11945));
            realTimeQuoteVo.setLowLimit(new BigDecimal(13475));
            realTimeQuoteVo.setUpdateTime(LocalDateTime.parse("09:00:02"));
            realTimeQuoteVoList.add(realTimeQuoteVo);
            realTimeQuoteVo = new RealTimeQuoteVo();
            realTimeQuoteVo.setFutureCode("CF509");
            realTimeQuoteVo.setLastPrice(new BigDecimal(13100));
            realTimeQuoteVo.setClearPrice(new BigDecimal(12970));
            realTimeQuoteVo.setHighLimit(new BigDecimal(12145));
            realTimeQuoteVo.setLowLimit(new BigDecimal(13705));
            realTimeQuoteVo.setUpdateTime(LocalDateTime.parse("09:00:02"));
            realTimeQuoteVoList.add(realTimeQuoteVo);
            realTimeQuoteVo = new RealTimeQuoteVo();
            realTimeQuoteVo.setFutureCode("CF511");
            realTimeQuoteVo.setLastPrice(new BigDecimal(13170));
            realTimeQuoteVo.setClearPrice(new BigDecimal(13040));
            realTimeQuoteVo.setHighLimit(new BigDecimal(12235));
            realTimeQuoteVo.setLowLimit(new BigDecimal(13805));
            realTimeQuoteVo.setUpdateTime(LocalDateTime.parse("09:00:02"));
            realTimeQuoteVoList.add(realTimeQuoteVo);
            realTimeQuoteVo = new RealTimeQuoteVo();
            realTimeQuoteVo.setFutureCode("CF601");
            realTimeQuoteVo.setLastPrice(new BigDecimal(13265));
            realTimeQuoteVo.setClearPrice(new BigDecimal(13125));
            realTimeQuoteVo.setHighLimit(new BigDecimal(12320));
            realTimeQuoteVo.setLowLimit(new BigDecimal(13900));
            realTimeQuoteVo.setUpdateTime(LocalDateTime.parse("09:00:02"));
            realTimeQuoteVoList.add(realTimeQuoteVo);
            realTimeQuoteVo = new RealTimeQuoteVo();
            realTimeQuoteVo.setFutureCode("CF603");
            realTimeQuoteVo.setLastPrice(new BigDecimal(13295));
            realTimeQuoteVo.setClearPrice(new BigDecimal(13165));
            realTimeQuoteVo.setHighLimit(new BigDecimal(12310));
            realTimeQuoteVo.setLowLimit(new BigDecimal(13890));
            realTimeQuoteVo.setUpdateTime(LocalDateTime.parse("09:00:02"));
            realTimeQuoteVoList.add(realTimeQuoteVo);
        }

        return realTimeQuoteVoList;
    }

    private List<RealTimeQuoteVo> parseRealTimeQuoteVoList(String json){

        List<RealTimeQuoteVo> realTimeQuoteVoList = new ArrayList<>();
        ResponseMessage reponseMessage = (ResponseMessage) JSON.parseObject(json.toString(), ResponseMessage.class);
        if (!reponseMessage.getBody().getRtnCode().equals("0000")) {
            throw new BusinessException(reponseMessage.getBody().getRtnCode(), reponseMessage.getBody().getRtnMessage());
        }

        Map<String, Object> dataset = reponseMessage.getBody().getDataSet();
        Object result = dataset.values().iterator().next();//realTimeQuoteVoList

            log.debug("数据期货行情数据: {}", result.toString());
            // 解析第一个JSON字符串
            // JSONObject jsonObject = JSON.parseObject(result.toString());
            //System.out.println("Message: " + jsonObject.getString("msg"));
            JSONArray dataArray = (JSONArray)result;//jsonObject.getJSONArray("realTimeQuoteVoList");
            for (Object obj : dataArray) {
                // 将JSON对象转换为目标类对象
                RealTimeQuoteVo quoteVo = JSON.toJavaObject((JSONObject) obj, RealTimeQuoteVo.class);
                realTimeQuoteVoList.add(quoteVo);
            }

        return realTimeQuoteVoList;
    }


}
