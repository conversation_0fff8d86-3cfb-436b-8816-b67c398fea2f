package com.basiscotton.deliverycore.cache.constant;


/**
 * Date: 2025/8/21 11:38
 * Description: 缓存配置项的枚举类
 *
 * <AUTHOR>
 */
public enum CacheEnum {
    //点价指令锁缓存
    PricingCommandCache("pricingCommandCache", CacheConstants.EXPIRES_NO,500,100000);


    private String name;
    private long expire;

    private int initialCapacity;

    private int maximumSize;

    CacheEnum(String name, long expire, int initialCapacity, int maximumSize){
        this.name = name;
        this.expire = expire;
        this.initialCapacity = initialCapacity;
        this.maximumSize = maximumSize;
    }

     String getName() {
        return name;
    }

     public long getExpires() {
        return expire;
    }

    public int getInitialCapacity() {
        return initialCapacity;
    }

     public int getMaximumSize() {
        return maximumSize;
    }
}
