package com.basiscotton.deliverycore.cache;

import com.basiscotton.deliverycore.cache.bo.PricingCommand;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.basiscotton.deliverycore.cache.constant.CacheEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: PricingCommandCacheBuilder.java
 * @Description: 点价指令及锁缓存
 * @date 2025/8/21 14:40
 */
@Configuration
@Slf4j
public class PricingCommandCacheBuilder {
    @Primary
    @Bean
    public Cache<String, PricingCommand> pricingCommandCache(){
        Cache<String, PricingCommand> cache = Caffeine.newBuilder()
                .initialCapacity(CacheEnum.PricingCommandCache.getInitialCapacity())
                .maximumSize(CacheEnum.PricingCommandCache.getMaximumSize())
                //不过期.expireAfterAccess(CacheEnum.FundsAccountLockCache.getExpires(), TimeUnit.SECONDS)
                .recordStats()
                .build();
        log.info("点价指令及锁缓存构建完成！");
        return cache;
    }
}
