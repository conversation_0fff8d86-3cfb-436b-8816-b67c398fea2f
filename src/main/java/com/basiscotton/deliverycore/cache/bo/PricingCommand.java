package com.basiscotton.deliverycore.cache.bo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: PricingCommandVo.java
 * @Description: 点价指令VO
 * @date 2025/8/21 14:43
 */
@Data
public class PricingCommand {

    /**指令ID*/
    private String commandId;

    /**交割标的ID*/
    private String targetId;

    /**批号*/
    private String batchNo;

    /**期货合约代码*/
    private String futureCode;

    /**点价价格*/
    private BigDecimal pricingPrice;

    /**指令状态*/
    private String commandStatus;

    /**指令创建时间*/
    private LocalDateTime commandCreateTime;

    /**指令生效时间*/
    private LocalDateTime commandEffectTime;

    /**点价有效期 */
    private LocalDateTime pricingValidEndTime;

    /**锁对象,公平锁(同时避免等待线程同时唤醒),禁止序列化;*/
    public ReentrantLock commandLock = new ReentrantLock(true);
}
