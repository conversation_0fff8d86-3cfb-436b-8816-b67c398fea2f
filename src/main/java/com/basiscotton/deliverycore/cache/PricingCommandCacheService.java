package com.basiscotton.deliverycore.cache;

import com.basiscotton.deliverycore.cache.bo.PricingCommand;
import com.github.benmanes.caffeine.cache.Cache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: PricingCommandCacheService.java
 * @Description: 点价指令缓存服务
 * @date 2025/8/21 15:53
 */
@Component
@Slf4j
public class PricingCommandCacheService {

    @Resource
    Cache<String, PricingCommand> pricingCommandCache;

    /**
     * 添加缓存
     */
    public void addCache(String key, PricingCommand pricingCommand) {
        pricingCommandCache.get(key, k -> pricingCommand);
    }
    /**
     * 获取缓存中的某个值
     */
    public PricingCommand getCache(String key) {
        return pricingCommandCache.getIfPresent(key);
    }
    /**
     * 获取缓存中的所有值
     */
    public List<PricingCommand> getAllCache() {
        List<PricingCommand> pricingCommandList = new ArrayList<PricingCommand>(pricingCommandCache.asMap().values());
        return pricingCommandList;
    }
    /**
     * 返回pricingCommandCache的引用
     */
    public Cache<String, PricingCommand> getPricingCommandCache() {
        return pricingCommandCache;
    }
    /**
     * 删除缓存
     */
    public void removeCache(String key) {
        pricingCommandCache.invalidate(key);
    }

    /**
     * 初始化缓存
     */
    public void initCache(ArrayList<PricingCommand> list) {
        list.stream().forEach(pricingCommand -> {
            try {
                pricingCommandCache.put(pricingCommand.getCommandId(), pricingCommand);
            } catch (Exception e) {
                log.error("初始化缓存失败。", e);
                throw new RuntimeException("初始化缓存失败。");
            }
        });

    }

}
