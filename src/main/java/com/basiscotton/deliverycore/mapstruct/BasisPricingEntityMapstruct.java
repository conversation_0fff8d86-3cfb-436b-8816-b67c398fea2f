package com.basiscotton.deliverycore.mapstruct;

import com.basiscotton.base.entity.BasisDeliveryTargetEntity;
import com.basiscotton.base.entity.BasisPricingCommandEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * 点价指令实体转换
 * <AUTHOR>
 *
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE , componentModel = "spring")
public interface BasisPricingEntityMapstruct {

	BasisPricingEntityMapstruct INSTANCE = Mappers.getMapper(BasisPricingEntityMapstruct.class);

	@Mappings({
			//id
			@Mapping(target = "id" , expression = "java(org.harry.dandelion.framework.id.core.service.BizIdGenerator.getInstance().generateBizId())"),
			//标的id
			@Mapping(source = "targetEntity.id",target = "targetId"),
			//交易号
			@Mapping(source = "targetEntity.transactionNo",target = "transactionNo"),
			//点价有效期
			@Mapping(source = "targetEntity.pricingValidEndTime",target = "pricingValidTime"),
			//批号
			@Mapping(source = "targetEntity.batchNo",target = "batchNo"),
			//仓单号
			@Mapping(source = "targetEntity.warehouseReceiptNo",target = "warehouseReceiptNo"),
			//期货合约
			@Mapping(source = "targetEntity.futureCode",target = "futureCode"),
			//点价方代码
			@Mapping(source = "targetEntity.buyerTraderCode",target = "pricingCommanderCode"),
			//点价方名称
			@Mapping(source = "targetEntity.buyerTraderName",target = "pricingCommanderName"),
			//接收方代码
			@Mapping(source = "targetEntity.sellerTraderCode",target = "pricingTakerCode"),
			//接收方名称
			@Mapping(source = "targetEntity.sellerTraderName",target = "pricingTakerName"),
	})
	public BasisPricingCommandEntity toBasisPricingEntity(BasisDeliveryTargetEntity targetEntity);



}
