package com.basiscotton.deliverycore.reentrantlocks;

import com.basiscotton.deliverycore.cache.bo.PricingCommand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import com.github.benmanes.caffeine.cache.Cache;
import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: PricingCommandLockService.java
 * @Description: 点价指令锁服务
 * @date 2025/8/21 15:53
 */
@Component
@Slf4j
public class PricingCommandLockService {

    @Resource
    Cache<String, PricingCommand> pricingCommandCache;

    /**
     *  对单一点价指令加锁，线程安全
     *  加锁实现,后面要紧跟try块，及临界区代码
     */
    public void lockPricingCommand(String commandId) {
        PricingCommand pricingCommand = pricingCommandCache.getIfPresent(commandId);
        if(pricingCommand!=null){
           pricingCommand.commandLock.lock();
        }else {
            log.error("加锁失败，点价指令不存在，指令ID：{}", pricingCommand.getCommandId());
            throw new RuntimeException("点价指令不存在。");
        }
    }

    /**
     *  对单一点价指令解锁，线程安全
     */
    public void unlockPricingCommand(String commandId) {
        PricingCommand pricingCommand = pricingCommandCache.getIfPresent(commandId);
        if(pricingCommand!=null){
            // 解锁实现,要求在业务代码的finally块中调用。
            // 注意：解锁操作必须执行，否则线程会一直持有锁
            // 加锁的代码和对应的解锁的代码必须定义在同一个类中
            // 防御性编码，防止lock前出现异常(缓存)，finally块中解锁失败；这样lock在try块前最后行或块中第一行都可以
            if (pricingCommand.commandLock.isHeldByCurrentThread()) {
                pricingCommand.commandLock.unlock();
            }
        }else {
            log.error("解锁失败，点价指令不存在，指令ID：{}", pricingCommand.getCommandId());
            throw new RuntimeException("点价指令不存在。");
        }
    }

    /**
     * 对单一点价指令限时加锁,基本用不到
     */
    public boolean lockPricingCommandWithTimeOut(String commandId, long timeout, TimeUnit timeUnit) {
        boolean lockAcquired = false;
        PricingCommand pricingCommand = pricingCommandCache.getIfPresent(commandId);
        try {
            lockAcquired = pricingCommand.commandLock.tryLock(timeout, timeUnit);
        }catch (InterruptedException e) {
            log.error("点价指令不存在加锁失败：{}", pricingCommand);
            throw new RuntimeException("点价指令不存在加锁失败。");
        }
        return lockAcquired;
    }
}
