package com.basiscotton.deliverycore.business.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.BasisDeliveryTargetEntity;
import com.basiscotton.base.entity.BasisPricingCommandEntity;
import com.basiscotton.base.mappers.BasisDeliveryTargetMapper;
import com.basiscotton.base.mappers.BasisPricingCommandMapper;
import com.basiscotton.common.marketSeting.BasisMarketSettingService;
import com.basiscotton.common.tradetime.service.BasisTradeTimeWindowService;
import com.basiscotton.deliverycore.business.PricingCommandService;
import com.basiscotton.deliverycore.cache.PricingCommandCacheService;
import com.basiscotton.deliverycore.mapstruct.BasisPricingEntityMapstruct;
import com.basiscotton.deliverycore.reentrantlocks.PricingCommandLockService;
import com.basiscotton.deliverycore.cache.bo.PricingCommand;
import com.basiscotton.futurequotes.Queues.PricingFutureQuoteQueue;
import com.basiscotton.futurequotes.vo.RealTimeQuoteVo;
import com.basiscotton.locks.redislocks.BusinessLockService;
import com.github.benmanes.caffeine.cache.Cache;
import com.sinosoft.dandelion.system.client.params.DataItem;
import lombok.extern.slf4j.Slf4j;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Title: PricingCommandService.java
 * @Description: 点价指令服务-提供业务使用
 * <AUTHOR>
 * @date 2025/8/20
 * @version V1.0
 */
@Service
@Slf4j
public class BuyerPricingCommandService implements PricingCommandService {

    @Resource
    private BasisTradeTimeWindowService basisTradeTimeWindowService;
    @Resource
    private PricingFutureQuoteQueue pricingFutureQuoteQueue;
    @Resource
    private PricingCommandCacheService pricingCommandCacheService;
    @Resource
    private PricingCommandLockService pricingCommandLockService;
    @Resource
    private BusinessLockService businessLockUtilService;
    @Resource
    private BasisPricingCommandMapper pricingCommandMapper;
    @Resource
    private BasisDeliveryTargetMapper deliveryTargetMapper;
    @Resource
    private Cache<String, RealTimeQuoteVo> futureQuotesCache;





	/**--------------------------------点价指令操作服务--------------------------------**/
	/*
		创建点价指令
		1. 交易时间验证：是否属于交易时间范围(获取公共设置)+限定点价有限期当日的15:00前
		2. 商品状态验证：商品状态是否符合条件(商品点价状态)
		3. 点价指令验证：同一个人同一批次的预生效、已生效点价指令只能存在一条
		4. 点价指令锁定，业务处理
			1)、执行2、3验证
			2)、第一次点价指令则仓单状态变更
			3)、业务数据处理
			4)、放入预点价指令
	*/
	@Override
	public void createPricingCommand(String targetId, BigDecimal pricingPrice) {
        log.debug("创建点价指令，标的ID:{},点价价格:{}", targetId, pricingPrice);
        //查询标的是否存在点价指令
        BasisDeliveryTargetEntity target = deliveryTargetMapper.selectById(targetId);
        //验证工作日、交易时间范围
        String verifyResult = basisTradeTimeWindowService.verifyTradeTimeWindow();
        if(verifyResult != null && !BasisCottonConstants.TRADE_NORMAL.equals(verifyResult)) {
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE,"不属于交易时间，无法点价。");
        }
        if (BasisCottonConstants.pricing_status_1.equals(target.getPricingStatus().getCode())) {//未点价
            try{
                //加锁
                List<String> businessIdList = new ArrayList<String>();
                businessIdList.add(targetId);
                businessLockUtilService.addLock(businessIdList);
                BasisDeliveryTargetEntity newTarget = deliveryTargetMapper.selectById(targetId);
                //再次判断状态，标的是否点价
                if (BasisCottonConstants.pricing_status_1.equals(newTarget.getPricingStatus().getCode()) ) {
                    long AUTO_EFFECT_TIME = BasisMarketSettingService.settingEntity.getAutoPricingTime() * 60 * 1000;//自动确认时间
                    //构造点价指令数据
                    BasisPricingCommandEntity pricingEntity = BasisPricingEntityMapstruct.INSTANCE.toBasisPricingEntity(newTarget);
                    pricingEntity.setPricingPrice(pricingPrice);
                    pricingEntity.setPricingType(new DataItem(BasisCottonConstants.pricing_type_1, ""));
                    pricingEntity.setCommandStatus(new DataItem(BasisCottonConstants.command_status_1, ""));
                    pricingEntity.setCommandCreateTime(LocalDateTime.now());
                    pricingEntity.setCommandEffectTime(LocalDateTime.now().plus(AUTO_EFFECT_TIME, ChronoUnit.MINUTES));
                    pricingCommandMapper.insert(pricingEntity);
                    //修改交割标的点价状态
                    LambdaUpdateWrapper<BasisDeliveryTargetEntity> targetUpdateWrapper = new LambdaUpdateWrapper<BasisDeliveryTargetEntity>();
                    targetUpdateWrapper.eq(BasisDeliveryTargetEntity::getId, targetId);
                    targetUpdateWrapper.set(BasisDeliveryTargetEntity::getPricingStatus, BasisCottonConstants.pricing_status_2);
                    deliveryTargetMapper.update(null, targetUpdateWrapper);
                    //添加缓存
                    PricingCommand pricingCommand = new PricingCommand();
                    pricingCommand.setCommandId(pricingEntity.getId().toString());
                    pricingCommand.setTargetId(pricingEntity.getTargetId().toString());
                    pricingCommand.setBatchNo(pricingEntity.getBatchNo());
                    pricingCommand.setFutureCode(pricingEntity.getFutureCode());
                    pricingCommand.setPricingPrice(pricingEntity.getPricingPrice());
                    pricingCommand.setCommandStatus(pricingEntity.getCommandStatus().getCode());
                    pricingCommand.setCommandCreateTime(pricingEntity.getCreateTime());
                    pricingCommand.setCommandEffectTime(pricingEntity.getCommandEffectTime());
                    pricingCommandCacheService.addCache(pricingCommand.getCommandId(), pricingCommand);

                    log.debug("完成创建点价指令");
                }else{
                    throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE,"商品已点价，请勿重复点价。");
                }

            } catch (Exception e) {
                log.error("点价指令加锁失败。", e);
                throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE,"点价指令加锁失败");
            }finally {
                //解锁一定要在此执行
                businessLockUtilService.unWaitLock(targetId);
            }
        }else{
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE,"商品已点价，请勿重复点价。");
        }
	}


	/*
		更新点价指令
		1. 交易时间验证：是否属于交易时间范围(获取公共设置) +限定点价有限期当日的15:00前
		2. 商品状态验证：商品状态是否符合条件(点价指令状态)
		3. 点价指令锁定，业务处理
			1)、执行2验证
			2)、业务数据处理(点价指令表)
			3)、更新点价指令
	*/
	@Override
	public void updatePricingCommand(String commandId, BigDecimal pricingPrice) {
        log.debug("修改点价指令，点价指令ID:{},点价价格:{}", commandId, pricingPrice);
        PricingCommand pricingCommand = pricingCommandCacheService.getCache(commandId);
        if(pricingCommand == null){
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE,"批次未点价，无法更新点价");
        }
        //验证工作日、交易时间范围
        String verifyResult = basisTradeTimeWindowService.verifyTradeTimeWindow();
        if(verifyResult != null && !BasisCottonConstants.TRADE_NORMAL.equals(verifyResult)) {
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE,"不属于交易时间，无法点价。");
        }
        if (pricingCommand.getCommandStatus().equals(BasisCottonConstants.command_status_1) || pricingCommand.getCommandStatus().equals(BasisCottonConstants.command_status_2)) {//预生效或者已生效
            try{
                //加锁
                pricingCommandLockService.lockPricingCommand(commandId);
                PricingCommand newPricingCommand = pricingCommandCacheService.getCache(commandId);
                //再次判断状态
                if (newPricingCommand.getCommandStatus().equals(BasisCottonConstants.command_status_1) || newPricingCommand.getCommandStatus().equals(BasisCottonConstants.command_status_2)) {//预生效或者已生效
                    //修改数据库状态
                    LambdaUpdateWrapper<BasisPricingCommandEntity> updateWrapper = new LambdaUpdateWrapper<BasisPricingCommandEntity>();
                    updateWrapper.eq(BasisPricingCommandEntity::getId, newPricingCommand.getCommandId());
                    updateWrapper.set(BasisPricingCommandEntity::getPricingPrice, pricingPrice);
                    pricingCommandMapper.update(null, updateWrapper);
                    //修改缓存价格
                    newPricingCommand.setPricingPrice(pricingPrice);

                    log.debug("完成修改点价指令");
                }else{
                    throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE,"批次点价击穿或者卖方已确认，无法更新点价");
                }

            } catch (Exception e) {
                log.error("点价指令加锁失败。", e);
                throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE,"点价指令加锁失败");
            }finally {
                //解锁一定要在此执行
                pricingCommandLockService.unlockPricingCommand(commandId);
            }
        }else{
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE,"批次点价击穿或者卖方已确认，无法更新点价");
        }
	}

	/*
		取消点价指令
		1. 交易时间验证：是否属于交易时间范围(获取公共设置)
		2. 商品状态验证：商品状态是否符合条件(点价指令状态)
		3. 点价指令锁定，业务处理
			1)、执行2验证
			2)、业务数据处理(点价指令表、标的表)
			3)、移除点价指令
	*/
	@Override
	public void cancelPricingCommand(String commandId) {
        log.debug("手动取消点价指令，点价指令ID:{}", commandId);
        PricingCommand pricingCommand = pricingCommandCacheService.getCache(commandId);
        if(pricingCommand == null){
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE,"批次未点价，无法取消点价");
        }
        //验证工作日、交易时间范围
        String verifyResult = basisTradeTimeWindowService.verifyTradeTimeWindow();
        if(verifyResult != null && !BasisCottonConstants.TRADE_NORMAL.equals(verifyResult)) {
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE,"不属于交易时间，无法点价。");
        }
        if (pricingCommand.getCommandStatus().equals(BasisCottonConstants.command_status_1) || pricingCommand.getCommandStatus().equals(BasisCottonConstants.command_status_2)) {//预生效或者已生效
            try{
                //加锁
                pricingCommandLockService.lockPricingCommand(commandId);
                PricingCommand newPricingCommand = pricingCommandCacheService.getCache(commandId);
                //再次判断状态，标的是否点价
                if (newPricingCommand.getCommandStatus().equals(BasisCottonConstants.command_status_1) || newPricingCommand.getCommandStatus().equals(BasisCottonConstants.command_status_2)) {//预生效或者已生效
                    //修改数据库状态
                    LambdaUpdateWrapper<BasisPricingCommandEntity> updateWrapper = new LambdaUpdateWrapper<BasisPricingCommandEntity>();
                    updateWrapper.eq(BasisPricingCommandEntity::getId, commandId);
                    updateWrapper.set(BasisPricingCommandEntity::getCommandStatus, BasisCottonConstants.command_status_3);
                    pricingCommandMapper.update(null, updateWrapper);
                    //移除缓存价格
                    pricingCommandCacheService.removeCache(commandId);

                    log.debug("完成手动取消点价指令");
                }else{
                    throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE,"批次点价击穿或者卖方已确认，无法撤销点价");
                }

            } catch (Exception e) {
                log.error("点价指令加锁失败。", e);
                throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE,"点价指令加锁失败");
            }finally {
                //解锁一定要在此执行
                pricingCommandLockService.unlockPricingCommand(commandId);
            }
        }else{
            throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE,"批次点价击穿或者卖方已确认，无法撤销点价");
        }
	}

	/*
		手动确认点价指令处理
		1. 交易时间验证：是否属于交易时间范围(获取公共设置)
		2. 商品状态验证：商品状态是否符合条件(商品状态)
		3. 点价指令锁定，业务处理
			1)、执行2验证
			2)、业务数据处理(点价指令表)
			3)、移除点价指令
			4)、生成结算协议
	*/
	@Override
	public void ConfirmPricingCommand(String commandId, BigDecimal pricingPrice) {
        log.debug("手动确认点价指令，点价指令ID:{}", commandId);
        PricingCommand pricingCommand = pricingCommandCacheService.getCache(commandId);
        if(pricingCommand == null)
        if (pricingCommand.getCommandStatus().equals(BasisCottonConstants.command_status_1) ) {//预生效
            //手动确认点价指令
            try{
                //加锁
                pricingCommandLockService.lockPricingCommand(pricingCommand.getCommandId());
                //再次判断状态，避免卖方已确认
                if (pricingCommand.getCommandStatus().equals(BasisCottonConstants.command_status_1)) {
                    //修改缓存状态
                    pricingCommand.setCommandStatus(BasisCottonConstants.command_status_2);
                    //修改数据库状态
                    LambdaUpdateWrapper<BasisPricingCommandEntity> updateWrapper = new LambdaUpdateWrapper<BasisPricingCommandEntity>();
                    updateWrapper.eq(BasisPricingCommandEntity::getId, pricingCommand.getCommandId());
                    updateWrapper.set(BasisPricingCommandEntity::getCommandStatus, BasisCottonConstants.command_status_2);
                    updateWrapper.set(BasisPricingCommandEntity::getCommandEffectType,BasisCottonConstants.command_effect_type_1);
                    updateWrapper.set(BasisPricingCommandEntity::getCommandEffectTime, LocalDateTime.now());
                    pricingCommandMapper.update(null, updateWrapper);
                    //修改交割标的点价状态
                    LambdaUpdateWrapper<BasisDeliveryTargetEntity> targetUpdateWrapper = new LambdaUpdateWrapper<BasisDeliveryTargetEntity>();
                    targetUpdateWrapper.eq(BasisDeliveryTargetEntity::getId, pricingCommand.getTargetId());
                    targetUpdateWrapper.set(BasisDeliveryTargetEntity::getPricingStatus, BasisCottonConstants.pricing_status_3);
                    targetUpdateWrapper.set(BasisDeliveryTargetEntity::getPricingTime, LocalDateTime.now());
                    targetUpdateWrapper.set(BasisDeliveryTargetEntity::getPricingPrice, pricingPrice);
                    deliveryTargetMapper.update(null, targetUpdateWrapper);
                    pricingCommandCacheService.removeCache(commandId);
                    log.debug("手动确认点价指令，点价指令ID:{}", commandId);
                }
            } catch (Exception e) {
                log.error("点价指令加锁失败。", e);
                //回滚点价指令缓存状态,移除缓存必然未执行，所以这里不用回填缓存
                pricingCommand.setCommandStatus(BasisCottonConstants.command_status_1);
                throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE,"点价指令加锁失败");
            }finally {
                //解锁一定要在此执行
                pricingCommandLockService.unlockPricingCommand(pricingCommand.getCommandId());
            }
        }
	}

	/**--------------------------------点价指令自动服务--------------------------------**/
	/*
		初始化点价指令数据
	*/
	@Override
	public void initPricingCommandData() {
        log.debug("初始化点价指令数据");
		List<String> statusList = Arrays.asList(BasisCottonConstants.command_status_1,BasisCottonConstants.command_status_2);
		ArrayList<PricingCommand> pricingList = pricingCommandMapper.getAllPricingCommandList(statusList);
		pricingCommandCacheService.initCache(pricingList);
        log.debug("完成初始化点价指令数据");
	}

	/*
       更新点价指令数据
   */
	@Override
	public void updatePricingCommandData() {

	}

    /*
    生效点价指令
    1. 自动确认时间验证：是否达到自定确认时间(获取公共设置)
    2. 点价指令锁定，业务处理
        1)、业务数据处理(点价指令表)
        2)、放入点价指令
*/
    @Override
    public void effectPricingCommand() {
        log.debug("生效点价指令");
        long AUTO_EFFECT_TIME = BasisMarketSettingService.settingEntity.getAutoPricingTime() * 60 * 1000;
        //获取pricingCommandCache，循环处理
        pricingCommandCacheService.getAllCache().forEach(pricingCommand -> {
            if (pricingCommand.getCommandStatus().equals(BasisCottonConstants.command_status_1) ) {
                //判断是否达到自动确认时间,注意时间的先后顺序，否则会得出负值
                if (ChronoUnit.MILLIS.between( pricingCommand.getCommandCreateTime(),LocalDateTime.now()) > AUTO_EFFECT_TIME) {
                    try{
                        //加锁
                        pricingCommandLockService.lockPricingCommand(pricingCommand.getCommandId());
                        //再次判断状态，避免卖方已确认
                        if (pricingCommand.getCommandStatus().equals(BasisCottonConstants.command_status_1)) {
                            //修改缓存状态
                            pricingCommand.setCommandStatus(BasisCottonConstants.command_status_2);
                            //修改数据库状态
                            LambdaUpdateWrapper<BasisPricingCommandEntity> updateWrapper = new LambdaUpdateWrapper<BasisPricingCommandEntity>();
                            updateWrapper.eq(BasisPricingCommandEntity::getId, pricingCommand.getCommandId());
                            updateWrapper.set(BasisPricingCommandEntity::getCommandStatus, BasisCottonConstants.command_status_2);
                            updateWrapper.set(BasisPricingCommandEntity::getCommandEffectType,BasisCottonConstants.command_effect_type_2);
                            updateWrapper.set(BasisPricingCommandEntity::getCommandEffectTime, LocalDateTime.now());
                            pricingCommandMapper.update(null, updateWrapper);
                            //修改交割标的点价状态
                            LambdaUpdateWrapper<BasisDeliveryTargetEntity> targetUpdateWrapper = new LambdaUpdateWrapper<BasisDeliveryTargetEntity>();
                            targetUpdateWrapper.eq(BasisDeliveryTargetEntity::getId, pricingCommand.getTargetId());
                            targetUpdateWrapper.set(BasisDeliveryTargetEntity::getPricingStatus, BasisCottonConstants.pricing_status_3);
                            deliveryTargetMapper.update(null, targetUpdateWrapper);
                        }

                    } catch (Exception e) {
                        log.error("点价指令加锁失败。", e);
                        //回滚点价指令缓存状态
                        pricingCommand.setCommandStatus(BasisCottonConstants.command_status_1);
                        throw new RuntimeException("点价指令加锁失败。");
                    }finally {
                        //解锁一定要在此执行
                        pricingCommandLockService.unlockPricingCommand(pricingCommand.getCommandId());
                    }
                }
            }
        });
        log.debug("完成生效点价指令");
    }



	/*
		系统自动撤销点价指令

		1. 点价指令锁定，业务处理
			1)、业务数据处理(点价指令表、商品表)
			2)、移除点价指令
	*/
	@Override
	public void systemCancelPricingCommand(String commandId,  @Pattern(regexp = "^[45]$")String commandStatus) {
        log.debug("自动取消点价指令，点价指令ID:{}，自动撤销类型:{}", commandId, commandStatus);
        PricingCommand pricingCommand = pricingCommandCacheService.getCache(commandId);
        if(pricingCommand != null) {
            try {
                //加锁
                pricingCommandLockService.lockPricingCommand(commandId);
                //修改数据库状态
                LambdaUpdateWrapper<BasisPricingCommandEntity> updateWrapper = new LambdaUpdateWrapper<BasisPricingCommandEntity>();
                updateWrapper.eq(BasisPricingCommandEntity::getId, commandId);
                updateWrapper.set(BasisPricingCommandEntity::getCommandStatus, commandStatus);
                pricingCommandMapper.update(null, updateWrapper);
                //移除缓存
                pricingCommandCacheService.removeCache(commandId);

                log.debug("完成自动取消点价指令");
            } catch (Exception e) {
                log.error("点价指令加锁失败。", e);
                throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "点价指令加锁失败");
            } finally {
                //解锁一定要在此执行
                pricingCommandLockService.unlockPricingCommand(commandId);
            }
        }
	}

	/*
		系统自动击穿点价指令处理
		1. 商品状态验证：商品状态是否符合条件(商品状态)
		2. 点价指令锁定，业务处理
			1)、执行1验证
			2)、行情价格-点价价格击穿验证
			3)、点价有效期验证
			4)、业务数据处理(点价指令表、标的表)
			5)、移除点价指令
			6)、生成结算协议

		补充：行情产生时间>点价指令生效时间：主要考虑行情积压时（理论上概率极小），用几分钟前的行情成交了新的点价指令，而卖方在期货上并没有击穿成交的问题。
             如果行情遗漏了，后续又没有机会了，则而更依赖于卖方来确认点价指令。
	*/
	@Override
	public void SystemBreakPricingCommandHandle() {
        log.debug("点价指令是否击穿");
        // 循环取尽所有在队列中的行情，依次再循环点价指令队列，来比对处理
        while (true) {
            // 从尾部取数据，如果队列为空立即返回null
            RealTimeQuoteVo realTimeQuote = pricingFutureQuoteQueue.pollLast();
            if (realTimeQuote != null) {
                // 有数据，则获取pricingCommandCache，循环处理
                pricingCommandCacheService.getAllCache().forEach(pricingCommand -> {
                    if (pricingCommand.getCommandStatus().equals(BasisCottonConstants.command_status_2)
                            && pricingCommand.getFutureCode().equals(realTimeQuote.getFutureCode())
                            && pricingCommand.getCommandEffectTime().isAfter(realTimeQuote.getUpdateTime())
                            && pricingCommand.getCommandEffectTime().toLocalDate().isBefore(LocalDate.now())) {//防御性判断，定时任务如果未在15:05清除已到期指令，则会被排除
                        // 判断价格是否击穿
                        if (realTimeQuote.getLastPrice().compareTo(pricingCommand.getPricingPrice()) > 0) {
                            log.debug("点价指令被击穿，点价指令ID:{}，期货代码:{}，点价价格:{}，行情价格:{}", pricingCommand.getCommandId(), pricingCommand.getFutureCode(), pricingCommand.getPricingPrice(), realTimeQuote.getLastPrice());
                            try{
                                //加锁
                                pricingCommandLockService.lockPricingCommand(pricingCommand.getCommandId());
                                //再次判断状态
                                if(pricingCommand.getCommandStatus().equals(BasisCottonConstants.command_status_2)) {
                                    //修改缓存状态
                                    pricingCommand.setCommandStatus(BasisCottonConstants.command_status_6);
                                    //修改数据库状态
                                    LambdaUpdateWrapper<BasisPricingCommandEntity> updateWrapper = new LambdaUpdateWrapper<BasisPricingCommandEntity>();
                                    updateWrapper.eq(BasisPricingCommandEntity::getId, pricingCommand.getCommandId());
                                    updateWrapper.set(BasisPricingCommandEntity::getCommandStatus, BasisCottonConstants.command_status_6);
                                    updateWrapper.set(BasisPricingCommandEntity::getPricingCompleteTime, LocalDateTime.now());
                                    pricingCommandMapper.update(null, updateWrapper);
                                    //修改交割标的点价状态
                                    LambdaUpdateWrapper<BasisDeliveryTargetEntity> targetUpdateWrapper = new LambdaUpdateWrapper<BasisDeliveryTargetEntity>();
                                    targetUpdateWrapper.eq(BasisDeliveryTargetEntity::getId, pricingCommand.getTargetId());
                                    targetUpdateWrapper.set(BasisDeliveryTargetEntity::getPricingStatus, BasisCottonConstants.pricing_status_3);
                                    deliveryTargetMapper.update(null, targetUpdateWrapper);
                                    //移除缓存
                                    pricingCommandCacheService.removeCache(pricingCommand.getCommandId());
                                }
                            } catch (Exception e) {
                                log.error("点价指令加锁失败。", e);
                                //回滚点价指令缓存状态,移除缓存必然未执行，所以这里不用回填缓存
                                pricingCommand.setCommandStatus(BasisCottonConstants.command_status_2);
                                throw new RuntimeException("点价指令加锁失败。");
                            }finally {
                                //解锁一定要在此执行
                                pricingCommandLockService.unlockPricingCommand(pricingCommand.getCommandId());
                            }
                        }

                    }
                });

            }else {
                // 没有数据，则退回，等待下一轮处理
                break;
            }
        }
	}

    /**
     * 循环点价指令作当日到期处理--使用定时任务触发，要求当日已收盘15:00以后
     * 如果错过了当日，则技术处置
     */
    @Override
    public void pricingCommandExpire() {
        log.debug("循环点价指令作有效期处理");
        pricingCommandCacheService.getAllCache().forEach(pricingCommand -> {
          //要求当日到期，可能是生效也可能是预生效-刚发出的指令
            if (pricingCommand.getPricingValidEndTime().toLocalDate().equals(LocalDate.now())) {
                log.debug("点价指令已到期，点价指令ID:{}，期货代码:{}，点价价格:{}", pricingCommand.getCommandId(), pricingCommand.getFutureCode(), pricingCommand.getPricingPrice());
                systemCancelPricingCommand(pricingCommand.getCommandId(), BasisCottonConstants.command_status_4);
            }
        });
    }

    /**
     * 循环未点价（点价中的应该已于15点恢复为未点价了）并当日到期的标的做开盘价处理--使用定时任务触发21:05
     */
    @Override
    public void pricingCommandOpeningPrice() {
        log.debug("循环未点价并当日到期的标的做开盘价处理");
        LocalDate today = LocalDate.now();
        LocalDateTime startOfDay = today.atStartOfDay(); // 2023-10-05 00:00:00
        LocalDateTime startOfNextDay = today.plusDays(1).atStartOfDay(); // 2023-10-06 00:00:00
        //从数据库取数 条件为未点价、有效期为当日;只有如果数据库中为 00:00:00才能与localDate.now()相同，否则要处理
        LambdaQueryWrapper<BasisDeliveryTargetEntity> queryWrapper = new LambdaQueryWrapper<BasisDeliveryTargetEntity>();
        queryWrapper.eq(BasisDeliveryTargetEntity::getPricingStatus, BasisCottonConstants.pricing_status_1);
        queryWrapper.ge(BasisDeliveryTargetEntity::getPricingValidEndTime, startOfDay)
                .lt(BasisDeliveryTargetEntity::getPricingValidEndTime, startOfNextDay);
        List<BasisDeliveryTargetEntity> deliveryTargetList = deliveryTargetMapper.selectList(queryWrapper);
        for (BasisDeliveryTargetEntity deliveryTarget : deliveryTargetList) {
            LambdaUpdateWrapper<BasisDeliveryTargetEntity> targetUpdateWrapper = new LambdaUpdateWrapper<BasisDeliveryTargetEntity>();
            targetUpdateWrapper.eq(BasisDeliveryTargetEntity::getId, deliveryTarget.getId());
            targetUpdateWrapper.set(BasisDeliveryTargetEntity::getPricingStatus, BasisCottonConstants.pricing_status_3);
            //查找对应期货，计算最终结算价格
               // 获取缓存中的值，如果不存在则返回null
            RealTimeQuoteVo quote = futureQuotesCache.asMap().get(deliveryTarget.getFutureCode());
            if(quote!=null) {//理论上不应该空
                targetUpdateWrapper.set(BasisDeliveryTargetEntity::getFinalSettlementPrice, deliveryTarget.getTradeBasisPrice().add(quote.getOpenPrice()));
                targetUpdateWrapper.set(BasisDeliveryTargetEntity::getPricingFrom, BasisCottonConstants.pricing_From_1);
                targetUpdateWrapper.set(BasisDeliveryTargetEntity::getPricingTime, LocalDateTime.now());
            }
            deliveryTargetMapper.update(null, targetUpdateWrapper);
            log.debug("未点价并当日到期的标的做开盘价处理，标的ID:{}，批号:{}", deliveryTarget.getId(), deliveryTarget.getBatchNo());

        }
    }
}
