package com.basiscotton.deliverycore.business;

import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * @Title: PricingCommandService.java
 * @Description: 点价指令服务-提供业务使用
 * <AUTHOR>
 * @date 2025/8/20
 * @version V1.0
 */
@Service
public interface PricingCommandService {

	/**--------------------------------点价指令操作服务--------------------------------**/
	/*
	 * @Description: 创建点价指令
	 * @param:
	 * @param: null
	 * @return:
	 **/
	public void createPricingCommand(String targetId, BigDecimal pricingPrice);



	/*
	 * @Description: 修改点价指令
	 * @param:
	 * @param: null
	 * @return:
	 **/
	public void updatePricingCommand(String commandId, BigDecimal pricingPrice);

	/*
	 * @Description: 撤销点价指令
	 * @param:
	 * @param: null
	 * @return:
	 **/
	public void cancelPricingCommand(String commandId);

	/*
	 * @Description: 手动确认点价指令
	 * @param:
	 * @param: null
	 * @return:
	 **/
	public void ConfirmPricingCommand(String commandId, BigDecimal pricingPrice);


	/**--------------------------------点价指令自动服务--------------------------------**/
	/*
	 * @Description: 初始化点价指令数据
	 * @param:
	 * @param: null
	 * @return:
	 **/
	public void initPricingCommandData();

	/*
	 * @Description: 更新点价指令数据
	 * @param:
	 * @param: null
	 * @return:
	 **/
	public void updatePricingCommandData();

    /*
     * @Description: 点价自动生效指令
     * @param:
     * @param: null
     * @return:
     **/
    public void effectPricingCommand();

	/*
	 * @Description: 系统内部撤销点价指令
	 * @param:
	 * @param: null
	 * @return:
	 **/
	public void systemCancelPricingCommand(String commandId, String commandStatus);

	/*
	 * @Description: 自动点价击穿处理
	 * @param:
	 * @param: null
	 * @return:
	 **/
	public void SystemBreakPricingCommandHandle();

    /*
     * @Description: 点价指令当日到期处理
     * @param:
     * @param: null
     * @return:
     **/
    public void pricingCommandExpire();

    /*
     * @Description: 当日到期点价指令-灌入开盘价
     * @param:
     * @param: null
     * @return:
     **/
    public void pricingCommandOpeningPrice();
}
