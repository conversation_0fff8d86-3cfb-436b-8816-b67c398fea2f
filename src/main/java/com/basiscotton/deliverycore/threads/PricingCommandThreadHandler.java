package com.basiscotton.deliverycore.threads;

import com.basiscotton.deliverycore.business.impl.BuyerPricingCommandService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: PricingCommandThreadHandler.java
 * @Description: 点价指令线程处理器
 * @date 2025/8/21 14:31
 */
@Component
@Slf4j
public class PricingCommandThreadHandler implements CommandLineRunner, DisposableBean {

    @Resource
    private BuyerPricingCommandService pricingCommandService;


    private volatile boolean running = true;
    private Thread handlerThread;

    @Override
    public void run(String... args) throws Exception {
        //初始化点价指令缓存
        pricingCommandService.initPricingCommandData();
        //启动点价指令处理线程
        handlerThread = new Thread(() -> {
            while (running) {
            try {
                //循环一遍，自动确认5分钟内的点价指令
                pricingCommandService.effectPricingCommand();
                //行情与点价指令，判断是否击穿
                pricingCommandService.SystemBreakPricingCommandHandle();
                Thread.sleep(5000);
                } catch (Exception e) {
                    log.error("点价指令处理线程异常", e);
                }
            }
            log.debug("点价指令处理线程已退出");
        }, "pricing-command-handler-thread");

        handlerThread.start();
    }

    @Override
    public void destroy() throws Exception {
        log.info("正在关闭点价指令处理线程...");
        running = false;           // 通知线程退出
        if (handlerThread != null && handlerThread.isAlive()) {// 中断 sleep 状态
            handlerThread.interrupt();
        }
    }
}
