package com.basiscotton.deliverycore.threads;

import com.basiscotton.deliverycore.business.PricingCommandService;
import lombok.extern.slf4j.Slf4j;
import org.harry.dandelion.framework.scheduler.quartz.job.DandelionJob;
import org.quartz.JobExecutionContext;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: PricingCommandExpireTask.java
 * @Description: 点价指令到期处理定时任务2 于21:05执行;夜盘开始后使用开盘价，作用于先交割后点价批次。
 * @date 2025/8/22 23:11
 */
@Slf4j
public class PricingCommandOpeningPriceTask extends DandelionJob {
    @Resource
    private PricingCommandService pricingCommandService;
    @Override
    public void process(JobExecutionContext context) throws Exception {
        //获取开盘价,包含未点价而到期的交割标的
        pricingCommandService.pricingCommandOpeningPrice();
    }
}
