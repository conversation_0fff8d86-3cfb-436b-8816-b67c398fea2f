package com.applet.trade.preSaleResources.preSale.service;


import com.applet.trade.hall.vo.StockTelVo;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

/**
 * @Title: GetStockTelInfo.java
 * @Description: 获取商品联系方式
 * <AUTHOR>
 * @date 2025/6/9
 * @version V1.0
 */
@Service("preSale.getStockTelInfo.1")
@ApiRequestObject(value = "交易大厅-预售电询", name = "getStockTelInfo", groups = {"小程序端-基差交易-预售模块"}, params = {
        @ApiParamMeta(key = "stockId", desc = "商品ID", type = String.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "stockTelVo", desc = "联系信息", type = StockTelVo.class),
})
public class GetStockTelInfo implements IBusinessService {

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        String stockId = context.getValueObject(String.class, "stockId");
        StockTelVo stockTelVo = new StockTelVo();
        stockTelVo.setBusinessName("北京全国棉花交易市场集团有限公司");
        stockTelVo.setBusinessTel("***********");

        //创建响应实体
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("stockTelVo", stockTelVo);
    }
}
