package com.applet.trade.preSaleResources.preSale.service;

import com.applet.trade.base.mappers.SmallProPreSaleMappers;
import com.applet.trade.preSaleResources.preSale.vo.SmallProPreSaleReqVo;
import com.applet.trade.preSaleResources.preSale.vo.SmallProPreSaleResVo;
import com.basiscotton.base.BasisCottonConstants;
import org.harry.dandelion.framework.core.params.PageParameter;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.harry.dandelion.framework.rep.support.page.PipPagination;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Title: GetPreSaleStockByPage.java
 * @Description: 预售商品列表
 * <AUTHOR>
 * @date 2025/6/9
 * @version V1.0
 */

@Service("preSale.getSmallProPreSaleStockByPage.1")
@ApiRequestObject(value = "预售商品列表", name = "getSmallProPreSaleStockByPage", groups = {"小程序端-基差交易-交易大厅"}, params = {
        @ApiParamMeta(key = "pageParameter", desc = "分页参数", type = PageParameter.class),
        @ApiParamMeta(key = "reqVo", desc = "查询vo", type = SmallProPreSaleReqVo.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "pagination", desc = "查询分页列表", type = SmallProPreSaleResVo.class,pagination = true),
})
public class GetSmallProPreSaleStockByPage implements IBusinessService {


    @Resource
    private SmallProPreSaleMappers smallProPreSaleMappers;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        PageParameter pageParameter = context.getValueObject(PageParameter.class, "pageParameter");
        SmallProPreSaleReqVo reqVo = context.getValueObject(SmallProPreSaleReqVo.class, "reqVo");
        if(StringUtil.isNotEmpty(reqVo.getIntactPlace())){
            reqVo.setIntactPlaceList(Arrays.asList(reqVo.getIntactPlace().split(",")));
        }
        String currCustomCode = context.getCurrentUserCustomCode();
        reqVo.setResourceAuditStatus(BasisCottonConstants.resource_audit_status_3);
        List<String> tradeStatusList = new ArrayList<String>();
        tradeStatusList.add(BasisCottonConstants.trade_status_1);
        tradeStatusList.add(BasisCottonConstants.trade_status_2);
        reqVo.setTradeStatus(tradeStatusList);
        reqVo.setPreStockSource(BasisCottonConstants.pre_stock_source_1);
        reqVo.setTraderCode(currCustomCode);
        PipPagination<SmallProPreSaleResVo> pipPagination = new PipPagination<SmallProPreSaleResVo>(pageParameter);
        pipPagination = smallProPreSaleMappers.getSmallProPreSaleStockByPage(pipPagination, reqVo);
        List<SmallProPreSaleResVo> dataList = pipPagination.getResult();
        if(dataList != null && dataList.size() > 0 ){
            pipPagination.setResult(this.buildData(currCustomCode,dataList));
        }
        //创建响应实体
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("pagination", pipPagination);
    }

    private List<SmallProPreSaleResVo> buildData(String currCustomCode, List<SmallProPreSaleResVo> dataList){
        for (SmallProPreSaleResVo resVo : dataList) {
            //基差
            if (StringUtil.isNotEmpty(resVo.getQuoteTraderCode())) {
                if(currCustomCode.equals(resVo.getQuoteTraderCode())){
                    resVo.setTraderQuoteStatus(BasisCottonConstants.COMMON_YES);
                }
            }
            //基差
            if(StringUtil.isNotEmpty(resVo.getMinBasisPrice()) && StringUtil.isNotEmpty(resVo.getMaxBasisPrice())){
                resVo.setBasisPriceStr(resVo.getMinBasisPrice() + "~" + resVo.getMaxBasisPrice());
            }else if(StringUtil.isNotEmpty(resVo.getMinBasisPrice())){
                resVo.setBasisPriceStr("≥" + resVo.getMinBasisPrice());
            }else if(StringUtil.isNotEmpty(resVo.getMaxBasisPrice())){
                resVo.setBasisPriceStr("≤" + resVo.getMaxBasisPrice());
            }
            //长度
            if(StringUtil.isNotEmpty(resVo.getMinLength()) && StringUtil.isNotEmpty(resVo.getMaxLength())){
                resVo.setLengthStr(resVo.getMinLength().setScale(0, RoundingMode.DOWN) + "~" + resVo.getMaxLength().setScale(0,RoundingMode.DOWN));
            }else if(StringUtil.isNotEmpty(resVo.getMinLength())){
                resVo.setLengthStr("≥" + resVo.getMinLength().setScale(0,RoundingMode.DOWN));
            }else if(StringUtil.isNotEmpty(resVo.getMaxLength())){
                resVo.setLengthStr("≤" + resVo.getMaxLength().setScale(0,RoundingMode.DOWN));
            }
            //强力
            if(StringUtil.isNotEmpty(resVo.getMinBreak()) && StringUtil.isNotEmpty(resVo.getMaxBreak())){
                resVo.setBreakStr(resVo.getMinBreak().setScale(0,RoundingMode.DOWN) + "~" + resVo.getMaxBreak().setScale(0,RoundingMode.DOWN));
            }else if(StringUtil.isNotEmpty(resVo.getMinBreak())){
                resVo.setBreakStr("≥" + resVo.getMinBreak().setScale(0,RoundingMode.DOWN));
            }else if(StringUtil.isNotEmpty(resVo.getMaxBreak())){
                resVo.setBreakStr("≤" + resVo.getMaxBreak().setScale(0,RoundingMode.DOWN));
            }
            //长整
            if(StringUtil.isNotEmpty(resVo.getMinUniformityAverage()) && StringUtil.isNotEmpty(resVo.getMaxUniformityAverage())){
                resVo.setUniformityAverageStr(resVo.getMinUniformityAverage().setScale(0,RoundingMode.DOWN) + "~" + resVo.getMaxUniformityAverage().setScale(0,RoundingMode.DOWN));
            }else if(StringUtil.isNotEmpty(resVo.getMinUniformityAverage())){
                resVo.setUniformityAverageStr("≥" + resVo.getMinUniformityAverage());
            }else if(StringUtil.isNotEmpty(resVo.getMaxUniformityAverage().setScale(0,RoundingMode.DOWN))){
                resVo.setUniformityAverageStr("≤" + resVo.getMaxUniformityAverage().setScale(0,RoundingMode.DOWN));
            }
            //含杂
            if(StringUtil.isNotEmpty(resVo.getMinImpurity()) && StringUtil.isNotEmpty(resVo.getMaxImpurity())){
                resVo.setImpurityStr(resVo.getMinImpurity().setScale(1,RoundingMode.DOWN) + "~" + resVo.getMaxImpurity().setScale(1,RoundingMode.DOWN));
            }else if(StringUtil.isNotEmpty(resVo.getMinImpurity())){
                resVo.setImpurityStr("≥" + resVo.getMinImpurity().setScale(1,RoundingMode.DOWN));
            }else if(StringUtil.isNotEmpty(resVo.getMaxImpurity())){
                resVo.setImpurityStr("≤" + resVo.getMaxImpurity().setScale(1,RoundingMode.DOWN));
            }
            //回潮
            if(StringUtil.isNotEmpty(resVo.getMinMoisture()) && StringUtil.isNotEmpty(resVo.getMaxMoisture())){
                resVo.setMoistureStr(resVo.getMinMoisture().setScale(1,RoundingMode.DOWN) + "~" + resVo.getMaxMoisture().setScale(1,RoundingMode.DOWN));
            }else if(StringUtil.isNotEmpty(resVo.getMinMoisture())){
                resVo.setMoistureStr("≥" + resVo.getMinMoisture().setScale(1,RoundingMode.DOWN));
            }else if(StringUtil.isNotEmpty(resVo.getMaxMoisture())){
                resVo.setMoistureStr("≤" + resVo.getMaxMoisture().setScale(1,RoundingMode.DOWN));
            }
            //马值
            if(StringUtil.isNotEmpty(resVo.getMinMkl()) && StringUtil.isNotEmpty(resVo.getMaxMkl())){
                resVo.setMklStr(resVo.getMinMkl() + "~" + resVo.getMaxMkl());
            }else if(StringUtil.isNotEmpty(resVo.getMinMkl())){
                resVo.setMklStr("≥" + resVo.getMinMkl());
            }else if(StringUtil.isNotEmpty(resVo.getMaxMkl())){
                resVo.setMklStr("≤" + resVo.getMaxMkl());
            }
            //颜色级
            if(StringUtil.isNotEmpty(resVo.getMinColorGrade()) && StringUtil.isNotEmpty(resVo.getMaxColorGrade())){
                resVo.setColorGradeStr(resVo.getMinColorGrade() + "~" + resVo.getMaxColorGrade());
            }else if(StringUtil.isNotEmpty(resVo.getMinColorGrade())){
                resVo.setColorGradeStr("≥" + resVo.getMinColorGrade());
            }else if(StringUtil.isNotEmpty(resVo.getMaxColorGrade())){
                resVo.setColorGradeStr("≤" + resVo.getMaxColorGrade());
            }
        }
        return dataList;
    }
}
