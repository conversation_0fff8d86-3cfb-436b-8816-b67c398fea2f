package com.applet.trade.preSaleResources.resourceSubmission.service;

import com.applet.trade.base.mappers.SmallProPreStockMappers;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.BasisPreStockEntity;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * @Title: AddPreStock.java
 * @Description: 添加预售预购商品-交易商
 * <AUTHOR>
 * @date 2025/6/9
 * @version V1.0
 */
@Service("preSale.addSmallProPreStock.1")
@ApiRequestObject(value = "添加预售商品-资源提交(上架资源)", name = "addSmallProPreStock", groups = {"小程序端-基差交易-预售模块"}, params = {
        @ApiParamMeta(key = "idStr", desc = "id", type = String.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "success", desc = "返回结果", type = boolean.class),
        @ApiParamMeta(key = "message", desc = "返回消息", type = String.class),
})
public class AddSmallProPreStock implements IBusinessService {

    @Resource
    private SmallProPreStockMappers smallProPreStockMappers;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        String idStr = context.getValueObject(String.class, "idStr");

        LambdaQueryWrapper<BasisPreStockEntity> queryWrapper = Wrappers.lambdaQuery(BasisPreStockEntity.class);
        queryWrapper.in(BasisPreStockEntity::getId,Arrays.asList(idStr.split(",")));
        List<BasisPreStockEntity> dataList = smallProPreStockMappers.selectList(queryWrapper);

        if(dataList != null && dataList.size() > 0){
            for(BasisPreStockEntity entity : dataList){
                LambdaUpdateWrapper<BasisPreStockEntity> updateWrapper = new LambdaUpdateWrapper<BasisPreStockEntity>();
                updateWrapper.eq(BasisPreStockEntity::getId, entity.getId());
                if(StringUtil.isNotEmpty(entity.getRemark())){
                    updateWrapper.set(BasisPreStockEntity::getResourceAuditStatus, BasisCottonConstants.resource_audit_status_2);
                }else{
                    updateWrapper.set(BasisPreStockEntity::getResourceAuditStatus, BasisCottonConstants.resource_audit_status_3);
                }
                smallProPreStockMappers.update(null, updateWrapper);
            }
        }
        //构造返回数据
        this.createSuccessResponse(context);
        context.getResponseBody().setData("success", true);
        context.getResponseBody().setData("message", "添加成功");
    }
}
