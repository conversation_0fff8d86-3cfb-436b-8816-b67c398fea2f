package com.applet.trade.preSaleResources.resourceSubmission.service;

import com.applet.trade.base.mappers.SmallPreStockBaseInfoMappers;
import com.applet.trade.base.mappers.SmallProPreStockMappers;
import com.applet.trade.preSaleResources.resourceSubmission.vo.SmallProPreResourceVo;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.BasisSequenceContants;
import com.basiscotton.base.entity.BasisPreStockBaseInfoEntity;
import com.basiscotton.base.entity.BasisPreStockEntity;
import com.sinosoft.dandelion.system.client.params.DataItem;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.id.core.service.BizIdGenerator;
import org.harry.dandelion.framework.sequence.ISequenceFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * @Title: AddPreResource.java
 * @Description: 添加预售预购资源
 * <AUTHOR>
 * @date 2025/6/9
 * @version V1.0
 */
@Service("preSale.addSmallProPreResource.1")
@ApiRequestObject(value = "添加预售资源-资源提交(模块录入)", name = "addSmallProPreResource", groups = {"小程序端-基差交易-预售模块"}, params = {
        @ApiParamMeta(key = "resourceVo", desc = "查询vo", type = SmallProPreResourceVo.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "success", desc = "返回结果", type = boolean.class),
        @ApiParamMeta(key = "message", desc = "返回消息", type = String.class),
})
public class AddSmallProPreResource implements IBusinessService {

    @Resource
    private SmallProPreStockMappers smallProPreStockMappers;

    @Resource
    private SmallPreStockBaseInfoMappers smallPreStockBaseInfoMappers;

    @Resource
    private ISequenceFactory sequenceFactory;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        SmallProPreResourceVo resourceVo = context.getValueObject(SmallProPreResourceVo.class, "resourceVo");
        Long stockBaseId = BizIdGenerator.getInstance().generateBizId();
        String stockCode = sequenceFactory.create(BasisSequenceContants.SEQ_GPSPM).next();
        this.addStock(resourceVo,stockCode,stockBaseId,context);
        this.addStockBase(resourceVo,stockCode,stockBaseId,context);
        //构造返回数据
        this.createSuccessResponse(context);
        context.getResponseBody().setData("success", true);
        context.getResponseBody().setData("message", "添加成功");
    }

    private void addStock(SmallProPreResourceVo resourceVo, String stockCode, Long stockBaseId, ServiceHandlerContext context){
        BasisPreStockEntity stockEntity = new BasisPreStockEntity();
        stockEntity.setId(BizIdGenerator.getInstance().generateBizId());
        stockEntity.setStockBaseInfoId(stockBaseId);
        stockEntity.setStockCode(stockCode);
        stockEntity.setResourceDisplayType(new DataItem(BasisCottonConstants.resource_display_type_1,""));
        stockEntity.setResourceAuditStatus(new DataItem(BasisCottonConstants.resource_audit_status_1,""));
        stockEntity.setQuoteType(new DataItem(BasisCottonConstants.quote_type_2,""));
        stockEntity.setPricingParty(new DataItem(BasisCottonConstants.pricing_party_1,""));
        stockEntity.setPreStockWeight(resourceVo.getPreStockWeight());
        stockEntity.setPreStockSource(new DataItem(resourceVo.getPreStockSource(),""));
        stockEntity.setTradeStatus(new DataItem(BasisCottonConstants.trade_status_1,""));
        stockEntity.setTraderCode(context.getCurrentUserCustomCode());
        stockEntity.setTraderName(context.getCurrentUserCustomName());
        stockEntity.setBasisSellerMarginType(new DataItem(BasisCottonConstants.seller_basis_margin_type_1,""));
        stockEntity.setSellerMarginStandard(new BigDecimal("0"));
        stockEntity.setBasisSellerTradeFeeType(new DataItem(BasisCottonConstants.basis_seller_free_type_1,""));
        stockEntity.setBasisSellerDeliveryFeeType(new DataItem(BasisCottonConstants.basis_seller_free_type_1,""));
        stockEntity.setSellerTradeFeeStandard(new BigDecimal("0"));
        stockEntity.setSellerDeliveryFeeStandard(new BigDecimal("0"));
        stockEntity.setSupplementContent(resourceVo.getSupplementContent());

        stockEntity.setFutureCode(resourceVo.getFutureCode());
        stockEntity.setMinBasisPrice(resourceVo.getMinBasisPrice());
        stockEntity.setMaxBasisPrice(resourceVo.getMaxBasisPrice());
        stockEntity.setPricingValidTime(resourceVo.getPricingValidTime());
        stockEntity.setDeliveryTime(resourceVo.getDeliveryTime());
        stockEntity.setRemark(resourceVo.getRemark());
        smallProPreStockMappers.insert(stockEntity);
    }

    private void addStockBase(SmallProPreResourceVo resourceVo, String stockCode, Long stockBaseId, ServiceHandlerContext context){
        BasisPreStockBaseInfoEntity stockBaseEntity = new BasisPreStockBaseInfoEntity();
        stockBaseEntity.setId(stockBaseId);
        stockBaseEntity.setStockCode(stockCode);
        BigDecimal weight = resourceVo.getPreStockWeight();
        BigDecimal quantity = weight.multiply(BasisCottonConstants.batch_weught).setScale(0, RoundingMode.UP);
        stockBaseEntity.setQuantity(quantity.intValue());
        stockBaseEntity.setTraderCode(context.getCurrentUserCustomCode());
        stockBaseEntity.setTraderName(context.getCurrentUserCustomName());
        stockBaseEntity.setWhsPickMode(new DataItem(resourceVo.getWhsPickMode(),""));
        stockBaseEntity.setMinColorGrade(resourceVo.getMinColorGrade());
        stockBaseEntity.setMinMkl(resourceVo.getMinMkl());
        stockBaseEntity.setMaxMkl(resourceVo.getMaxMkl());
        stockBaseEntity.setMinLength(resourceVo.getMinLength());
        stockBaseEntity.setMaxLength(resourceVo.getMaxLength());
        stockBaseEntity.setMinBreak(resourceVo.getMinBreak());
        stockBaseEntity.setMaxBreak(resourceVo.getMaxBreak());
        stockBaseEntity.setMinUniformityAverage(resourceVo.getMinUniformityAverage());
        stockBaseEntity.setMinImpurity(resourceVo.getMinImpurity());
        stockBaseEntity.setMinMoisture(resourceVo.getMinMoisture());
        stockBaseEntity.setMaxMoisture(resourceVo.getMaxMoisture());
        stockBaseEntity.setPlaceType(resourceVo.getPlaceType());
        stockBaseEntity.setIntactPlace(resourceVo.getIntactPlace());
        smallPreStockBaseInfoMappers.insert(stockBaseEntity);
    }
}
