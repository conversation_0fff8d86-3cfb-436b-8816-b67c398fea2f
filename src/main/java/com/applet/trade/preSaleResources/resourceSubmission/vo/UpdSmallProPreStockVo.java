package com.applet.trade.preSaleResources.resourceSubmission.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Title: UpdBasisPreStockVo.java
 * @Description: 资源维护vo
 * <AUTHOR>
 * @date 2025/6/3
 * @version V1.0
 */
@Data
public class UpdSmallProPreStockVo implements Serializable {

    @ApiModelProperty(value="商品id")
    private String idStr;

    @ApiModelProperty(value = "商品来源: 1-预售 2-预购")
    private String preStockSource;

    @ApiModelProperty(value = "预售预购重量")
    private BigDecimal preStockWeight;

    @ApiModelProperty(value="产地编码")
    private String placeType;

    @ApiModelProperty(value="完整地址")
    private String intactPlace;

    @ApiModelProperty(value="采摘方式：1、手摘棉 2机采棉")
    private String whsPickMode;

    @ApiModelProperty(value="最小长度")
    private BigDecimal minLength;

    @ApiModelProperty(value="最大长度")
    private BigDecimal maxLength;

    @ApiModelProperty(value="最小强力")
    private BigDecimal minBreak;

    @ApiModelProperty(value="最大强力")
    private BigDecimal maxBreak;

    @ApiModelProperty(value="最小含杂")
    private BigDecimal minImpurity;

    @ApiModelProperty(value="最小马值")
    private BigDecimal minMkl;

    @ApiModelProperty(value="最大马值")
    private BigDecimal maxMkl;

    @ApiModelProperty(value="最小长整")
    private BigDecimal minUniformityAverage;

    @ApiModelProperty(value="最小回潮")
    private BigDecimal minMoisture;

    @ApiModelProperty(value="最大回潮")
    private BigDecimal maxMoisture;

    @ApiModelProperty(value="最小颜色级")
    private BigDecimal minColorGrade;

    @ApiModelProperty(value="补充说明")
    private String supplementContent;

    @ApiModelProperty(value="期货合约")
    private String futureCode;

    @ApiModelProperty(value = "基差价格")
    private BigDecimal minBasisPrice;

    @ApiModelProperty(value = "基差价格")
    private BigDecimal maxBasisPrice;

    @ApiModelProperty(value = "点价有效期")
    private Date pricingValidTime;

    @ApiModelProperty(value = "预计交货时间")
    private Date deliveryTime;

    @ApiModelProperty(value = "备注")
    private String remark;
}
