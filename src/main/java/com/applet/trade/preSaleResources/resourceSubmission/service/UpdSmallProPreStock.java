package com.applet.trade.preSaleResources.resourceSubmission.service;

import com.applet.trade.base.mappers.SmallPreStockBaseInfoMappers;
import com.applet.trade.base.mappers.SmallProPreStockMappers;
import com.applet.trade.preSaleResources.resourceSubmission.vo.UpdSmallProPreStockVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.BasisPreStockBaseInfoEntity;
import com.basiscotton.base.entity.BasisPreStockEntity;
import com.sinosoft.dandelion.system.client.params.DataItem;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;

/**
 * @Title: UpdBasisPreStock.java
 * @Description: 修改预售预购信息
 * <AUTHOR>
 * @date 2025/6/9
 * @version V1.0
 */
@Service("preSale.updSmallProPreStock.1")
@ApiRequestObject(value = "修改预售信息-资源管理(维护资源)", name = "updSmallProPreStock", groups = {"小程序端-基差交易-预售模块"}, params = {
        @ApiParamMeta(key = "stockInfo", desc = "商品信息", type = UpdSmallProPreStockVo.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "success", desc = "返回结果", type = boolean.class),
        @ApiParamMeta(key = "message", desc = "返回消息", type = String.class),
})
public class UpdSmallProPreStock implements IBusinessService {

    @Resource
    private SmallProPreStockMappers smallProPreStockMappers;

    @Resource
    private SmallPreStockBaseInfoMappers smallPreStockBaseInfoMappers;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        UpdSmallProPreStockVo stockInfo = context.getValueObject(UpdSmallProPreStockVo.class, "stockInfo");

        LambdaQueryWrapper<BasisPreStockEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(BasisPreStockEntity::getId, Arrays.asList(stockInfo.getIdStr().split(",")));
        List<BasisPreStockEntity> preStockList = smallProPreStockMappers.selectList(queryWrapper);

        if(preStockList != null && preStockList.size() > 0){
            for(BasisPreStockEntity stock : preStockList){
                DataItem tradeStatus = stock.getTradeStatus();
                if(tradeStatus != null){
                    if(!BasisCottonConstants.trade_status_1.equals(tradeStatus.getCode()) && !BasisCottonConstants.trade_status_3.equals(tradeStatus.getCode())){
                        throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, stock.getStockCode()+"商品存在报价信息，无法资源维护");
                    }
                }
                this.updStock(stockInfo,stock.getId());
                this.updStockBase(stockInfo,stock.getStockBaseInfoId());
            }
        }
        //构造返回数据
        this.createSuccessResponse(context);
        context.getResponseBody().setData("success", true);
        context.getResponseBody().setData("message", "修改成功");
    }

    private void updStock(UpdSmallProPreStockVo stockInfo,Long stockId){
        BasisPreStockEntity stockEntity = new BasisPreStockEntity();
        stockEntity.setId(stockId);
        stockEntity.setPreStockSource(new DataItem(stockInfo.getPreStockSource(),""));
        stockEntity.setPreStockWeight(stockInfo.getPreStockWeight());
        stockEntity.setSupplementContent(stockInfo.getSupplementContent());
        stockEntity.setFutureCode(stockInfo.getFutureCode());
        stockEntity.setMinBasisPrice(stockInfo.getMinBasisPrice());
        stockEntity.setMaxBasisPrice(stockInfo.getMaxBasisPrice());
        stockEntity.setPricingValidTime(stockInfo.getPricingValidTime());
        stockEntity.setDeliveryTime(stockInfo.getDeliveryTime());
        stockEntity.setRemark(stockInfo.getRemark());
        smallProPreStockMappers.updateById(stockEntity);
    }

    private void updStockBase(UpdSmallProPreStockVo stockInfo, Long stockBaseId){
        BasisPreStockBaseInfoEntity stockBaseEntity = new BasisPreStockBaseInfoEntity();
        stockBaseEntity.setId(stockBaseId);
        BigDecimal weight = stockInfo.getPreStockWeight();
        BigDecimal quantity = weight.multiply(BasisCottonConstants.batch_weught).setScale(0, RoundingMode.UP);
        stockBaseEntity.setQuantity(quantity.intValue());
        stockBaseEntity.setIntactPlace(stockInfo.getIntactPlace());
        stockBaseEntity.setWhsPickMode(new DataItem(stockInfo.getWhsPickMode(),""));
        stockBaseEntity.setMinColorGrade(stockInfo.getMinColorGrade());
        stockBaseEntity.setMinMkl(stockInfo.getMinMkl());
        stockBaseEntity.setMaxMkl(stockInfo.getMaxMkl());
        stockBaseEntity.setMinLength(stockInfo.getMinLength());
        stockBaseEntity.setMaxLength(stockInfo.getMaxLength());
        stockBaseEntity.setMinBreak(stockInfo.getMinBreak());
        stockBaseEntity.setMaxBreak(stockInfo.getMaxBreak());
        stockBaseEntity.setMinUniformityAverage(stockInfo.getMinUniformityAverage());
        stockBaseEntity.setMinImpurity(stockInfo.getMinImpurity());
        stockBaseEntity.setMinMoisture(stockInfo.getMinMoisture());
        stockBaseEntity.setMaxMoisture(stockInfo.getMaxMoisture());
        smallPreStockBaseInfoMappers.updateById(stockBaseEntity);
    }
}
