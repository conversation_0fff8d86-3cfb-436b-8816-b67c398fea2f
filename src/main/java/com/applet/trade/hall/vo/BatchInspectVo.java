package com.applet.trade.hall.vo;


import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class BatchInspectVo implements Serializable {

	/**
	 */
	@TableId("ID")
	@ApiModelProperty(value = "TODO <！请添加注释>")
	@JSONField(serializeUsing = ToStringSerializer.class)
	private Long id;
	
	/**
	 *加工批号
	 */
	@TableField("BATCH_CODE")
	@ApiModelProperty(value = "加工批号")
	private String batchCode;
	
	/**
	 *轧工质量P1比率
	 */
	@TableField("GINNING_QUALITY_RATE_P1")
	@ApiModelProperty(value = "轧工质量P1比率")
	private BigDecimal ginningQualityRateP1;
	
	/**
	 *轧工质量P2比率
	 */
	@TableField("GINNING_QUALITY_RATE_P2")
	@ApiModelProperty(value = "轧工质量P2比率")
	private BigDecimal ginningQualityRateP2;
	
	/**
	 *轧工质量P3比率
	 */
	@TableField("GINNING_QUALITY_RATE_P3")
	@ApiModelProperty(value = "轧工质量P3比率")
	private BigDecimal ginningQualityRateP3;
	
	/**
	 *主体颜色级
	 */
	@TableField("COLOR_GRADE")
	@ApiModelProperty(value = "主体颜色级")
	private String colorGrade;
	
	/**
	 *颜色级比率：白棉1级
	 */
	@TableField("WHITE_COTTON_L1")
	@ApiModelProperty(value = "颜色级比率：白棉1级")
	private BigDecimal whiteCottonL1;
	
	/**
	 *颜色级比率：白棉2级
	 */
	@TableField("WHITE_COTTON_L2")
	@ApiModelProperty(value = "颜色级比率：白棉2级")
	private BigDecimal whiteCottonL2;
	
	/**
	 *颜色级比率：白棉3级
	 */
	@TableField("WHITE_COTTON_L3")
	@ApiModelProperty(value = "颜色级比率：白棉3级")
	private BigDecimal whiteCottonL3;
	
	/**
	 *颜色级比率：白棉4级
	 */
	@TableField("WHITE_COTTON_L4")
	@ApiModelProperty(value = "颜色级比率：白棉4级")
	private BigDecimal whiteCottonL4;
	
	/**
	 *颜色级比率：白棉5级
	 */
	@TableField("WHITE_COTTON_L5")
	@ApiModelProperty(value = "颜色级比率：白棉5级")
	private BigDecimal whiteCottonL5;
	
	/**
	 *颜色级比率：白棉6级
	 */
	@TableField("WHITE_COTTON_L6")
	@ApiModelProperty(value = "颜色级比率：白棉6级")
	private BigDecimal whiteCottonL6;
	
	/**
	 *颜色级比率：淡点污棉1级
	 */
	@TableField("SPOT_COTTON_L1")
	@ApiModelProperty(value = "颜色级比率：淡点污棉1级")
	private BigDecimal spotCottonL1;
	
	/**
	 *颜色级比率：淡点污棉2级
	 */
	@TableField("SPOT_COTTON_L2")
	@ApiModelProperty(value = "颜色级比率：淡点污棉2级")
	private BigDecimal spotCottonL2;
	
	/**
	 *颜色级比率：淡点污棉3级
	 */
	@TableField("SPOT_COTTON_L3")
	@ApiModelProperty(value = "颜色级比率：淡点污棉3级")
	private BigDecimal spotCottonL3;
	
	/**
	 *颜色级比率：淡点污棉4级
	 */
	@TableField("SPOT_COTTON_L4")
	@ApiModelProperty(value = "颜色级比率：淡点污棉4级")
	private BigDecimal spotCottonL4;
	
	/**
	 *颜色级比率：淡黄染棉1级
	 */
	@TableField("YELLOW_ISH_COTTON_L1")
	@ApiModelProperty(value = "颜色级比率：淡黄染棉1级")
	private BigDecimal yellowIshCottonL1;
	
	/**
	 *颜色级比率：淡黄染棉2级
	 */
	@TableField("YELLOW_ISH_COTTON_L2")
	@ApiModelProperty(value = "颜色级比率：淡黄染棉2级")
	private BigDecimal yellowIshCottonL2;
	
	/**
	 *颜色级比率：淡黄染棉3级
	 */
	@TableField("YELLOW_ISH_COTTON_L3")
	@ApiModelProperty(value = "颜色级比率：淡黄染棉3级")
	private BigDecimal yellowIshCottonL3;
	
	/**
	 *颜色级比率：黄染棉1级
	 */
	@TableField("YELLOW_COTTON_L1")
	@ApiModelProperty(value = "颜色级比率：黄染棉1级")
	private BigDecimal yellowCottonL1;
	
	/**
	 *颜色级比率：黄染棉2级
	 */
	@TableField("YELLOW_COTTON_L2")
	@ApiModelProperty(value = "颜色级比率：黄染棉2级")
	private BigDecimal yellowCottonL2;
	
	/**
	 *长度级平均值
	 */
	@TableField("AVG_LENGTH")
	@ApiModelProperty(value = "长度级平均值")
	private BigDecimal avgLength;
	
	/**
	 *长度级最大值
	 */
	@TableField("MAX_LENGTH")
	@ApiModelProperty(value = "长度级最大值")
	private BigDecimal maxLength;
	
	/**
	 *长度级最小值
	 */
	@TableField("MIN_LENGTH")
	@ApiModelProperty(value = "长度级最小值")
	private BigDecimal minLength;
	
	/**
	 *长度级比率：25(33)毫米
	 */
	@TableField("LENGTH_RATE_25")
	@ApiModelProperty(value = "长度级比率：25(33)毫米")
	private BigDecimal lengthRate25;
	
	/**
	 *长度级比率：26(34)毫米
	 */
	@TableField("LENGTH_RATE_26")
	@ApiModelProperty(value = "长度级比率：26(34)毫米")
	private BigDecimal lengthRate26;
	
	/**
	 *长度级比率：27(35)毫米
	 */
	@TableField("LENGTH_RATE_27")
	@ApiModelProperty(value = "长度级比率：27(35)毫米")
	private BigDecimal lengthRate27;
	
	/**
	 *长度级比率：28(36)毫米
	 */
	@TableField("LENGTH_RATE_28")
	@ApiModelProperty(value = "长度级比率：28(36)毫米")
	private BigDecimal lengthRate28;
	
	/**
	 *长度级比率：29(37)毫米
	 */
	@TableField("LENGTH_RATE_29")
	@ApiModelProperty(value = "长度级比率：29(37)毫米")
	private BigDecimal lengthRate29;
	
	/**
	 *长度级比率：30(38)毫米
	 */
	@TableField("LENGTH_RATE_30")
	@ApiModelProperty(value = "长度级比率：30(38)毫米")
	private BigDecimal lengthRate30;
	
	/**
	 *长度级比率：31(39)毫米
	 */
	@TableField("LENGTH_RATE_31")
	@ApiModelProperty(value = "长度级比率：31(39)毫米")
	private BigDecimal lengthRate31;
	
	/**
	 *长度级比率：32毫米
	 */
	@TableField("LENGTH_RATE_32")
	@ApiModelProperty(value = "长度级比率：32毫米")
	private BigDecimal lengthRate32;
	
	/**
	 *主体马克隆值级
	 */
	@TableField("MAIN_MKL")
	@ApiModelProperty(value = "主体马克隆值级")
	private String mainMkl;
	
	/**
	 *马克隆值级平均值
	 */
	@TableField("AVG_MKL")
	@ApiModelProperty(value = "马克隆值级平均值")
	private BigDecimal avgMkl;
	
	/**
	 *马克隆值级最大值
	 */
	@TableField("MAX_MKL")
	@ApiModelProperty(value = "马克隆值级最大值")
	private BigDecimal maxMkl;
	
	/**
	 *马克隆值级最小值
	 */
	@TableField("MIN_MKL")
	@ApiModelProperty(value = "马克隆值级最小值")
	private BigDecimal minMkl;
	
	/**
	 *马克隆各档比率：A档
	 */
	@TableField("MKL_A")
	@ApiModelProperty(value = "马克隆各档比率：A档")
	private BigDecimal mklA;
	
	/**
	 *马克隆各档比率：B1档
	 */
	@TableField("MKL_B1")
	@ApiModelProperty(value = "马克隆各档比率：B1档")
	private BigDecimal mklB1;
	
	/**
	 *马克隆各档比率：B2档
	 */
	@TableField("MKL_B2")
	@ApiModelProperty(value = "马克隆各档比率：B2档")
	private BigDecimal mklB2;
	
	/**
	 *马克隆各档比率：C1档
	 */
	@TableField("MKL_C1")
	@ApiModelProperty(value = "马克隆各档比率：C1档")
	private BigDecimal mklC1;
	
	/**
	 *马克隆各档比率：C2档
	 */
	@TableField("MKL_C2")
	@ApiModelProperty(value = "马克隆各档比率：C2档")
	private BigDecimal mklC2;
	
	/**
	 *马克隆各档平均值：A档
	 */
	@TableField("MKL_AVG_A")
	@ApiModelProperty(value = "马克隆各档平均值：A档")
	private BigDecimal mklAvgA;
	
	/**
	 *马克隆各档平均值：B1档
	 */
	@TableField("MKL_AVG_B1")
	@ApiModelProperty(value = "马克隆各档平均值：B1档")
	private BigDecimal mklAvgB1;
	
	/**
	 *马克隆各档平均值：B2档
	 */
	@TableField("MKL_AVG_B2")
	@ApiModelProperty(value = "马克隆各档平均值：B2档")
	private BigDecimal mklAvgB2;
	
	/**
	 *马克隆各档平均值：C1档
	 */
	@TableField("MKL_AVG_C1")
	@ApiModelProperty(value = "马克隆各档平均值：C1档")
	private BigDecimal mklAvgC1;
	
	/**
	 *马克隆各档平均值：C2档
	 */
	@TableField("MKL_AVG_C2")
	@ApiModelProperty(value = "马克隆各档平均值：C2档")
	private BigDecimal mklAvgC2;
	
	/**
	 *长度整齐度平均值
	 */
	@TableField("AVG_LEN_UNIFORMITY")
	@ApiModelProperty(value = "长度整齐度平均值")
	private BigDecimal avgLenUniformity;
	
	/**
	 *长度整齐度最大值
	 */
	@TableField("MAX_LEN_UNIFORMITY")
	@ApiModelProperty(value = "长度整齐度最大值")
	private BigDecimal maxLenUniformity;
	
	/**
	 *长度整齐度最小值
	 */
	@TableField("MIN_LEN_UNIFORMITY")
	@ApiModelProperty(value = "长度整齐度最小值")
	private BigDecimal minLenUniformity;
	
	/**
	 *长度整齐度各档比率：U5
	 */
	@TableField("LEN_UNIFORMITY_RATE_U5")
	@ApiModelProperty(value = "长度整齐度各档比率：U5")
	private BigDecimal lenUniformityRateU5;
	
	/**
	 *长度整齐度各档比率：U4
	 */
	@TableField("LEN_UNIFORMITY_RATE_U4")
	@ApiModelProperty(value = "长度整齐度各档比率：U4")
	private BigDecimal lenUniformityRateU4;
	
	/**
	 *长度整齐度各档比率：U3
	 */
	@TableField("LEN_UNIFORMITY_RATE_U3")
	@ApiModelProperty(value = "长度整齐度各档比率：U3")
	private BigDecimal lenUniformityRateU3;
	
	/**
	 *长度整齐度各档比率：U2
	 */
	@TableField("LEN_UNIFORMITY_RATE_U2")
	@ApiModelProperty(value = "长度整齐度各档比率：U2")
	private BigDecimal lenUniformityRateU2;
	
	/**
	 *长度整齐度各档比率：U1
	 */
	@TableField("LEN_UNIFORMITY_RATE_U1")
	@ApiModelProperty(value = "长度整齐度各档比率：U1")
	private BigDecimal lenUniformityRateU1;
	
	/**
	 *长度整齐度各档平均值：U5
	 */
	@TableField("LEN_UNIFORMITY_AVG_U5")
	@ApiModelProperty(value = "长度整齐度各档平均值：U5")
	private BigDecimal lenUniformityAvgU5;
	
	/**
	 *长度整齐度各档平均值：U4
	 */
	@TableField("LEN_UNIFORMITY_AVG_U4")
	@ApiModelProperty(value = "长度整齐度各档平均值：U4")
	private BigDecimal lenUniformityAvgU4;
	
	/**
	 *长度整齐度各档平均值：U3
	 */
	@TableField("LEN_UNIFORMITY_AVG_U3")
	@ApiModelProperty(value = "长度整齐度各档平均值：U3")
	private BigDecimal lenUniformityAvgU3;
	
	/**
	 *长度整齐度各档平均值：U2
	 */
	@TableField("LEN_UNIFORMITY_AVG_U2")
	@ApiModelProperty(value = "长度整齐度各档平均值：U2")
	private BigDecimal lenUniformityAvgU2;
	
	/**
	 *长度整齐度各档平均值：U1
	 */
	@TableField("LEN_UNIFORMITY_AVG_U1")
	@ApiModelProperty(value = "长度整齐度各档平均值：U1")
	private BigDecimal lenUniformityAvgU1;
	
	/**
	 *断裂比强度平均值
	 */
	@TableField("AVG_BREAK_RATE")
	@ApiModelProperty(value = "断裂比强度平均值")
	private BigDecimal avgBreakRate;
	
	/**
	 *断裂比强度最大值
	 */
	@TableField("MAX_BREAK_RATE")
	@ApiModelProperty(value = "断裂比强度最大值")
	private BigDecimal maxBreakRate;
	
	/**
	 *断裂比强度最小值
	 */
	@TableField("MIN_BREAK_RATE")
	@ApiModelProperty(value = "断裂比强度最小值")
	private BigDecimal minBreakRate;
	
	/**
	 *断裂比强度比率：S5
	 */
	@TableField("BREAK_RATE_S5")
	@ApiModelProperty(value = "断裂比强度比率：S5")
	private BigDecimal breakRateS5;
	
	/**
	 *断裂比强度比率：S4
	 */
	@TableField("BREAK_RATE_S4")
	@ApiModelProperty(value = "断裂比强度比率：S4")
	private BigDecimal breakRateS4;
	
	/**
	 *断裂比强度比率：S3
	 */
	@TableField("BREAK_RATE_S3")
	@ApiModelProperty(value = "断裂比强度比率：S3")
	private BigDecimal breakRateS3;
	
	/**
	 *断裂比强度比率：S2
	 */
	@TableField("BREAK_RATE_S2")
	@ApiModelProperty(value = "断裂比强度比率：S2")
	private BigDecimal breakRateS2;
	
	/**
	 *断裂比强度比率：S1
	 */
	@TableField("BREAK_RATE_S1")
	@ApiModelProperty(value = "断裂比强度比率：S1")
	private BigDecimal breakRateS1;
	
	/**
	 *断裂比强度平均值：S5
	 */
	@TableField("BREAK_RATE_AVG_S5")
	@ApiModelProperty(value = "断裂比强度平均值：S5")
	private BigDecimal breakRateAvgS5;
	
	/**
	 *断裂比强度平均值：S4
	 */
	@TableField("BREAK_RATE_AVG_S4")
	@ApiModelProperty(value = "断裂比强度平均值：S4")
	private BigDecimal breakRateAvgS4;
	
	/**
	 *断裂比强度平均值：S3
	 */
	@TableField("BREAK_RATE_AVG_S3")
	@ApiModelProperty(value = "断裂比强度平均值：S3")
	private BigDecimal breakRateAvgS3;
	
	/**
	 *断裂比强度平均值：S2
	 */
	@TableField("BREAK_RATE_AVG_S2")
	@ApiModelProperty(value = "断裂比强度平均值：S2")
	private BigDecimal breakRateAvgS2;
	
	/**
	 *断裂比强度平均值：S1
	 */
	@TableField("BREAK_RATE_AVG_S1")
	@ApiModelProperty(value = "断裂比强度平均值：S1")
	private BigDecimal breakRateAvgS1;
	
	/**
	 *Rd平均值
	 */
	@TableField("AVG_RD")
	@ApiModelProperty(value = "Rd平均值")
	private BigDecimal avgRd;
	
	/**
	 *Rd最大值
	 */
	@TableField("MAX_RD")
	@ApiModelProperty(value = "Rd最大值")
	private BigDecimal maxRd;
	
	/**
	 *Rd最小值
	 */
	@TableField("MIN_RD")
	@ApiModelProperty(value = "Rd最小值")
	private BigDecimal minRd;
	
	/**
	 *+b平均值
	 */
	@TableField("AVG_PLUS_B")
	@ApiModelProperty(value = "+b平均值")
	private BigDecimal avgPlusB;
	
	/**
	 *+b最大值
	 */
	@TableField("MAX_PLUS_B")
	@ApiModelProperty(value = "+b最大值")
	private BigDecimal maxPlusB;
	
	/**
	 *+b最小值
	 */
	@TableField("MIN_PLUS_B")
	@ApiModelProperty(value = "+b最小值")
	private BigDecimal minPlusB;
	
	/**
	 *主体长度级
	 */
	@TableField("MAIN_LENGTH")
	@ApiModelProperty(value = "主体长度级")
	private String mainLength;
	
	/**
	 *马克隆A级比率
	 */
	@TableField("MKL_GRADE_A")
	@ApiModelProperty(value = "马克隆A级比率")
	private BigDecimal mklGradeA;
	
	/**
	 *马克隆B级比率
	 */
	@TableField("MKL_GRADE_B")
	@ApiModelProperty(value = "马克隆B级比率")
	private BigDecimal mklGradeB;
	
	/**
	 *马克隆C级比率
	 */
	@TableField("MKL_GRADE_C")
	@ApiModelProperty(value = "马克隆C级比率")
	private BigDecimal mklGradeC;
	
	/**
	 *证书编号
	 */
	@TableField("CERTIFICATE_NO")
	@ApiModelProperty(value = "证书编号")
	private String certificateNo;
	
	/**
	 *异性纤维比率
	 */
	@TableField("FORE_FIBER_RATE")
	@ApiModelProperty(value = "异性纤维比率")
	private BigDecimal foreFiberRate;
	
	/**
	 *生产年度
	 */
	@TableField("WORK_YEAR")
	@ApiModelProperty(value = "生产年度")
	private BigDecimal workYear;
	
	/**
	 *加工厂代码
	 */
	@TableField("ENTERPRISE_CODE")
	@ApiModelProperty(value = "加工厂代码")
	private String enterpriseCode;
	
	/**
	 *创建日期
	 */
	@TableField("CREATE_TIME")
	@ApiModelProperty(value = "创建日期")
	private Date createTime;
	
	/**
	 *主体品级
	 */
	@TableField("SUBJECT_LEVEL")
	@ApiModelProperty(value = "主体品级")
	private String subjectLevel;
	
	/**
	 *品级1级比率
	 */
	@TableField("LEVEL1_RATE")
	@ApiModelProperty(value = "品级1级比率")
	private BigDecimal level1Rate;
	
	/**
	 *品级2级比率
	 */
	@TableField("LEVEL2_RATE")
	@ApiModelProperty(value = "品级2级比率")
	private BigDecimal level2Rate;
	
	/**
	 *品级3级比率
	 */
	@TableField("LEVEL3_RATE")
	@ApiModelProperty(value = "品级3级比率")
	private BigDecimal level3Rate;
	
	/**
	 *品级4级比率
	 */
	@TableField("LEVEL4_RATE")
	@ApiModelProperty(value = "品级4级比率")
	private BigDecimal level4Rate;
	
	/**
	 *品级5级比率
	 */
	@TableField("LEVEL5_RATE")
	@ApiModelProperty(value = "品级5级比率")
	private BigDecimal level5Rate;
	
	/**
	 *品级6级比率
	 */
	@TableField("LEVEL6_RATE")
	@ApiModelProperty(value = "品级6级比率")
	private BigDecimal level6Rate;
	
	/**
	 *品级7级比率
	 */
	@TableField("LEVEL7_RATE")
	@ApiModelProperty(value = "品级7级比率")
	private BigDecimal level7Rate;
	
	/**
	 *最多颜色级
	 */
	@TableField("MOST_COLOR_GRADE")
	@ApiModelProperty(value = "最多颜色级")
	private String mostColorGrade;
	
	/**
	 *最多颜色比率
	 */
	@TableField("MOST_COLOR_VALUE")
	@ApiModelProperty(value = "最多颜色比率")
	private BigDecimal mostColorValue;
	
	/**
	 *最多马克隆等级
	 */
	@TableField("MOST_MKL_GRADE")
	@ApiModelProperty(value = "最多马克隆等级")
	private String mostMklGrade;
	
	/**
	 *最多马克隆比率
	 */
	@TableField("MOST_MKL_VALUE")
	@ApiModelProperty(value = "最多马克隆比率")
	private BigDecimal mostMklValue;
	
	/**
	 *实验室代码
	 */
	@TableField("LAB_CODE")
	@ApiModelProperty(value = "实验室代码")
	private String labCode;
	
	/**
	 *最多长度级
	 */
	@TableField("MOST_LENGTH_GRADE")
	@ApiModelProperty(value = "最多长度级")
	private String mostLengthGrade;
	
	/**
	 *最多长度级占比
	 */
	@TableField("MOST_LENGTH_VALUE")
	@ApiModelProperty(value = "最多长度级占比")
	private Integer mostLengthValue;
	
	/**
	 *2:二次组批数据,1:首次组批
	 */
	@TableField("TYPE")
	@ApiModelProperty(value = "2:二次组批数据,1:首次组批")
	private String type;
	
	/**
	 *最多品级
	 */
	@TableField("MOST_GRADE")
	@ApiModelProperty(value = "最多品级")
	private String mostGrade;
	
	/**
	 *最多品级占比
	 */
	@TableField("MOST_GRADE_VALUE")
	@ApiModelProperty(value = "最多品级占比")
	private String mostGradeValue;


	/**
	 * 公检证书发布时间
	 */
	@TableField("SYNC_TIME")
	@ApiModelProperty(value = "公检证书发布时间，既同步智棉的创建时间")
	private Date syncTime;


	/**
	 * 检验日期
	 */
	@TableField("INSPECT_DATE")
	@ApiModelProperty(value = "检验日期")
	private Date inspectDate;
	/**
	 *智棉监管类型
	 */
	@TableField("BIZ_TYPE")
	@ApiModelProperty(value = "智棉监管类型(1:监管棉；2：非监管棉；null:未确认)")
	private String bizType;


	/**
	 *更新时间
	 */
	@TableField("UPDATE_TIME")
	@ApiModelProperty(value = "更新时间")
	private Date updateTime;
	
	/**
	 *创建人
	 */
	@TableField("CREATE_BY")
	@ApiModelProperty(value = "创建人")
	private String createBy;
	
	/**
	 *更新人
	 */
	@TableField("UPDATE_BY")
	@ApiModelProperty(value = "更新人")
	private String updateBy;
	

}