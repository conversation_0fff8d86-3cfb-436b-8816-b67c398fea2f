package com.applet.trade.hall.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.sinosoft.dandelion.system.client.params.DataItem;
import com.sinosoft.dandelion.system.client.serializer.DataItemCodeDeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Title: AllStockResVo.java
 * @Description: 全部资源列表 vo
 * <AUTHOR>
 * @date 2025/6/11
 * @version V1.0
 */
@Data
public class BuyerStockResVo implements Serializable {

	@ApiModelProperty(value = "商品ID")
	private String id;

	@ApiModelProperty(value = "商品码")
	private String stockCode;

	@ApiModelProperty(value = "仓单号")
	private String warehouseReceiptNo;

	@ApiModelProperty(value = "批号")
	private String batchNo;

	@ApiModelProperty(value = "颜色级")
	private String colorGrade;

	@ApiModelProperty(value = "颜色级比例")
	private String colorGradeStr;

	@ApiModelProperty(value = "颜色级比率：白棉1级")
	private BigDecimal whiteCottonL1;

	@ApiModelProperty(value = "颜色级比率：白棉2级")
	private BigDecimal whiteCottonL2;

	@ApiModelProperty(value = "颜色级比率：白棉3级")
	private BigDecimal whiteCottonL3;

	@ApiModelProperty(value = "颜色级比率：白棉4级")
	private BigDecimal whiteCottonL4;

	@ApiModelProperty(value = "颜色级比率：白棉5级")
	private BigDecimal whiteCottonL5;

	@ApiModelProperty(value = "颜色级比率：白棉6级")
	private BigDecimal whiteCottonL6;

	@ApiModelProperty(value = "颜色级比率：淡点污棉1级")
	private BigDecimal spotCottonL1;

	@ApiModelProperty(value = "颜色级比率：淡点污棉2级")
	private BigDecimal spotCottonL2;

	@ApiModelProperty(value = "颜色级比率：淡点污棉3级")
	private BigDecimal spotCottonL3;

	@ApiModelProperty(value = "颜色级比率：淡点污棉4级")
	private BigDecimal spotCottonL4;

	@ApiModelProperty(value = "颜色级比率：淡黄染棉1级")
	private BigDecimal yellowIshCottonL1;

	@ApiModelProperty(value = "颜色级比率：淡黄染棉2级")
	private BigDecimal yellowIshCottonL2;

	@ApiModelProperty(value = "颜色级比率：淡黄染棉3级")
	private BigDecimal yellowIshCottonL3;

	@ApiModelProperty(value = "颜色级比率：黄染棉1级")
	private BigDecimal yellowCottonL1;

	@ApiModelProperty(value = "颜色级比率：黄染棉2级")
	private BigDecimal yellowCottonL2;

	@ApiModelProperty(value = "长度")
	private BigDecimal avgLength;

	@ApiModelProperty(value = "强力")
	private String breakValue;

	@ApiModelProperty(value = "长整")
	private String uniformityAverageValue;

	@ApiModelProperty(value = "含杂")
	private String impurityRate;

	@ApiModelProperty(value = "回潮")
	private String moistureRate;

	@ApiModelProperty(value = "马值")
	private BigDecimal avgMkl;

	@ApiModelProperty(value = "最大马值")
	private BigDecimal maxMkl;

	@ApiModelProperty(value = "最小马值")
	private BigDecimal minMkl;

	@ApiModelProperty(value = "重量(吨)")
	private BigDecimal conditionedWeight;

	@ApiModelProperty(value = "产地")
	private String intactPlace;

	@ApiModelProperty(value = "仓库")
	private String storageWhsName;

	@ApiModelProperty(value = "报价方式1一口价2、买方点价3卖方点价4预售5预购")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem tradeQuoteType;

	@ApiModelProperty(value = "期货合约")
	private String futureCode;

	@ApiModelProperty(value = "基差价格")
	private BigDecimal quotePrice;

	@ApiModelProperty(value = "备注")
	private String remark;

	@ApiModelProperty(value = "当前报价交易商")
	private String quoteTraderCode;

	@ApiModelProperty(value = "是否当前交易商报价-1、否1、是")
	private String traderQuoteStatus = "-1";

	@ApiModelProperty(value = "中棉协升贴水")
	private BigDecimal ZMX;

	@ApiModelProperty(value = "郑商所升贴水")
	private BigDecimal ZSS;

	@ApiModelProperty(value = "加工单位代码")
	private String factoryCode;

	@ApiModelProperty(value = "加工单位")
	private String factoryName;

}

