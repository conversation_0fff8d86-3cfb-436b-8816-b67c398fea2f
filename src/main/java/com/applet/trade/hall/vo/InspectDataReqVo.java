package com.applet.trade.hall.vo;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Title: InspectDataReqVo.java
 * @Description: 公检信息查询vo
 * <AUTHOR>
 * @date 2025/5/24
 * @version V1.0
 */
@Data
public class InspectDataReqVo implements Serializable {

	//是否开启颜色比率占比
	private boolean isColorRate = false;

	//颜色级
	private List<String> colorGrade;

	//开始颜色比率
	private Double beginColorRate;
	//开始颜色级
	private String beginColor;
	//且
	private boolean isJust = false;
	//结束颜色比率
	private Double endColorRate;
	//结束颜色级
	private String endColor;

	private List<String> endColorList;
	private List<String> beginColorList;

	@ApiModelProperty(value = "长度-最小")
	private String avgLengthMin;

	@ApiModelProperty(value = "长度-最大")
	private String avgLengthMax;

	@ApiModelProperty(value = "强力-最小")
	private String bruteforceMin;

	@ApiModelProperty(value = "强力-最大")
	private String bruteforceMax;

	@ApiModelProperty(value = "马值-最小")
	private String mainMklMin;

	@ApiModelProperty(value = "马值-最大")
	private String mainMklMax;

	@ApiModelProperty(value = "长整-最小")
	private String tidyAvgMin;

	@ApiModelProperty(value = "长整-最大")
	private String tidyAvgMax;

	@ApiModelProperty(value = "含杂-最小")
	private String dirtyMin;

	@ApiModelProperty(value = "含杂-最大")
	private String dirtyMax;

	@ApiModelProperty(value = "回潮-最小")
	private String moistureRegainMin;

	@ApiModelProperty(value = "回潮-最大")
	private String moistureRegainMax;

}


