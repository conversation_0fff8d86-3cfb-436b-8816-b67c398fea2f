package com.applet.trade.hall.core;

import com.basiscotton.manager.hall.core.impl.IBasisQuoteService;
import com.basiscotton.manager.hall.vo.BasisQuoteVo;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;

/**
 * @Title: BasisQuoteProcessor.java
 * @Description: 报价处理器
 * <AUTHOR>
 * @date 2025/5/24
 * @version V1.0
 */
public class BasisQuoteProcessor {

	public void process(IBasisQuoteService method, ServiceHandlerContext context, BasisQuoteVo quoteVo) {
		method.createQuote(context,quoteVo);
	}
}
