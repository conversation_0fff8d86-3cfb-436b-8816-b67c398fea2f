package com.applet.trade.hall.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.basiscotton.base.entity.BasisStockEntity;
import com.applet.trade.base.mappers.SmallBasisStockBaseInfoMappers;
import com.applet.trade.base.mappers.SmallBasisStockMappers;
import com.applet.trade.hall.vo.BatchInspectVo;
import com.applet.trade.hall.vo.BatchWeightVo;
import com.applet.trade.hall.vo.QuanlityBaseInfoVo;
import com.applet.trade.basisStock.vo.BasisWhsReceiptVo;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Title: GetStockTelInfo.java
 * @Description: 修改商品联系方式
 * <AUTHOR>
 * @date 2025/6/11
 * @version V1.0
 */
@Service("appHall.getQuanlityInfo.1")
@ApiRequestObject(value = "获取批次组批数据", name = "getQuanlityInfo", groups = {"小程序-基差交易-仓单模块"}, params = {
        @ApiParamMeta(key = "stockCode", desc = "商品编码", type = String.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "baseInfoVo", desc = "组批数据", type = QuanlityBaseInfoVo.class),
})
public class GetQuanlityInfo implements IBusinessService {

    @Resource
    private SmallBasisStockMappers basisStockMappers;

    @Resource
    private SmallBasisStockBaseInfoMappers basisStockBaseInfoMappers;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        String stockCode = context.getValueObject(String.class, "stockCode");

        QuanlityBaseInfoVo baseInfoVo = new QuanlityBaseInfoVo();
        //查询商品信息
        LambdaQueryWrapper<BasisStockEntity> queryWrapper = new LambdaQueryWrapper<BasisStockEntity>();
        queryWrapper.eq(BasisStockEntity::getStockCode, stockCode);
        BasisStockEntity stockEntity = basisStockMappers.selectOne(queryWrapper);
        String wareNo = stockEntity.getWarehouseReceiptNo();
        String batchNo  = stockEntity.getBatchNo();
        if(StringUtil.isNotEmpty(wareNo) && StringUtil.isNotEmpty(batchNo)){
            BasisWhsReceiptVo whsReceiptVo = basisStockBaseInfoMappers.getWhsReceiptVo(wareNo);
            BatchInspectVo batchInspectVo = basisStockBaseInfoMappers.getBatchInspectVo(batchNo);
            BatchWeightVo batchWeightVo = basisStockBaseInfoMappers.getBatchWeightVo(batchNo);

            baseInfoVo.setQuantity(String.valueOf(whsReceiptVo.getQuantity()));
            if (Objects.nonNull(batchInspectVo))
                BeanUtils.copyProperties(batchInspectVo, baseInfoVo);
            if (Objects.nonNull(batchWeightVo))
                BeanUtils.copyProperties(batchWeightVo, baseInfoVo);
        }
        //创建响应实体
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("baseInfoVo", baseInfoVo);
    }
}
