package com.applet.trade.hall.service;

import com.applet.trade.base.mappers.SmallBasisStockMappers;
import com.applet.trade.hall.vo.BuyerStockReqVo;
import com.applet.trade.hall.vo.BuyerStockResVo;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.manager.stock.enums.ColorGradeEnum;
import org.harry.dandelion.framework.core.params.PageParameter;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.harry.dandelion.framework.rep.support.page.PipPagination;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Title: GetAllStockBuyerByPage.java
 * @Description: 修改小程序交易大厅
 * <AUTHOR>
 * @date 2025/6/11
 * @version V1.0
 */
@Service("appHall.getAllStockBuyerByPage.1")
@ApiRequestObject(value = "交易大厅", name = "getAllStockBuyerByPage", groups = {"小程序-基差交易-仓单模块"}, params = {
        @ApiParamMeta(key = "pageParameter", desc = "分页参数", type = PageParameter.class),
        @ApiParamMeta(key = "reqVo", desc = "查询vo", type = BuyerStockReqVo.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "pagination", desc = "查询分页列表", type = BuyerStockResVo.class,pagination = true),
})
public class GetAllStockBuyerByPage implements IBusinessService {

    @Resource
    private SmallBasisStockMappers basisStockMappers;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        PageParameter pageParameter = context.getValueObject(PageParameter.class, "pageParameter");
        BuyerStockReqVo reqVo = context.getValueObject(BuyerStockReqVo.class, "reqVo");
        String currCustomCode = context.getCurrentUserCustomCode();
        reqVo = this.buildInspectSearch(reqVo);
        if(StringUtil.isNotEmpty(reqVo.getIntactPlace())){
            reqVo.setIntactPlaceList(Arrays.asList(reqVo.getIntactPlace().split(",")));
        }
        reqVo.setResourceAuditStatus(BasisCottonConstants.resource_audit_status_3);
        List<String> tradeStatusList = new ArrayList<String>();
        tradeStatusList.add(BasisCottonConstants.trade_status_1);
        tradeStatusList.add(BasisCottonConstants.trade_status_2);
        reqVo.setTradeStatus(tradeStatusList);
        reqVo.setQuoteType(BasisCottonConstants.quote_type_2);
        reqVo.setPricingParty(BasisCottonConstants.pricing_party_1);
        reqVo.setTraderCode(currCustomCode);
        PipPagination<BuyerStockResVo> pipPagination = new PipPagination<>(pageParameter);
        pipPagination = basisStockMappers.getAllStockBuyerByPage(pipPagination, reqVo);
        List<BuyerStockResVo> dataList = pipPagination.getResult();
        if(dataList != null && dataList.size() > 0 ){
            pipPagination.setResult(this.buildData(currCustomCode,dataList));
        }
        //创建响应实体
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("pagination", pipPagination);
    }

    private List<BuyerStockResVo> buildData(String currCustomCode,List<BuyerStockResVo> dataList) {
        for (BuyerStockResVo resVo : dataList) {
            //基差
            if (StringUtil.isNotEmpty(resVo.getQuoteTraderCode())) {
                if(currCustomCode.equals(resVo.getQuoteTraderCode())){
                    resVo.setTraderQuoteStatus(BasisCottonConstants.COMMON_YES);
                }
            }
            //颜色占比处理
            List<String> colorList = new ArrayList<String>();
            if(StringUtil.isNotEmpty(resVo.getWhiteCottonL1())){
                colorList.add("11:"+resVo.getWhiteCottonL1());
            }
            if(StringUtil.isNotEmpty(resVo.getWhiteCottonL2())){
                colorList.add("21:"+resVo.getWhiteCottonL2());
            }
            if(StringUtil.isNotEmpty(resVo.getWhiteCottonL3())){
                colorList.add("31:"+resVo.getWhiteCottonL3());
            }
            if(StringUtil.isNotEmpty(resVo.getWhiteCottonL4())){
                colorList.add("41:"+resVo.getWhiteCottonL4());
            }
            if(StringUtil.isNotEmpty(resVo.getWhiteCottonL5())){
                colorList.add("51:"+resVo.getWhiteCottonL5());
            }
            if(StringUtil.isNotEmpty(resVo.getWhiteCottonL6())){
                colorList.add("61:"+resVo.getWhiteCottonL6());
            }
            if(StringUtil.isNotEmpty(resVo.getSpotCottonL1())){
                colorList.add("12:"+resVo.getSpotCottonL1());
            }
            if(StringUtil.isNotEmpty(resVo.getSpotCottonL2())){
                colorList.add("22:"+resVo.getSpotCottonL2());
            }
            if(StringUtil.isNotEmpty(resVo.getSpotCottonL3())){
                colorList.add("32:"+resVo.getSpotCottonL3());
            }
            if(StringUtil.isNotEmpty(resVo.getSpotCottonL4())){
                colorList.add("42:"+resVo.getSpotCottonL4());
            }
            if(StringUtil.isNotEmpty(resVo.getYellowIshCottonL1())){
                colorList.add("13:"+resVo.getYellowIshCottonL1());
            }
            if(StringUtil.isNotEmpty(resVo.getYellowIshCottonL2())){
                colorList.add("23:"+resVo.getYellowIshCottonL2());
            }
            if(StringUtil.isNotEmpty(resVo.getYellowIshCottonL3())){
                colorList.add("33:"+resVo.getYellowIshCottonL3());
            }

            if(StringUtil.isNotEmpty(resVo.getYellowCottonL1())){
                colorList.add("14:"+resVo.getYellowCottonL1());
            }
            if(StringUtil.isNotEmpty(resVo.getYellowCottonL2())){
                colorList.add("24:"+resVo.getYellowCottonL2());
            }
            resVo.setColorGradeStr(String.join(" ", colorList));
            // 最终拼接结果
        }
        return dataList;
    }

    private BuyerStockReqVo buildInspectSearch(BuyerStockReqVo reqVo){
        //开启颜色占比查询
        //设置颜色级占比筛选
        if (reqVo.isColorRate()) {
            //选择占比，不使用颜色级筛选条件
            reqVo.setColorGrade(new ArrayList<>());
            String beginColor = reqVo.getBeginColor();
            List<String> result = getValuesLessThan(ColorGradeEnum.getNameByCode(beginColor));
            //myInspectDataQueryVo.setBeginColor(ColorGradeEnum.getNameByCode(beginColor));
            reqVo.setBeginColorList(result);
        }
        //勾选且
        if (reqVo.isJust()){
            String endColor = reqVo.getEndColor();
            List<String> result = printValuesAfterTarget(ColorGradeEnum.getNameByCode(endColor));
            //myInspectDataQueryVo.setEndColor(ColorGradeEnum.getNameByCode(endColor));
            reqVo.setEndColorList(result);
        }
        return reqVo;
    }

    private static List<String> getValuesLessThan(String targetValue) {
        Map<String,String> map = new HashMap<String,String>();
        map.put("1","WHITE_COTTON_L1");
        map.put("2","WHITE_COTTON_L2");
        map.put("3","WHITE_COTTON_L3");
        map.put("4","WHITE_COTTON_L4");
        map.put("5","WHITE_COTTON_L5");
        map.put("6","WHITE_COTTON_L6");
        map.put("7","SPOT_COTTON_L1");
        map.put("8","SPOT_COTTON_L2");
        map.put("9","SPOT_COTTON_L3");
        map.put("10","SPOT_COTTON_L4");
        map.put("11","YELLOW_ISH_COTTON_L1");
        map.put("12","YELLOW_ISH_COTTON_L2");
        map.put("13","YELLOW_ISH_COTTON_L3");
        map.put("14","YELLOW_COTTON_L1");
        map.put("15","YELLOW_COTTON_L2");
        List<String> targetKeys = new ArrayList<>();
        // 找到所有值为targetValue的键
        for (Map.Entry<String, String> entry : map.entrySet()) {
            if (entry.getValue().equals(targetValue)) {
                targetKeys.add(entry.getKey());
            }
        }
        if (targetKeys.isEmpty()) {
            return Collections.emptyList(); // 目标值不存在
        }
        // 找到最大的键值作为基准
        int maxKey = targetKeys.stream()
                .mapToInt(Integer::parseInt)
                .max()
                .getAsInt();
        // 收集所有键小于maxKey的value
        List<String> result = new ArrayList<>();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            int key = Integer.parseInt(entry.getKey());
            if (key < maxKey) {
                result.add(entry.getValue());
            }
        }
        result.add(targetValue);
        return result;
    }

    private static List<String> printValuesAfterTarget(String targetValue ) {
        Map<String,String> map = new HashMap<String,String>();

        map.put("1","WHITE_COTTON_L1");
        map.put("2","WHITE_COTTON_L2");
        map.put("3","WHITE_COTTON_L3");
        map.put("4","WHITE_COTTON_L4");
        map.put("5","WHITE_COTTON_L5");
        map.put("6","WHITE_COTTON_L6");
        map.put("7","SPOT_COTTON_L1");
        map.put("8","SPOT_COTTON_L2");
        map.put("9","SPOT_COTTON_L3");
        map.put("10","SPOT_COTTON_L4");
        map.put("11","YELLOW_ISH_COTTON_L1");
        map.put("12","YELLOW_ISH_COTTON_L2");
        map.put("13","YELLOW_ISH_COTTON_L3");
        map.put("14","YELLOW_COTTON_L1");
        map.put("15","YELLOW_COTTON_L2");

        // 收集所有与目标value匹配的key，并转换为整数
        List<Integer> targetKeys = new ArrayList<>();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            if (entry.getValue().equals(targetValue)) {
                targetKeys.add(Integer.parseInt(entry.getKey()));
            }
        }
        if (targetKeys.isEmpty()) {
            return Collections.emptyList();
        }

        // 找到最大的目标key
        int    tempkey = Collections.max(targetKeys);



        // 将Map的所有key转换为整数并排序
        List<Integer> sortedKeys = map.keySet().stream()
                .map(Integer::parseInt)
                .sorted()
                .collect(Collectors.toList());

        // 收集所有大于maxKey的key对应的value
        List<String> result = new ArrayList<>();
        result.add(targetValue);
        for (Integer key : sortedKeys) {
            if (key > tempkey) {
                result.add(map.get(String.valueOf(key)));
            }
        }

        return result;
    }
}
