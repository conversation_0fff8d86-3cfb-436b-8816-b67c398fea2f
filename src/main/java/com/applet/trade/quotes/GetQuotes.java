package com.applet.trade.quotes;

import com.basiscotton.futurequotes.vo.RealTimeQuoteVo;
import com.github.benmanes.caffeine.cache.Cache;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: GetQuotes.java
 * @Description: 获取行情信息
 * @date 2025/5/18 23:51
 */
@Service("appQuotes.getQuotes.1")
@ApiRequestObject(value = "获取行情信息", name = "getQuotes", groups = {"小程序-基差交易-仓单模块"}, params = {
        @ApiParamMeta(key = "futureCode", desc = "合约编号", type = String.class)
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "realTimeQuoteVoList", desc = "行情信息", type = RealTimeQuoteVo.class, multipart = true),
})
public class GetQuotes implements IBusinessService {

   @Resource
   private Cache<String, RealTimeQuoteVo> quotesCache;
   @Override
   public void doVerify(ServiceHandlerContext context) {

   }

   @Override
   public void doWork(ServiceHandlerContext context) {

      String futureCode = context.getValueObject(String.class, "futureCode");
      List<RealTimeQuoteVo> realTimeQuoteVoList = new ArrayList<>();
      if(futureCode==null){
         //获取所有缓存数据,转入realTimeQuoteVoList
          quotesCache.asMap().values().forEach(realTimeQuoteVo -> {
              realTimeQuoteVoList.add(realTimeQuoteVo);
          });
      }else{
          realTimeQuoteVoList.add(quotesCache.getIfPresent(futureCode));
      }

      //创建响应实体
      this.createSuccessResponse(context);
      context.getResponseBody().getDataSet().put("realTimeQuoteVoList",realTimeQuoteVoList);
   }
}
