package com.applet.trade.basisStock.vo;

import com.applet.trade.hall.vo.InspectDataReqVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Title: AllStockReqVo.java
 * @Description: 全部资源列表查询vo
 * <AUTHOR>
 * @date 2025/5/24
 * @version V1.0
 */
@Data
public class BuyerStockReqVo extends InspectDataReqVo {

	//默认查询项
	private String resourceAuditStatus;
	private String quoteType;
	private String pricingParty;
	private List<String> tradeStatus;
	private String traderCode;

	//页面查询项
	@ApiModelProperty(value = "产地")
	private String intactPlace;

	@ApiModelProperty(value = "产地")
	private List<String> intactPlaceList;

	@ApiModelProperty(value = "棉花年度")
	private String productYear;

	@ApiModelProperty(value = "采摘方式")
	private String whsPickMode;

	@ApiModelProperty(value = "加工单位")
	private String factoryName;

	@ApiModelProperty(value = "仓库名称")
	private String storageWhsName;

	@ApiModelProperty(value = "批号")
	private String batchNo;


}

