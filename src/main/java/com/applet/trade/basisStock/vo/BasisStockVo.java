package com.applet.trade.basisStock.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: BasisStockVo.java
 * @Description: 仓单资源提交VO
 * @date 2025/5/25 19:18
 */
@Data
public class BasisStockVo implements Serializable {


    /**
     * 期货合约
     */
    @ApiModelProperty(value="期货合约")
    private String futureCode;

    @ApiModelProperty(value="点价方1-买方 2-卖方")
    private String pricingParty;

    /**
     *基差价格
     */
    @ApiModelProperty(value = "基差价格")
    private BigDecimal basisPrice;

    /**
     *点价有效期
     */
    @ApiModelProperty(value = "点价有效期")
    private Date pricingValidTime;

    /**
     * 包干费承担方 undertaker_type
     */
    @ApiModelProperty(value = "包干费承担方: 1-卖方 2-买方 3-其它企业")
    private String undertakerType;

    @ApiModelProperty(value = "其它包干方名称")
    private String undertakerOtherName;

    @ApiModelProperty(value = "包干费承担时间")
    private Integer undertakerTime;

    /**
     * 运补申领方 subsidy_apply_type
     */
    @ApiModelProperty(value = "运补申领方: 1-卖方 2-买方 3-其它企业")
    private String subsidyApplyType;

    @ApiModelProperty(value = "其它运补方名称")
    private String subsidyApplyOtherName;

    /**
     * 备注
     */
    @ApiModelProperty(value="备注")
    private String remark;
}
