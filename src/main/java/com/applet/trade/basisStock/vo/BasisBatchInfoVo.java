package com.applet.trade.basisStock.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.sinosoft.dandelion.system.client.params.DataItem;
import com.sinosoft.dandelion.system.client.serializer.DataItemCodeDeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: BatchInfoVo.java
 * @Description: 批次信息表-源于仓储
 * @date 2025/1/11 21:55
 */
@Data
public class BasisBatchInfoVo {
 /**
  * traderCode 交易商代码
  * traderName 交易商名称
  * BATCH_NO 批号
  * info.CONDITIONED_WEIGHT 公定重
  * weight.GROSS_WEIGHT 毛重
  * weight.PACKET_NUM 件数
  * weight.REPOSITORY 存放仓库
  * info.WHS_PICK_MODE 采摘方式
  * weight.PLACE 产地
  * weight.PROCESS_COMPANY 加工单位
  * inspect.AVG_LENGTH 平均长度
  * inspect.AVG_BREAK_RATE 断裂比强度
  * inspect.MKL_A 马克隆各档比率：A档
  * inspect.MKL_B1 马克隆各档比率：B1档
  * inspect.MKL_B2 马克隆各档比率：B2档
  * inspect.MKL_C1 马克隆各档比率：C1档
  * inspect.MKL_C2 马克隆各档比率：C2档
  * inspect.WHITE_COTTON_L1  颜色级比率：白棉1级 11
  * inspect.WHITE_COTTON_L2  颜色级比率：白棉2级 21
  * inspect.WHITE_COTTON_L3  颜色级比率：白棉3级 31
  * inspect.WHITE_COTTON_L4  颜色级比率：白棉4级 41
  * inspect.WHITE_COTTON_L5  颜色级比率：白棉5级 51
  * inspect.WHITE_COTTON_L6  颜色级比率：白棉6级 61
  * inspect.SPOT_COTTON_L1   颜色级比率：淡点污棉1级 12
  * inspect.SPOT_COTTON_L2   颜色级比率：淡点污棉2级 22
  * inspect.SPOT_COTTON_L3   颜色级比率：淡点污棉3级 32
  * inspect.SPOT_COTTON_L4   颜色级比率：淡点污棉4级 42
  * inspect.YELLOW_ISH_COTTON_L1  颜色级比率：淡黄染棉1级 13
  * inspect.YELLOW_ISH_COTTON_L2  颜色级比率：淡黄染棉2级 23
  * inspect.YELLOW_ISH_COTTON_L3  颜色级比率：淡黄染棉3级 33
  * inspect.YELLOW_COTTON_L1  颜色级比率：黄染棉1级   14
  * inspect.YELLOW_COTTON_L2  颜色级比率：黄染棉2级   24
  * weight.MOISTURE_RATE 回潮率
  * weight.IMPURITY_RATE 含杂率
  * inspect.AVG_LEN_UNIFORMITY 长度整齐度
  * inspect.GINNING_QUALITY_RATE_P1 轧工质量P1
  * inspect.GINNING_QUALITY_RATE_P2 轧工质量P2
  * inspect.GINNING_QUALITY_RATE_P3 轧工质量P3
  * weight.INSPECT_DATE  公检日期
  * info.WAREHOUSE_RECEIPT_STATUS 仓单状态
  * inspect.MAX_MKL 马克隆值级最大值
  * inspect.MIN_MKL 马克隆值级最小值
  */

   //交易商代码
   @Column(name = "traderCode")
   @ApiModelProperty(value="交易商代码")
   private String traderCode;

   //交易商名称
   @Column(name = "traderName")
   @ApiModelProperty(value="交易商名称")
   private String traderName;

 //批号
   @Column(name = "BATCH_NO")
   @ApiModelProperty(value="批号")
   private String batchNo;

   //仓单号
    @Column(name = "WAREHOUSE_RECEIPT_NUMBER")
    @ApiModelProperty(value="仓单号")
    private String warehouseReceiptNumber;

   @Column(name = "CONDITIONED_WEIGHT")
   @ApiModelProperty(value="公定重")
   private BigDecimal conditionedWeight;

   @Column(name = "GROSS_WEIGHT")
   @ApiModelProperty(value="毛重")
   private BigDecimal grossWeight;

   //marks_weight  唛头重量
    @Column(name = "MARK_WEIGHT")
    @ApiModelProperty(value="唛头重量")
    private BigDecimal markWeight;


   @Column(name = "PACKET_NUM")
   @ApiModelProperty(value="件数")
   private Integer packetNum;

  @Column(name = "storage_whs_code")
  @ApiModelProperty(value = "仓库代码")
  private String storageWhsCode;

   @Column(name = "REPOSITORY")
   @ApiModelProperty(value="存放仓库")
   private String repository;

   @Column(name = "WHS_PICK_MODE")
   @ApiModelProperty(value="采摘方式")
   private String whsPickMode;

   @Column(name = "PLACE")
   @ApiModelProperty(value="产地")
   private String place;

    @Column(name = "PLACE_NAME")
    @ApiModelProperty(value="具体产地")
    private String placeName;

  @Column(name = "PROCESS_CODE")
  @ApiModelProperty(value="加工单位代码")
  private String processCode;

   @Column(name = "PROCESS_COMPANY")
   @ApiModelProperty(value="加工单位")
   private String processCompany;

   @Column(name = "AVG_LENGTH")
   @ApiModelProperty(value="平均长度")
   private BigDecimal avgLength;

   @Column(name = "AVG_BREAK_RATE")
   @ApiModelProperty(value="断裂比强度")
   private BigDecimal avgBreakRate;

   //AVG_MKL 马克隆值平均值
   @Column(name = "AVG_MKL")
   @ApiModelProperty(value="马克隆值平均值")
   private BigDecimal avgMkl;

   @Column(name = "MKL_A")
   @ApiModelProperty(value="马克隆各档比率：A档")
   private String mklA;

   @Column(name = "MKL_B1")
   @ApiModelProperty(value="马克隆各档比率：B1档")
   private String mklB1;

   @Column(name = "MKL_B2")
   @ApiModelProperty(value="马克隆各档比率：B2档")
   private String mklB2;

   @Column(name = "MKL_C1")
   @ApiModelProperty(value="马克隆各档比率：C1档")
   private String MklC1;

   @Column(name = "MKL_C2")
   @ApiModelProperty(value="马克隆各档比率：C2档")
   private String mklC2;

   @Column(name = "WHITE_COTTON_L1")
   @ApiModelProperty(value="颜色级比率：白棉1级 11")
   private BigDecimal whiteCottonL1;

   @Column(name = "WHITE_COTTON_L2")
   @ApiModelProperty(value="颜色级比率：白棉2级 21")
   private BigDecimal whiteCottonL2;

   @Column(name = "WHITE_COTTON_L3")
   @ApiModelProperty(value="颜色级比率：白棉3级 31")
   private BigDecimal whiteCottonL3;

   @Column(name = "WHITE_COTTON_L4")
   @ApiModelProperty(value="颜色级比率：白棉4级 41")
   private BigDecimal whiteCottonL4;

   @Column(name = "WHITE_COTTON_L5")
   @ApiModelProperty(value="颜色级比率：白棉5级 51")
   private BigDecimal whiteCottonL5;

   @Column(name = "WHITE_COTTON_L6")
   @ApiModelProperty(value="颜色级比率：白棉6级 61")
   private BigDecimal whiteCottonL6;

   @Column(name = "SPOT_COTTON_L1")
   @ApiModelProperty(value="颜色级比率：淡点污棉1级 12")
   private BigDecimal spotCottonL1;

   @Column(name = "SPOT_COTTON_L2")
   @ApiModelProperty(value="颜色级比率：淡点污棉2级 22")
   private BigDecimal spotCottonL2;

   @Column(name = "SPOT_COTTON_L3")
   @ApiModelProperty(value="颜色级比率：淡点污棉3级 32")
   private BigDecimal spotCottonL3;

   @Column(name = "SPOT_COTTON_L4")
   @ApiModelProperty(value="颜色级比率：淡点污棉4级 42")
   private BigDecimal spotCottonL4;

   @Column(name = "YELLOW_ISH_COTTON_L1")
   @ApiModelProperty(value="颜色级比率：淡黄染棉1级 13")
   private BigDecimal yellowIshCottonL1;

   @Column(name = "YELLOW_ISH_COTTON_L2")
   @ApiModelProperty(value="颜色级比率：淡黄染棉2级 23")
   private BigDecimal yellowIshCottonL2;

   @Column(name = "YELLOW_ISH_COTTON_L3")
   @ApiModelProperty(value="颜色级比率：淡黄染棉3级 33")
   private BigDecimal yellowIshCottonL3;

   @Column(name = "YELLOW_COTTON_L1")
   @ApiModelProperty(value="颜色级比率：黄染棉1级   14")
   private BigDecimal yellowCottonL1;

   @Column(name = "YELLOW_COTTON_L2")
   @ApiModelProperty(value="颜色级比率：黄染棉2级   24")
   private BigDecimal yellowCottonL2;

   @Column(name = "MOISTURE_RATE")
   @ApiModelProperty(value="回潮率")
   private String moistureRate;

   @Column(name = "IMPURITY_RATE")
   @ApiModelProperty(value="含杂率")
   private String impurityRate;

   @Column(name = "AVG_LEN_UNIFORMITY")
   @ApiModelProperty(value="长度整齐度")
   private String avgLenUniformity;

   @Column(name = "GINNING_QUALITY_RATE_P1")
   @ApiModelProperty(value="轧工质量P1")
   private String ginningQualityRateP1;

   @Column(name = "GINNING_QUALITY_RATE_P2")
   @ApiModelProperty(value="轧工质量P2")
   private String ginningQualityRateP2;

   @Column(name = "GINNING_QUALITY_RATE_P3")
   @ApiModelProperty(value="轧工质量P3")
   private String ginningQualityRateP3;

   @Column(name = "INSPECT_DATE")
   @ApiModelProperty(value="公检日期")
   private Date inspectDate;

   @Column(name = "WAREHOUSE_RECEIPT_STATUS")
   @ApiModelProperty(value="仓单状态")
   private String warehouseReceiptStatus;

   //product_year 棉花年度
   @Column(name = "PRODUCT_YEAR")
   @ApiModelProperty(value="棉花年度")
   private String productYear;

    @Column(name = "WHS_CODE")
    @ApiModelProperty(value = "仓库代码")
    private String whsCode;

    @Column(name = "WHS_NAME")
    @ApiModelProperty(value = "仓库名称")
    private String whsName;

    @Column(name = "WHS_SUPERVISION_STATUS")
    @ApiModelProperty(value = "资金监管状态：1、正常 2、三方监管-籽棉贷款 3、三方监管-皮棉调销 4、三方监管-融资监管状态 5、质押监管 6、质押及三方监管")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem whsSupervisionStatus;

    @Column(name = "WHS_PLEDGE_STATUS")
    @ApiModelProperty(value = "资金服务状态：1:正常、2:仓单质押、3:合作销售、4:代理采购、5:委托融资、6:贸易融资、7:代拍融资、8:国储棉融资、9:协商融资、10:竞卖融资")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem whsPledgeStatus;

    @Column(name = "WHS_PLEDGE_CHANNEL")
    @ApiModelProperty(value = "金融业务渠道：1正常、2购销、3金益棉、4棉贸通、5E棉通、6棉E通、7全棉通、8邮棉贷、9农商银行")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
     private DataItem whsPledgeChannel;

    @Column(name = "LOAN_COMPANY_CODE")
    @ApiModelProperty(value = "贷款公司代码")
    private String loanCompanyCode;

    @Column(name = "LOAN_COMPANY_NAME")
    @ApiModelProperty(value = "贷款公司名称")
    private String loanCompanyName;

    @Column(name = "STOCK_NAME")
    @ApiModelProperty(value="品名 1:长绒棉 2:细绒棉 3:彩棉 4:籽棉 5:棉浆粕 6:短绒 7:棉纱 8:涤纶短纤 9:粘胶短纤-batch_info")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem stockName;

    @Column(name = "MARK_DESC")
    @ApiModelProperty(value="等级-wareinfo")
    private String markDesc;

    @Column(name = "FACTORY_NAME")
    @ApiModelProperty(value="加工单位-wareinfo")
    private String factoryName;

    @Column(name = "COLOR_GRADE_CODE")
    @ApiModelProperty(value="颜色级-wareinfo")
    private String colorGradeCode;

    @Column(name = "MOISTURE_REGAIN")
    @ApiModelProperty(value="回潮率-wareinfo")
    private BigDecimal moistureRegain;

    @Column(name = "DIRTY")
    @ApiModelProperty(value="含杂率-wareinfo")
    private BigDecimal dirty;

    @Column(name = "TIDY_AVG")
    @ApiModelProperty(value="长度整齐度-wareinfo")
    private BigDecimal tidyAvg;

    @Column(name = "ROLLING_QUALITY_AVG")
    @ApiModelProperty(value="轧工质量-wareinfo")
    private String rollingQualityAvg;

    @Column(name = "MAIN_LENGTH")
    @ApiModelProperty(value="主体长度-batchInspect")
    private String mainLength;

    @Column(name = "MAIN_MKL")
    @ApiModelProperty(value="马克龙档级-batchInspect")
    private String mainMkl;

    //件数
    @Column(name = "QUANTITY")
    @ApiModelProperty(value="件数-wareinfo")
    private Integer quantity;

   //马克隆值级最大值
   @Column(name = "MAX_MKL")
   @ApiModelProperty(value="马克隆值级最大值-batchInspect")
   private BigDecimal maxMkl;

   //马克隆值级最小值
   @Column(name = "MIN_MKL")
   @ApiModelProperty(value="马克隆值级最小值-batchInspect")
   private BigDecimal minMkl;


 /**
  * 	sbi.WHS_SUPERVISION_STATUS,资金监管状态：1、正常 2、三方监管-籽棉贷款 3、三方监管-皮棉调销 4、三方监管-融资监管状态 5、质押监管 6、质押及三方监管
  *     sbi.WHS_PLEDGE_STATUS,     资金服务状态：1:正常、2:仓单质押、3:合作销售、4:代理采购、5:委托融资、6:贸易融资、7:代拍融资、8:国储棉融资、9:协商融资、10:竞卖融资
  *     sbi.WHS_PLEDGE_CHANNEL,    金融业务渠道：1正常、2购销、3金益棉、4棉贸通、5E棉通、6棉E通、7全棉通、8邮棉贷、9农商银行
  *     STSA.LOAN_COMPANY_CODE,     -- 三方监管协议 STG_THREE_SUPERVISE_AGREEMENT
  * 	STSA.LOAN_COMPANY_NAME,     -- 三方监管协议 STG_THREE_SUPERVISE_AGREEMENT
  *     ssa.SUPERVISE_STATUS,       --监管申请表STG_SUPERVISE_APPLY 监管状态：1、未生效 2、已生效 3、已失效  --不提取
  */
}
