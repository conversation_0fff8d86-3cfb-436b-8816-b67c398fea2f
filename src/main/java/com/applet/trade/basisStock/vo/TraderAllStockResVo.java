package com.applet.trade.basisStock.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.sinosoft.dandelion.system.client.params.DataItem;
import com.sinosoft.dandelion.system.client.serializer.DataItemCodeDeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Title: TraderAllStockResVo.java
 * @Description: 全部资源列表 vo
 * <AUTHOR>
 * @date 2025/5/24
 * @version V1.0
 */
@Data
public class TraderAllStockResVo implements Serializable {

	@ApiModelProperty(value="主键")
	@JSONField(serializeUsing = ToStringSerializer.class)
	private Long id;

	@ApiModelProperty(value = "商品码")
	private String stockCode;

	@ApiModelProperty(value = "资源审核状态: 1-未提交 2-待审核 3-已上市 4-已取消")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem resourceAuditStatus;

	@ApiModelProperty(value = "报价方式1一口价2、买方点价3卖方点价4预售5预购")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem tradeQuoteType;

	@ApiModelProperty(value = "资源交易方式: 1-打捆 2-单批")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem resourceTradeType;

	@ApiModelProperty(value = "资源展示方式: 1-公开 2-指定")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem resourceDisplayType;

	@ApiModelProperty(value="期货合约")
	private String futureCode;

	@ApiModelProperty(value = "价格")
	private BigDecimal stockPrice;

	@ApiModelProperty(value = "点价有效期")
	private Date pricingValidTime;

	@ApiModelProperty(value = "包干费承担方: 1-卖方 2-买方 3-其它企业")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem undertakerType;

	@ApiModelProperty(value = "包干费承担时间")
	private Integer undertakerTime;

	@ApiModelProperty(value = "运补申领方: 1-卖方 2-买方 3-其它企业")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem subsidyApplyType;

	@ApiModelProperty(value = "仓单号")
	private String warehouseReceiptNo;

	@ApiModelProperty(value = "批号")
	private String batchNo;

	@ApiModelProperty(value = "件数")
	private Integer quantity;

	@ApiModelProperty(value = "唛头重量")
	private BigDecimal marksWeight;

	@ApiModelProperty(value = "公定重量")
	private BigDecimal conditionedWeight;

	@ApiModelProperty(value = "颜色级")
	private String colorGrade;

	@ApiModelProperty(value = "长度")
	private BigDecimal avgLength;

	@ApiModelProperty(value = "强力")
	private String breakValue;

	@ApiModelProperty(value = "长整")
	private String uniformityAverageValue;

	@ApiModelProperty(value = "含杂")
	private String impurityRate;

	@ApiModelProperty(value = "回潮")
	private String moistureRate;

	@ApiModelProperty(value = "马值")
	private BigDecimal avgMkl;

	@ApiModelProperty(value = "产地")
	private String intactPlace;

	@ApiModelProperty(value = "仓库")
	private String storageWhsName;

	@ApiModelProperty(value = "棉花年度")
	private String productYear;

	@ApiModelProperty(value = "资金监管状态：1、正常 2、三方监管-籽棉贷款 3、三方监管-皮棉调销 4、三方监管-融资监管状态 5、质押监管 6、质押及三方监管")
	@JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
	private DataItem whsSupervisionStatus;

	@ApiModelProperty(value = "监管机构名称")
	private String superviseName;

	@ApiModelProperty(value = "郑商所升贴水")
	private BigDecimal ZSS;

	@ApiModelProperty(value = "备注")
	private String remark;
}

