package com.applet.trade.basisStock.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Title: UpdBasisStockVo.java
 * @Description: 资源维护vo
 * <AUTHOR>
 * @date 2025/6/3
 * @version V1.0
 */
@Data
public class UpdBasisStockVo implements Serializable {

    @ApiModelProperty(value="商品id")
    private String idStr;
    /**
     * 期货合约
     */
    @ApiModelProperty(value="期货合约")
    private String futureCode;

    /**
     *基差价格
     */
    @ApiModelProperty(value = "基差价格")
    private BigDecimal basisPrice;

    /**
     *点价有效期
     */
    @ApiModelProperty(value = "点价有效期")
    private Date pricingValidTime;

    /**
     * 包干费承担方 undertaker_type
     */
    @ApiModelProperty(value = "包干费承担方: 1-卖方 2-买方 3-其它企业")
    private String undertakerType;

    @ApiModelProperty(value = "其它包干方名称")
    private String undertakerOtherName;

    @ApiModelProperty(value = "包干费承担时间")
    private Integer undertakerTime;

    /**
     * 运补申领方 subsidy_apply_type
     */
    @ApiModelProperty(value = "运补申领方: 1-卖方 2-买方 3-其它企业")
    private String subsidyApplyType;

    @ApiModelProperty(value = "其它运补方名称")
    private String subsidyApplyOtherName;

    /**
     * 备注
     */
    @ApiModelProperty(value="备注")
    private String remark;
}
