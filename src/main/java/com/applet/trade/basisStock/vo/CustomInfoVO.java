package com.applet.trade.basisStock.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.sinosoft.dandelion.system.client.params.DataItem;
import com.sinosoft.dandelion.system.client.serializer.DataItemCodeDeSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: CustomInfoVO.java
 * @Description: 用户库客户信息类
 * @date 2024/12/17 上午8:30
 */
@ApiModel(description="v_uc_custom_info")
@Data
public class CustomInfoVO {
    /**
     * 主键
     */
    @Column(name = "ID")
    @ApiModelProperty(value="主键")
    private Long id;

    /**
     * 企业ID
     */
    @Column(name = "ENTERPRISE_ID")
    @ApiModelProperty(value="企业ID")
    private Long enterpriseId;

    /**
     * 上级ID
     */
    @Column(name = "PARENT_CUSTOM_ID")
    @ApiModelProperty(value="上级ID")
    private Long parentCustomId;

    /**
     * 客户名称，大部分情况和企业名称一致或XX公司XX仓储
     */
    @Column(name = "CUSTOM_NAME")
    @ApiModelProperty(value="客户名称，大部分情况和企业名称一致或XX公司XX仓储")
    private String customName;

    /**
     * 客户编码
     */
    @Column(name = "CUSTOM_CODE")
    @ApiModelProperty(value="客户编码")
    private String customCode;

    /**
     * 客户类型，1：交易商，2：仓库，3：物流，4：加工单位，5：办事处，6：监管机构，7：保险公司，8：纤检机构，9：市场，10：市场交易商，11：普通企业，12：其他
     */
    @Column(name = "CUSTOM_TYPE")
    @ApiModelProperty(value="客户类型，1：交易商，2：仓库，3：物流，4：加工单位，5：办事处，6：监管机构，7：保险公司，8：纤检机构，9：市场，10：市场交易商，11：普通企业，12：其他")
    @JSONField(deserializeUsing = DataItemCodeDeSerializer.class)
    private DataItem customType;

    /**
     * 隶属对象类型
     */
    @Column(name = "ATTACHMENT_TYPE")
    @ApiModelProperty(value="隶属对象类型")
    private DataItem attachmentType;

    /**
     * 隶属对象编码
     */
    @Column(name = "ATTACHMENT_CODE")
    @ApiModelProperty(value="隶属对象编码")
    private String attachmentCode;

    /**
     * 隶属对象名称
     */
    @Column(name = "ATTACHMENT_NAME")
    @ApiModelProperty(value="隶属对象名称")
    private String attachmentName;

    /**
     * 隶属区域编码
     */
    @Column(name = "ATTACHMEN_REGION_CODE")
    @ApiModelProperty(value="隶属区域编码")
    private String attachmenRegionCode;

    /**
     * 隶属区域分组编码
     */
    @Column(name = "ATTACHMEN_REGION_GROUP_CODE")
    @ApiModelProperty(value="隶属区域分组编码")
    private String attachmenRegionGroupCode;

    /**
     * 备注
     */
    @Column(name = "REMARKS")
    @ApiModelProperty(value="备注")
    private String remarks;

    /**
     * 删除标记
     */
    @Column(name = "DEL_FLAG")
    @ApiModelProperty(value="删除标记")
    private Integer delFlag;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @Column(name = "CREATE_USER")
    @ApiModelProperty(value="创建人")
    private String createUser;

    /**
     * 创建人标识
     */
    @Column(name = "CREATE_USER_ID")
    @ApiModelProperty(value="创建人标识")
    private String createUserId;

    /**
     * 更新时间
     */
    @Column(name = "UPDATE_TIME")
    @ApiModelProperty(value="更新时间")
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @Column(name = "UPDATE_USER")
    @ApiModelProperty(value="更新人")
    private String updateUser;

    /**
     * 更新人标识
     */
    @Column(name = "UPDATE_USER_ID")
    @ApiModelProperty(value="更新人标识")
    private String updateUserId;

    /**
     * 省份
     */
    @Column(name = "PROVINCE")
    @ApiModelProperty(value="省份")
    private String province;

    /**
     * 客户编码集合
     */
    private List<String> customCodes;
}
