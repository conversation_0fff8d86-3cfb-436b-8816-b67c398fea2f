package com.applet.trade.basisStock.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.sinosoft.dandelion.system.client.params.DataItem;
import com.sinosoft.dandelion.system.client.serializer.DataItemCodeDeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class BasisWhsReceiptVo implements Serializable {

	@ApiModelProperty(value = "批号")
	private String batchNumber;
	
	@ApiModelProperty(value = "仓库名称")
	private String wareHouseName;


	@ApiModelProperty("公定重量（吨）")
	private BigDecimal conditionedWeight;

	@ApiModelProperty(value = "产地")
	private String placeCode;

	@ApiModelProperty(value = "件数：186")
	private Integer quantity;


	private String warehouseReceiptNumber;
	
}