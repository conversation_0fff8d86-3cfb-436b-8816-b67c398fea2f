package com.applet.trade.basisStock.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.applet.trade.base.mappers.SmallBasisStockMappers;
import com.applet.trade.basisStock.vo.UpdBasisStockVo;
import com.basiscotton.base.BasisCottonConstants;
import com.basiscotton.base.entity.BasisStockEntity;
import com.sinosoft.dandelion.system.client.params.DataItem;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.exception.BusinessException;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * @Title: UpdStockBasis.java
 * @Description: 修改商品信息
 * <AUTHOR>
 * @date 2025/6/3
 * @version V1.0
 */
@Service("appStock.updStockBasis.1")
@ApiRequestObject(value = "资源维护-小程序", name = "updStockBasis", groups = {"小程序-基差交易-仓单模块"}, params = {
        @ApiParamMeta(key = "stockInfo", desc = "资源信息", type = UpdBasisStockVo.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "success", desc = "返回结果", type = boolean.class),
        @ApiParamMeta(key = "message", desc = "返回消息", type = String.class),
})
public class UpdStockBasis implements IBusinessService {

    @Resource
    private SmallBasisStockMappers basisStockMappers;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        UpdBasisStockVo stockInfo = context.getValueObject(UpdBasisStockVo.class, "stockInfo");

        LambdaQueryWrapper<BasisStockEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(BasisStockEntity::getId, Arrays.asList(stockInfo.getIdStr().split(",")));
        List<BasisStockEntity> stockList = basisStockMappers.selectList(queryWrapper);

        if(stockList != null && stockList.size() > 0) {
            for (BasisStockEntity stock : stockList) {
                DataItem tradeStatus = stock.getTradeStatus();
                if(tradeStatus != null){
                    if(!BasisCottonConstants.trade_status_1.equals(tradeStatus.getCode()) && !BasisCottonConstants.trade_status_3.equals(tradeStatus.getCode())){
                        throw new BusinessException(Constants.SERVICE_VERIFY_EXCEPTION_CODE, "商品存在报价信息，无法资源维护");
                    }
                }
                DataItem quoteType = stock.getQuoteType();
                if(quoteType != null && BasisCottonConstants.quote_type_1.equals(quoteType.getCode())){
                    this.updFixedStock(stockInfo, stock.getId());
                }else{
                    this.updBasisStock(stockInfo, stock.getId());
                }
            }
        }
        //构造返回数据
        this.createSuccessResponse(context);
        context.getResponseBody().setData("success", true);
        context.getResponseBody().setData("message", "修改成功");
    }

    private void updFixedStock(UpdBasisStockVo stockInfo,Long stockId){
        LambdaUpdateWrapper<BasisStockEntity> updateWrapper = new LambdaUpdateWrapper<BasisStockEntity>();
        updateWrapper.eq(BasisStockEntity::getId, stockId);
        updateWrapper.set(BasisStockEntity::getFutureCode, stockInfo.getFutureCode());
        updateWrapper.set(BasisStockEntity::getRemark, stockInfo.getRemark());
        basisStockMappers.update(null, updateWrapper);
    }

    private void updBasisStock(UpdBasisStockVo stockInfo,Long stockId){
        LambdaUpdateWrapper<BasisStockEntity> updateWrapper = new LambdaUpdateWrapper<BasisStockEntity>();
        updateWrapper.eq(BasisStockEntity::getId, stockId);
        updateWrapper.set(BasisStockEntity::getFutureCode, stockInfo.getFutureCode());
        updateWrapper.set(BasisStockEntity::getStockPrice, stockInfo.getBasisPrice());
        updateWrapper.set(BasisStockEntity::getRemark, stockInfo.getRemark());
        basisStockMappers.update(null, updateWrapper);
    }
}
