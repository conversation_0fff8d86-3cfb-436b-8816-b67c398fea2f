package com.applet.trade.basisStock.service;

import com.applet.trade.base.mappers.SmallBasisStockMappers;
import com.applet.trade.basisStock.vo.TraderAllStockReqVo;
import com.applet.trade.basisStock.vo.TraderAllStockResVo;
import com.basiscotton.base.BasisCottonConstants;
import org.harry.dandelion.framework.core.params.PageParameter;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.rep.support.page.PipPagination;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Title: GetTraderAllStockByPage.java
 * @Description: 仓单资源列表
 * <AUTHOR>
 * @date 2025/6/5
 * @version V1.0
 */
@Service("appStock.getTraderAllStockByPage.1")
@ApiRequestObject(value = "全部资源列表-小程序", name = "getTraderAllStockByPage", groups = {"小程序-基差交易-仓单模块"}, params = {
        @ApiParamMeta(key = "pageParameter", desc = "分页参数", type = PageParameter.class),
        @ApiParamMeta(key = "reqVo", desc = "查询vo", type = TraderAllStockReqVo.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "pagination", desc = "查询分页列表", type = TraderAllStockResVo.class,pagination = true),
})
public class GetTraderAllStockByPage implements IBusinessService {

    @Resource
    private SmallBasisStockMappers basisStockMappers;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        PageParameter pageParameter = context.getValueObject(PageParameter.class, "pageParameter");
        TraderAllStockReqVo reqVo = context.getValueObject(TraderAllStockReqVo.class, "reqVo");
        reqVo.setResourceAuditStatus(BasisCottonConstants.resource_audit_status_1);
        reqVo.setTraderCode(context.getCurrentUserCustomCode());
        PipPagination<TraderAllStockResVo> pipPagination = new PipPagination<>(pageParameter);
        pipPagination = basisStockMappers.getTraderAllStockByPage(pipPagination, reqVo);
        //创建响应实体
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("pagination", pipPagination);
    }

}
