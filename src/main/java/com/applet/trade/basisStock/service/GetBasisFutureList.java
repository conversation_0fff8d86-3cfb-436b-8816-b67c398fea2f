package com.applet.trade.basisStock.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.basiscotton.base.entity.BasisFuturesEntity;
import com.applet.trade.base.mappers.SmallBasisFutureMappers;
import org.harry.dandelion.framework.core.service.IBusinessService;
import org.harry.dandelion.framework.core.service.ServiceHandlerContext;
import org.harry.dandelion.framework.core.service.annonation.ApiParamMeta;
import org.harry.dandelion.framework.core.service.annonation.ApiRequestObject;
import org.harry.dandelion.framework.core.service.annonation.ApiResponseObject;
import org.harry.dandelion.framework.core.utils.StringUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Title: GetBasisFutureList.java
 * @Description: 期货合约集合
 * <AUTHOR>
 * @date 2025/5/23
 * @version V1.0
 */
@Service("appStock.getBasisFutureList.1")
@ApiRequestObject(value = "期货合约集合", name = "getBasisFutureList", groups = {"小程序-基差交易-仓单模块"}, params = {
        @ApiParamMeta(key = "futureCode", desc = "期货合约编码", type = String.class),
})
@ApiResponseObject(params = {
        @ApiParamMeta(key = "futureList", desc = "期货合约集合", type = BasisFuturesEntity.class,  pagination = true),
})
public class GetBasisFutureList implements IBusinessService {

    @Resource
    private SmallBasisFutureMappers basisFutureMappers;

    @Override
    public void doVerify(ServiceHandlerContext context) {

    }

    @Override
    public void doWork(ServiceHandlerContext context) {
        //获取参数
        String futureCode = context.getValueObject(String.class, "futureCode");
        //查询仓库升贴水数据
        LambdaQueryWrapper<BasisFuturesEntity> queryWrapper = Wrappers.lambdaQuery(BasisFuturesEntity.class);
        if(StringUtil.isNotEmpty(futureCode)) {
        	queryWrapper.like(BasisFuturesEntity::getFutureCode, futureCode);
        }
        queryWrapper.orderByDesc(BasisFuturesEntity::getActiveFuture).orderByDesc(BasisFuturesEntity::getFutureCode);
        List<BasisFuturesEntity> futureList = basisFutureMappers.selectList(queryWrapper);
        //创建响应实体
        this.createSuccessResponse(context);
        context.getResponseBody().getDataSet().put("futureList", futureList);
    }

}
