<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.applet.trade.base.mappers.CommonMappers">

    <select id="getCustomInfo" resultType="com.applet.trade.basisStock.vo.CustomInfoVO"
            parameterType="com.applet.trade.basisStock.vo.CustomInfoVO">
        SELECT
        t1.ID,
        t1.ENTERPRISE_ID,
        t1.PARENT_CUSTOM_ID,
        t1.CUSTOM_NAME,
        t1.CUSTOM_CODE,
        t1.CUSTOM_TYPE,
        t1.ATTACHMENT_TYPE,
        t1.ATTACHMENT_CODE,
        t1.ATTACHMENT_NAME,
        t1.ATTACHMEN_REGION_CODE,
        t1.ATTACHMEN_REGION_GROUP_CODE,
        t1.REMARKS,
        t1.DEL_FLAG,
        t1.CREATE_TIME,
        t1.CREATE_USER,
        t1.CREATE_USER_ID,
        t1.UPDATE_TIME,
        t1.UPDATE_USER,
        t1.UPDATE_USER_ID,
        t3.NAME as PROVINCE
        FROM
        v_uc_custom_info t1
        left join v_uc_enterprise_info t2 ON t1.ENTERPRISE_ID = t2.ID
        left join v_uc_region_info  t3 ON t2.ENTERPRISE_REGISTERED_PROVINCE_CODE = t3.ZONE_CODE
        <where>
            <if test="paramVo.customCode != null and paramVo.customCode != ''">
                AND t1.CUSTOM_CODE = #{paramVo.customCode}
            </if>
            <if test="paramVo.customName != null and paramVo.customName != ''">
                AND t1.CUSTOM_NAME like concat('%',#{paramVo.customName},'%')
            </if>
            <if test="paramVo.customType != null and paramVo.customType != ''">
                AND t1.CUSTOM_TYPE = #{paramVo.customType}
            </if>
            <if test="paramVo.customCodes != null and paramVo.customCodes.size() != 0">
                AND t1.CUSTOM_CODE in
                <foreach item="item" collection="paramVo.customCodes" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>