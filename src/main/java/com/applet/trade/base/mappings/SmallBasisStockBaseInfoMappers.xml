<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.applet.trade.base.mappers.SmallBasisStockBaseInfoMappers">

    <!-- 交易商端-查询交易商下仓单信息-->
    <select id="selectBasisWarehouseReceiptByTraderCodes"
            resultType="com.applet.trade.basisStock.vo.BasisWhsReceiptVo"
            parameterType="com.applet.trade.basisStock.vo.BasisWhsReceiptParamVo">
        SELECT
        vwarehouse.BATCH_NUMBER AS batchNumber,
        vwarehouse.CONDITIONED_WEIGHT AS conditionedWeight,
        vwarehouse.PLACE_NAME as placeCode,
        vwarehouse.WHS_CODE AS wareHouseCode,
        vwarehouse.WHS_NAME AS wareHouseName,
        vwarehouse.WAREHOUSE_RECEIPT_NUMBER AS warehouseReceiptNumber
        FROM
        v_xs_warehouse_info vwarehouse
        LEFT JOIN (SELECT * FROM trd_basis_stock WHERE resource_audit_status = 4) AS stock ON vwarehouse.WAREHOUSE_RECEIPT_NUMBER = stock.warehouse_receipt_no
        <where>
            vwarehouse.WAREHOUSE_RECEIPT_ENABLE_STATUS = 2
            AND vwarehouse.WAREHOUSE_RECEIPT_STATUS = 1 AND vwarehouse.STORAGE_STATUS = 1
            AND vwarehouse.WHS_TRANSACTION_STATUS = 1
            AND (vwarehouse.BONDED_STATUS != 1 OR vwarehouse.BONDED_STATUS IS NULL)
            AND (stock.resource_audit_status IS NULL OR stock.resource_audit_status = 4)
            <!--已选择仓单号集合-->
            <if test='paramVo.whsNoList !=null and paramVo.whsNoList != "" '>
                AND vwarehouse.WAREHOUSE_RECEIPT_NUMBER NOT IN
                <foreach item="whsNo" collection="paramVo.whsNoList" index="index" separator="," open="(" close=")">
                    #{whsNo}
                </foreach>
            </if>
            <if test="paramVo.traderCode != null and paramVo.traderCode != ''">
                AND vwarehouse.CURRENT_TRADER_CODE = #{paramVo.traderCode}
            </if>
        </where>
        ORDER BY vwarehouse.BATCH_NUMBER DESC
    </select>

    <select id="getBatchInfoVo" resultType="com.applet.trade.basisStock.vo.BasisBatchInfoVo">
        SELECT
        info.CURRENT_TRADER_CODE as traderCode,
        info.CURRENT_TRADER_NAME as traderName,
        info.BATCH_NUMBER as BATCH_NO,
        info.WAREHOUSE_RECEIPT_NUMBER,
        info.CONDITIONED_WEIGHT,
        weight.GROSS_WEIGHT,
        info.MARK_WEIGHT,
        weight.PACKET_NUM,
        weight.REPOSITORY,
        info.WHS_PICK_MODE,
        weight.PLACE,
        info.PLACE_NAME,
        weight.PROCESS_COMPANY,
        info.FACTORY_CODE as PROCESS_CODE,
        inspect.AVG_LENGTH,
        inspect.AVG_BREAK_RATE,
        inspect.AVG_MKL,
        inspect.MKL_A,
        inspect.MKL_B1,
        inspect.MKL_B2,
        inspect.MKL_C1,
        inspect.MKL_C2,
        inspect.WHITE_COTTON_L1,
        inspect.WHITE_COTTON_L2,
        inspect.WHITE_COTTON_L3,
        inspect.WHITE_COTTON_L4,
        inspect.WHITE_COTTON_L5,
        inspect.WHITE_COTTON_L6,
        inspect.SPOT_COTTON_L1,
        inspect.SPOT_COTTON_L2,
        inspect.SPOT_COTTON_L3,
        inspect.SPOT_COTTON_L4,
        inspect.YELLOW_ISH_COTTON_L1,
        inspect.YELLOW_ISH_COTTON_L2,
        inspect.YELLOW_ISH_COTTON_L3,
        inspect.YELLOW_COTTON_L1,
        inspect.YELLOW_COTTON_L2,
        weight.MOISTURE_RATE,
        weight.IMPURITY_RATE,
        inspect.AVG_LEN_UNIFORMITY,
        inspect.GINNING_QUALITY_RATE_P1,
        inspect.GINNING_QUALITY_RATE_P2,
        inspect.GINNING_QUALITY_RATE_P3,
        weight.INSPECT_DATE,
        info.WAREHOUSE_RECEIPT_STATUS,
        info.PRODUCT_YEAR,
        info.WHS_CODE,
        info.WHS_NAME,
        info.WHS_SUPERVISION_STATUS,
        info.WHS_PLEDGE_STATUS,
        info.WHS_PLEDGE_CHANNEL,
        info.LOAN_COMPANY_CODE,
        info.LOAN_COMPANY_NAME,
        info.STOCK_NAME,
        info.MARK_DESC,
        info.FACTORY_NAME,
        info.COLOR_GRADE_CODE,
        info.MOISTURE_REGAIN,
        info.DIRTY,
        info.TIDY_AVG,
        info.ROLLING_QUALITY_AVG,
        inspect.MAIN_LENGTH,
        inspect.MAIN_MKL,
        info.QUANTITY,
        inspect.MAX_MKL,
        inspect.MIN_MKL
        from
        v_xs_warehouse_info info
        LEFT JOIN v_factory_batch_weight weight ON info.BATCH_NUMBER = weight.BATCH_CODE
        lEFT JOIN v_factory_batch_inspect inspect ON info.BATCH_NUMBER = inspect.BATCH_CODE
        <where>
            info.WAREHOUSE_RECEIPT_NUMBER  IN
            <foreach item="ReceiptNo" collection="warehouseReceiptNumberList" index="index" separator="," open="(" close=")">
                #{ReceiptNo}
            </foreach>
        </where>
        ORDER BY info.WAREHOUSE_RECEIPT_NUMBER DESC
    </select>

    <select id="getWhsReceiptVo" resultType="com.applet.trade.basisStock.vo.BasisWhsReceiptVo">
        SELECT * FROM v_xs_warehouse_info where WAREHOUSE_RECEIPT_NUMBER = #{wareNo}
    </select>

    <select id="getBatchInspectVo" resultType="com.applet.trade.hall.vo.BatchInspectVo">
        SELECT * FROM v_factory_batch_inspect where BATCH_CODE = #{batchNo} ORDER BY UPDATE_TIME DESC LIMIT 1
    </select>

    <select id="getBatchWeightVo" resultType="com.applet.trade.hall.vo.BatchWeightVo">
        SELECT * FROM v_factory_batch_weight where BATCH_CODE = #{batchNo} ORDER BY UPDATE_TIME DESC LIMIT 1
    </select>
</mapper>