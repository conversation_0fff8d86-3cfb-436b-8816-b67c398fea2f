<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.applet.trade.base.mappers.SmallBasisStockMappers">

    <select id="getAllStockByPage"
            parameterType="com.applet.trade.hall.vo.BuyerStockReqVo"
            resultType="com.applet.trade.hall.vo.BuyerStockResVo">
        SELECT
            stock.id,
            stock.stock_code,
            baseinfo.warehouse_receipt_no,
            baseinfo.batch_no,
            baseinfo.COLOR_GRADE,
            baseinfo.AVG_LENGTH,
            baseinfo.avg_break_rate BRUTEFORCE,
            baseinfo.uniformity_average_value,
            baseinfo.impurity_rate,
            baseinfo.moisture_rate,
            baseinfo.AVG_MKL,
            baseinfo.conditioned_weight,
            baseinfo.intact_place,
            baseinfo.storage_whs_name,
            CASE
                WHEN stock.quote_type = 1 THEN 1
                WHEN stock.quote_type != 1 AND stock.pricing_party = 1 THEN 2
                WHEN stock.quote_type != 1 AND stock.pricing_party = 2 THEN 3
                ELSE ''
                END tradeQuoteType,
            stock.future_code,
            CASE
                WHEN stock.quote_type = 1 THEN stock.fixed_price
                ELSE stock.basis_price
                END quotePrice,
            stock.remark
        FROM
            trd_basis_stock stock
            LEFT JOIN trd_basis_stock_base_info baseinfo ON stock.stock_base_info_id = baseinfo.id
        <where>
            <if test="search.resourceAuditStatus !=null and search.resourceAuditStatus !='' ">
                and stock.resource_audit_status = #{search.resourceAuditStatus}
            </if>
            <if test="search.tradeStatus!=null and search.tradeStatus.size > 0">
                AND stock.trade_status in
                <foreach item="status" collection="search.tradeStatus" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <!-- 产地 -->
            <if test="search.intactPlace !=null and search.intactPlace !='' ">
                <bind name="intactPlace" value="'%'+ search.intactPlace +'%'"/>
                and baseinfo.intact_place like #{intactPlace}
            </if>
            <!-- 棉花年度 -->
            <if test="search.productYear !=null and search.productYear !='' ">
                and baseinfo.product_year = #{search.productYear}
            </if>
            <!-- 采摘方式 -->
            <if test="search.whsPickMode !=null and search.whsPickMode !='' ">
                and baseinfo.whs_pick_mode = #{search.whsPickMode}
            </if>
            <!-- 颜色级 -->
            <if test="search.colorGrade!=null and search.colorGrade.size > 0">
                AND baseinfo.COLOR_GRADE in
                <foreach item="color" collection="search.colorGrade" open="(" separator="," close=")">
                    #{color}
                </foreach>
            </if>
            <!-- 颜色级占比筛选 -->
            <if test="search.beginColorRate!=null and search.beginColorRate!= '' ">
                <if test="search.beginColorList!=null and search.beginColorList.size()>0">
                    and
                    <foreach item="bc" collection="search.beginColorList" open="(" separator=" or "
                             close=")" index="index">
                        baseinfo.${bc} >= #{search.beginColorRate}
                    </foreach>
                </if>

            </if>
            <!-- 且 -->
            <if test="search.isJust">
                <if test="search.endColorRate!=null and search.endColorRate!= '' ">
                    <if test="search.endColorList!=null and search.endColorList.size()>0">
                        and
                        <foreach item="ec" collection="search.endColorList" open="(" separator=" or "
                                 close=")" index="index">
                            baseinfo.${ec} &lt;= #{search.endColorRate}
                        </foreach>
                    </if>
                </if>
            </if>
            <!-- 长度 -->
            <if test="search.avgLengthMin !=null and search.avgLengthMin!= '' ">
                and baseinfo.AVG_LENGTH  &gt;= #{search.avgLengthMin}
            </if>
            <if test="search.avgLengthMax !=null and search.avgLengthMax!= '' ">
                and baseinfo.AVG_LENGTH  &gt;= #{search.avgLengthMax}
            </if>
            <!-- 强力 -->
            <if test="search.bruteforceMin !=null and search.bruteforceMin!= '' ">
                and baseinfo.break_value  &gt;= #{search.bruteforceMin}
            </if>
            <if test="search.bruteforceMax !=null and search.bruteforceMax!= '' ">
                and baseinfo.break_value  &gt;= #{search.bruteforceMax}
            </if>
            <!-- 马值 -->
            <if test="search.mainMklMin !=null and search.mainMklMin!= '' ">
                and baseinfo.MAIN_MKL  &gt;= #{search.mainMklMin}
            </if>
            <if test="search.mainMklMax !=null and search.mainMklMax!= '' ">
                and baseinfo.MAIN_MKL  &gt;= #{search.mainMklMax}
            </if>
            <!-- 长整 -->
            <if test="search.tidyAvgMin !=null and search.tidyAvgMin!= '' ">
                and baseinfo.uniformity_average_value  &gt;= #{search.tidyAvgMin}
            </if>
            <if test="search.tidyAvgMax !=null and search.tidyAvgMax!= '' ">
                and baseinfo.uniformity_average_value  &gt;= #{search.tidyAvgMax}
            </if>
            <!-- 含杂 -->
            <if test="search.dirtyMin !=null and search.dirtyMin!= '' ">
                and baseinfo.impurity_rate  &gt;= #{search.dirtyMin}
            </if>
            <if test="search.dirtyMax !=null and search.dirtyMax!= '' ">
                and baseinfo.impurity_rate  &gt;= #{search.dirtyMax}
            </if>
            <!-- 回潮 -->
            <if test="search.moistureRegainMin !=null and search.moistureRegainMin!= '' ">
                and baseinfo.moisture_rate  &gt;= #{search.moistureRegainMin}
            </if>
            <if test="search.moistureRegainMax !=null and search.moistureRegainMax!= '' ">
                and baseinfo.moisture_rate  &gt;= #{search.moistureRegainMax}
            </if>
            <!-- 加工单位 -->
            <if test="search.factoryName !=null and search.factoryName !='' ">
                <bind name="factoryName" value="'%'+ search.factoryName +'%'"/>
                and baseinfo.factory_name like #{factoryName}
            </if>
            <!-- 仓库 -->
            <if test="search.storageWhsName !=null and search.storageWhsName !='' ">
                <bind name="storageWhsName" value="'%'+ search.storageWhsName +'%'"/>
                and baseinfo.storage_whs_name like #{storageWhsName}
            </if>
            <!-- 批号 -->
            <if test="search.batchNo !=null and search.batchNo !='' ">
                <bind name="batchNo" value="'%'+ search.batchNo +'%'"/>
                and baseinfo.batch_no like #{batchNo}
            </if>
        </where>
        ORDER BY stock.create_time DESC
    </select>

    <select id="getAllStockBuyerByPage"
            parameterType="com.applet.trade.hall.vo.BuyerStockReqVo"
            resultType="com.applet.trade.hall.vo.BuyerStockResVo">
        SELECT
            stock.id,
            stock.stock_code,
            baseinfo.warehouse_receipt_no,
            baseinfo.batch_no,
            baseinfo.COLOR_GRADE,
            baseinfo.WHITE_COTTON_L1,
            baseinfo.WHITE_COTTON_L2,
            baseinfo.WHITE_COTTON_L3,
            baseinfo.WHITE_COTTON_L4,
            baseinfo.WHITE_COTTON_L5,
            baseinfo.WHITE_COTTON_L6,
            baseinfo.SPOT_COTTON_L1,
            baseinfo.SPOT_COTTON_L2,
            baseinfo.SPOT_COTTON_L3,
            baseinfo.SPOT_COTTON_L4,
            baseinfo.YELLOW_ISH_COTTON_L1,
            baseinfo.YELLOW_ISH_COTTON_L2,
            baseinfo.YELLOW_ISH_COTTON_L3,
            baseinfo.YELLOW_COTTON_L1,
            baseinfo.YELLOW_COTTON_L2,
            baseinfo.AVG_LENGTH,
            baseinfo.avg_break_rate BRUTEFORCE,
            baseinfo.uniformity_average_value,
            baseinfo.impurity_rate,
            baseinfo.moisture_rate,
            baseinfo.AVG_MKL,
            baseinfo.MAX_MKL,
            baseinfo.MIN_MKL,
            baseinfo.conditioned_weight,
            baseinfo.intact_place,
            baseinfo.storage_whs_name,
            CASE
            WHEN stock.quote_type = 1 THEN 1
            WHEN stock.quote_type != 1 AND stock.pricing_party = 1 THEN 2
            WHEN stock.quote_type != 1 AND stock.pricing_party = 2 THEN 3
            ELSE ''
            END tradeQuoteType,
            stock.future_code,
            CASE
            WHEN stock.quote_type = 1 THEN stock.fixed_price
            ELSE stock.basis_price
            END quotePrice,
            stock.remark,
            quote.trader_code AS quoteTraderCode,
            whsforward.ZMX,
            whsforward.ZSS,
            baseinfo.factory_code,
            baseinfo.factory_name
        FROM
            trd_basis_stock stock
            LEFT JOIN trd_basis_stock_base_info baseinfo ON stock.stock_base_info_id = baseinfo.id
            LEFT JOIN v_whs_factory_forward_batch whsforward ON stock.batch_no = whsforward.batch_code
            LEFT JOIN trd_basis_quote quote ON stock.ID = QUOTE.stock_id AND quote.quote_status = 'succ'
        <where>
            <if test="search.traderCode !=null and search.traderCode !='' ">
                and stock.trader_code != #{search.traderCode}
            </if>
            <if test="search.resourceAuditStatus !=null and search.resourceAuditStatus !='' ">
                and stock.resource_audit_status = #{search.resourceAuditStatus}
            </if>
            <if test="search.tradeStatus!=null and search.tradeStatus.size > 0">
                AND stock.trade_status in
                <foreach item="status" collection="search.tradeStatus" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="search.pricingParty !=null and search.pricingParty !='' ">
                and stock.pricing_party = #{search.pricingParty}
            </if>


            <!-- 产地 -->
            <if test="search.intactPlaceList!=null and search.intactPlaceList.size > 0">
                AND
                <foreach item="intactPlaceStr" collection="search.intactPlaceList" open="(" separator=" OR " close=")">
                    <bind name="intactPlace" value="'%'+ intactPlaceStr +'%'"/>
                    baseinfo.intact_place LIKE #{intactPlace}
                </foreach>
            </if>
            <!-- 棉花年度 -->
            <if test="search.productYear !=null and search.productYear !='' ">
                and baseinfo.product_year = #{search.productYear}
            </if>
            <!-- 采摘方式 -->
            <if test="search.whsPickMode !=null and search.whsPickMode !='' ">
                and baseinfo.whs_pick_mode = #{search.whsPickMode}
            </if>
            <!-- 颜色级 -->
            <if test="search.colorGrade!=null and search.colorGrade.size > 0">
                AND baseinfo.COLOR_GRADE in
                <foreach item="color" collection="search.colorGrade" open="(" separator="," close=")">
                    #{color}
                </foreach>
            </if>
            <!-- 颜色级占比筛选 -->
            <if test="search.beginColorRate!=null and search.beginColorRate!= '' ">
                <if test="search.beginColorList!=null and search.beginColorList.size()>0">
                    and
                    <foreach item="bc" collection="search.beginColorList" open="(" separator=" or "
                             close=")" index="index">
                        baseinfo.${bc} >= #{search.beginColorRate}
                    </foreach>
                </if>

            </if>
            <!-- 且 -->
            <if test="search.isJust">
                <if test="search.endColorRate!=null and search.endColorRate!= '' ">
                    <if test="search.endColorList!=null and search.endColorList.size()>0">
                        and
                        <foreach item="ec" collection="search.endColorList" open="(" separator=" or "
                                 close=")" index="index">
                            baseinfo.${ec} &lt;= #{search.endColorRate}
                        </foreach>
                    </if>
                </if>
            </if>
            <!-- 长度 -->
            <if test="search.avgLengthMin !=null and search.avgLengthMin!= '' ">
                and baseinfo.AVG_LENGTH  &gt;= #{search.avgLengthMin}
            </if>
            <if test="search.avgLengthMax !=null and search.avgLengthMax!= '' ">
                and baseinfo.AVG_LENGTH  &gt;= #{search.avgLengthMax}
            </if>
            <!-- 强力 -->
            <if test="search.bruteforceMin !=null and search.bruteforceMin!= '' ">
                and baseinfo.break_value  &gt;= #{search.bruteforceMin}
            </if>
            <if test="search.bruteforceMax !=null and search.bruteforceMax!= '' ">
                and baseinfo.break_value  &gt;= #{search.bruteforceMax}
            </if>
            <!-- 马值 -->
            <if test="search.mainMklMin !=null and search.mainMklMin!= '' ">
                and baseinfo.MAIN_MKL  &gt;= #{search.mainMklMin}
            </if>
            <if test="search.mainMklMax !=null and search.mainMklMax!= '' ">
                and baseinfo.MAIN_MKL  &gt;= #{search.mainMklMax}
            </if>
            <!-- 长整 -->
            <if test="search.tidyAvgMin !=null and search.tidyAvgMin!= '' ">
                and baseinfo.uniformity_average_value  &gt;= #{search.tidyAvgMin}
            </if>
            <if test="search.tidyAvgMax !=null and search.tidyAvgMax!= '' ">
                and baseinfo.uniformity_average_value  &gt;= #{search.tidyAvgMax}
            </if>
            <!-- 含杂 -->
            <if test="search.dirtyMin !=null and search.dirtyMin!= '' ">
                and baseinfo.impurity_rate  &gt;= #{search.dirtyMin}
            </if>
            <if test="search.dirtyMax !=null and search.dirtyMax!= '' ">
                and baseinfo.impurity_rate  &gt;= #{search.dirtyMax}
            </if>
            <!-- 回潮 -->
            <if test="search.moistureRegainMin !=null and search.moistureRegainMin!= '' ">
                and baseinfo.moisture_rate  &gt;= #{search.moistureRegainMin}
            </if>
            <if test="search.moistureRegainMax !=null and search.moistureRegainMax!= '' ">
                and baseinfo.moisture_rate  &gt;= #{search.moistureRegainMax}
            </if>
            <!-- 加工单位 -->
            <if test="search.factoryName !=null and search.factoryName !='' ">
                <bind name="factoryName" value="'%'+ search.factoryName +'%'"/>
                and baseinfo.factory_name like #{factoryName}
            </if>
            <!-- 仓库 -->
            <if test="search.storageWhsName !=null and search.storageWhsName !='' ">
                <bind name="storageWhsName" value="'%'+ search.storageWhsName +'%'"/>
                and baseinfo.storage_whs_name like #{storageWhsName}
            </if>
            <!-- 批号 -->
            <if test="search.batchNo !=null and search.batchNo !='' ">
                <bind name="batchNo" value="'%'+ search.batchNo +'%'"/>
                and baseinfo.batch_no like #{batchNo}
            </if>
        </where>
        ORDER BY stock.basis_price ASC
    </select>

    <select id="getTraderAllStockByPage"
            parameterType="com.applet.trade.basisStock.vo.TraderAllStockReqVo"
            resultType="com.applet.trade.basisStock.vo.TraderAllStockResVo">
        SELECT
            stock.id,
            stock.stock_code,
            stock.resource_audit_status,
            CASE
            WHEN stock.quote_type = 1 THEN 1
            WHEN stock.quote_type != 1 AND stock.pricing_party = 1 THEN 2
            WHEN stock.quote_type != 1 AND stock.pricing_party = 2 THEN 3
            ELSE ''
            END tradeQuoteType,
            stock.resource_trade_type,
            stock.resource_display_type,
            stock.future_code,
            CASE
            WHEN stock.quote_type = 1 THEN stock.fixed_price
            ELSE stock.basis_price
            END stockPrice,
            stock.pricing_valid_time,
            stock.UNDERTAKER_TYPE,
            stock.UNDERTAKER_TIME,
            stock.subsidy_apply_type,
            baseinfo.warehouse_receipt_no,
            baseinfo.batch_no,
            baseinfo.quantity,
            baseinfo.marks_weight,
            baseinfo.conditioned_weight,
            baseinfo.COLOR_GRADE,
            baseinfo.AVG_LENGTH,
            baseinfo.break_value,
            baseinfo.uniformity_average_value,
            baseinfo.impurity_rate,
            baseinfo.moisture_rate,
            baseinfo.AVG_MKL,
            baseinfo.intact_place,
            baseinfo.storage_whs_name,
            baseinfo.product_year,
            baseinfo.whs_supervision_status,
            baseinfo.supervise_name,
            stock.remark
        FROM
            trd_basis_stock stock
            LEFT JOIN trd_basis_stock_base_info baseinfo ON stock.stock_base_info_id = baseinfo.id
        <where>
            <if test="search.traderCode !=null and search.traderCode !='' ">
                and baseinfo.trader_code = #{search.traderCode}
            </if>
            <if test="search.resourceAuditStatus !=null and search.resourceAuditStatus !='' ">
                and stock.resource_audit_status != #{search.resourceAuditStatus}
            </if>
            <if test="search.tradeStatus!=null and search.tradeStatus.size > 0">
                AND stock.trade_status in
                <foreach item="status" collection="search.tradeStatus" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <!-- 仓单号 -->
            <if test="search.warehouseReceiptNo !=null and search.warehouseReceiptNo !='' ">
                <bind name="warehouseReceiptNo" value="'%'+ search.warehouseReceiptNo +'%'"/>
                and baseinfo.warehouse_receipt_no like #{warehouseReceiptNo}
            </if>
            <!-- 批号 -->
            <if test="search.batchNo !=null and search.batchNo !='' ">
                <bind name="batchNo" value="'%'+ search.batchNo +'%'"/>
                and baseinfo.batch_no like #{batchNo}
            </if>
            <!-- 仓库 -->
            <if test="search.storageWhsName !=null and search.storageWhsName !='' ">
                <bind name="storageWhsName" value="'%'+ search.storageWhsName +'%'"/>
                and baseinfo.storage_whs_name like #{storageWhsName}
            </if>
        </where>
        ORDER BY stock.stock_code DESC
    </select>

    <select id="getManageStockByPage" resultType="com.applet.trade.resources.manage.vo.ManageStockResVo">
        SELECT
            stock.id,
            stock.stock_code,
            stock.resource_audit_status,
            CASE
            WHEN stock.quote_type = 1 THEN 1
            WHEN stock.quote_type != 1 AND stock.pricing_party = 1 THEN 2
            WHEN stock.quote_type != 1 AND stock.pricing_party = 2 THEN 3
            ELSE ''
            END tradeQuoteType,
            stock.resource_trade_type,
            stock.resource_display_type,
            stock.future_code,
            CASE
            WHEN stock.quote_type = 1 THEN stock.fixed_price
            ELSE stock.basis_price
            END stockPrice,
            stock.pricing_valid_time,
            stock.UNDERTAKER_TYPE,
            stock.UNDERTAKER_TIME,
            stock.subsidy_apply_type,
            baseinfo.warehouse_receipt_no,
            baseinfo.batch_no,
            baseinfo.quantity,
            baseinfo.marks_weight,
            baseinfo.conditioned_weight,
            baseinfo.COLOR_GRADE,
            baseinfo.AVG_LENGTH,
            baseinfo.break_value,
            baseinfo.uniformity_average_value,
            baseinfo.impurity_rate,
            baseinfo.moisture_rate,
            baseinfo.AVG_MKL,
            baseinfo.intact_place,
            baseinfo.storage_whs_name,
            baseinfo.product_year,
            baseinfo.whs_supervision_status,
            baseinfo.supervise_name,
            stock.remark,
            baseinfo.trader_code,
            baseinfo.trader_name
        FROM
        trd_basis_stock stock
        LEFT JOIN trd_basis_stock_base_info baseinfo ON stock.stock_base_info_id = baseinfo.id
        <where>
            <if test="search.resourceAuditStatus !=null and search.resourceAuditStatus !='' ">
                and stock.resource_audit_status != #{search.resourceAuditStatus}
            </if>
            <if test="search.tradeStatus!=null and search.tradeStatus.size > 0">
                AND stock.trade_status in
                <foreach item="status" collection="search.tradeStatus" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <!-- 仓单号 -->
            <if test="search.warehouseReceiptNo !=null and search.warehouseReceiptNo !='' ">
                <bind name="warehouseReceiptNo" value="'%'+ search.warehouseReceiptNo +'%'"/>
                and baseinfo.warehouse_receipt_no like #{warehouseReceiptNo}
            </if>
            <!-- 批号 -->
            <if test="search.batchNo !=null and search.batchNo !='' ">
                <bind name="batchNo" value="'%'+ search.batchNo +'%'"/>
                and baseinfo.batch_no like #{batchNo}
            </if>
            <!-- 仓库 -->
            <if test="search.storageWhsName !=null and search.storageWhsName !='' ">
                <bind name="storageWhsName" value="'%'+ search.storageWhsName +'%'"/>
                and baseinfo.storage_whs_name like #{storageWhsName}
            </if>
        </where>
        ORDER BY stock.stock_code DESC
    </select>

    <select id="getAllStock" resultType="com.basiscotton.base.entity.BasisStockEntity">
        SELECT
            stock.*
        FROM
            trd_basis_stock stock
            JOIN trd_basis_stock_base_info baseinfo ON stock.stock_base_info_id = baseinfo.id
        WHERE
            stock.resource_audit_status = 3
            AND stock.trade_status IN (1,2)
            AND quote_type = 2
            AND PRICING_PARTY = 1
            AND pricing_valid_time &lt;= #{pricingValidTime}
    </select>
    <select id="getAllResource" resultType="com.basiscotton.base.entity.BasisStockEntity">
        SELECT
            stock.*
        FROM
            trd_basis_stock stock
                JOIN trd_basis_stock_base_info baseinfo ON stock.stock_base_info_id = baseinfo.id
        WHERE
            stock.resource_audit_status IN (1,2)
          AND stock.trade_status IN (1,2)
          AND quote_type = 2
          AND PRICING_PARTY = 1
          AND pricing_valid_time &lt;= #{pricingValidTime}
    </select>
</mapper>