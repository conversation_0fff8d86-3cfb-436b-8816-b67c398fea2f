<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.applet.trade.base.mappers.SmallProPreSaleMappers">

<!--    <select id="selectResourceListByTraderCode" resultType="com.basiscotton.trade.resources.trader.vo.PreResourceResVo">-->
<!--        SELECT-->
<!--            stock.id,-->
<!--            stock.trader_name,-->
<!--            stock.pre_stock_source,-->
<!--            stock.pre_stock_weight,-->
<!--            stock.future_code,-->
<!--            stock.min_basis_price,-->
<!--            stock.max_basis_price,-->
<!--            baseinfo.min_length,-->
<!--            baseinfo.max_length,-->
<!--            baseinfo.min_break,-->
<!--            baseinfo.max_break,-->
<!--            baseinfo.min_uniformity_average,-->
<!--            baseinfo.max_uniformity_average,-->
<!--            baseinfo.min_impurity,-->
<!--            baseinfo.max_impurity,-->
<!--            baseinfo.min_moisture,-->
<!--            baseinfo.max_moisture,-->
<!--            baseinfo.min_mkl,-->
<!--            baseinfo.max_mkl,-->
<!--            baseinfo.min_color_grade,-->
<!--            baseinfo.max_color_grade,-->
<!--            baseinfo.intact_place,-->
<!--            baseinfo.whs_pick_mode,-->
<!--            stock.supplement_content,-->
<!--            stock.remark,-->
<!--            stock.pricing_valid_time,-->
<!--            stock.delivery_time-->
<!--        FROM-->
<!--            trd_basis_pre_stock stock-->
<!--            JOIN trd_basis_pre_stock_base_info baseinfo ON stock.stock_base_info_id = baseinfo.id-->
<!--        <where>-->
<!--            <if test="paramVo.resourceAuditStatus !=null and paramVo.resourceAuditStatus !='' ">-->
<!--                and stock.resource_audit_status = #{paramVo.resourceAuditStatus}-->
<!--            </if>-->
<!--            &lt;!&ndash; 企业 &ndash;&gt;-->
<!--            <if test="paramVo.traderCode !=null and paramVo.traderCode !='' ">-->
<!--                and stock.trader_code = #{paramVo.traderCode}-->
<!--            </if>-->
<!--            &lt;!&ndash; 资源类型 &ndash;&gt;-->
<!--            <if test="paramVo.preStockSource !=null and paramVo.preStockSource !='' ">-->
<!--                and stock.pre_stock_source = #{paramVo.preStockSource}-->
<!--            </if>-->
<!--        </where>-->
<!--        ORDER BY stock.create_time DESC-->
<!--    </select>-->

<!--    <select id="selectStockListByTraderCode" resultType="com.basiscotton.trade.resources.trader.vo.PreStockResVo">-->
<!--        SELECT-->
<!--            stock.id,-->
<!--            stock.stock_code,-->
<!--            stock.resource_audit_status,-->
<!--            stock.trader_code,-->
<!--            stock.trader_name,-->
<!--            stock.pre_stock_source,-->
<!--            CASE-->
<!--                WHEN stock.quote_type = 1 THEN 1-->
<!--                WHEN stock.quote_type != 1 AND stock.pricing_party = 1 THEN 2-->
<!--                WHEN stock.quote_type != 1 AND stock.pricing_party = 2 THEN 3-->
<!--                ELSE ''-->
<!--            END tradeQuoteType,-->
<!--            stock.resource_display_type,-->
<!--            stock.pre_stock_weight,-->
<!--            stock.future_code,-->
<!--            stock.min_basis_price,-->
<!--            stock.max_basis_price,-->
<!--            baseinfo.min_length,-->
<!--            baseinfo.max_length,-->
<!--            baseinfo.min_break,-->
<!--            baseinfo.max_break,-->
<!--            baseinfo.min_uniformity_average,-->
<!--            baseinfo.max_uniformity_average,-->
<!--            baseinfo.min_impurity,-->
<!--            baseinfo.max_impurity,-->
<!--            baseinfo.min_moisture,-->
<!--            baseinfo.max_moisture,-->
<!--            baseinfo.min_mkl,-->
<!--            baseinfo.max_mkl,-->
<!--            baseinfo.min_color_grade,-->
<!--            baseinfo.max_color_grade,-->
<!--            baseinfo.place_type,-->
<!--            baseinfo.intact_place,-->
<!--            baseinfo.whs_pick_mode,-->
<!--            stock.supplement_content,-->
<!--            stock.remark,-->
<!--            stock.pricing_valid_time,-->
<!--            stock.delivery_time-->
<!--        FROM-->
<!--            trd_basis_pre_stock stock-->
<!--            JOIN trd_basis_pre_stock_base_info baseinfo ON stock.stock_base_info_id = baseinfo.id-->
<!--        <where>-->
<!--            <if test="paramVo.resourceAuditStatus !=null and paramVo.resourceAuditStatus !='' ">-->
<!--                and stock.resource_audit_status != #{paramVo.resourceAuditStatus}-->
<!--            </if>-->
<!--            &lt;!&ndash; 企业 &ndash;&gt;-->
<!--            <if test="paramVo.traderCode !=null and paramVo.traderCode !='' ">-->
<!--                and stock.trader_code = #{paramVo.traderCode}-->
<!--            </if>-->
<!--            &lt;!&ndash; 资源类型 &ndash;&gt;-->
<!--            <if test="paramVo.preStockSource !=null and paramVo.preStockSource !='' ">-->
<!--                and stock.pre_stock_source = #{paramVo.preStockSource}-->
<!--            </if>-->
<!--        </where>-->
<!--        ORDER BY stock.stock_code DESC-->
<!--    </select>-->

    <select id="getSmallProPreSaleStockByPage"
            resultType="com.applet.trade.preSaleResources.preSale.vo.SmallProPreSaleResVo"
            parameterType="com.applet.trade.preSaleResources.preSale.vo.SmallProPreSaleReqVo">
        SELECT
            stock.id,
            stock.stock_code,
            stock.trader_code,
            stock.trader_name,
            stock.pre_stock_source,
            CASE
                WHEN stock.quote_type = 1 THEN 1
                WHEN stock.quote_type != 1 AND stock.pricing_party = 1 THEN 2
                WHEN stock.quote_type != 1 AND stock.pricing_party = 2 THEN 3
                ELSE ''
            END tradeQuoteType,
            stock.resource_display_type,
            stock.pre_stock_weight,
            stock.future_code,
            stock.min_basis_price,
            stock.max_basis_price,
            baseinfo.min_length,
            baseinfo.max_length,
            baseinfo.min_break,
            baseinfo.max_break,
            baseinfo.min_uniformity_average,
            baseinfo.max_uniformity_average,
            baseinfo.min_impurity,
            baseinfo.max_impurity,
            baseinfo.min_moisture,
            baseinfo.max_moisture,
            baseinfo.min_mkl,
            baseinfo.max_mkl,
            baseinfo.min_color_grade,
            baseinfo.max_color_grade,
            baseinfo.intact_place,
            baseinfo.whs_pick_mode,
--             stock.supplement_content,
            stock.pricing_valid_time,
            stock.delivery_time,
            stock.remark,
            quote.trader_code AS quoteTraderCode
        FROM
            trd_basis_pre_stock stock
            JOIN trd_basis_pre_stock_base_info baseinfo ON stock.stock_base_info_id = baseinfo.id
            LEFT JOIN trd_basis_quote quote ON stock.ID = QUOTE.stock_id AND quote.quote_status = 'succ'
        <where>
            <if test="paramVo.traderCode !=null and paramVo.traderCode !='' ">
                and stock.trader_code != #{paramVo.traderCode}
            </if>
            <if test="paramVo.resourceAuditStatus !=null and paramVo.resourceAuditStatus !='' ">
                and stock.resource_audit_status = #{paramVo.resourceAuditStatus}
            </if>
            <if test="paramVo.tradeStatus!=null and paramVo.tradeStatus.size > 0">
                AND stock.trade_status in
                <foreach item="status" collection="paramVo.tradeStatus" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="paramVo.intactPlaceList!=null and paramVo.intactPlaceList.size > 0">
                AND
                <foreach item="intactPlaceStr" collection="paramVo.intactPlaceList" open="(" separator=" OR " close=")">
                    <bind name="intactPlace" value="'%'+ intactPlaceStr +'%'"/>
                    baseinfo.intact_place LIKE #{intactPlace}
                </foreach>
            </if>
            <if test="paramVo.preStockSource !=null and paramVo.preStockSource !='' ">
                and stock.pre_stock_source = #{paramVo.preStockSource}
            </if>
            <!-- 交货时间 -->
            <if test="paramVo.startDeliveryTime !=null and paramVo.startDeliveryTime !='' ">
                and stock.delivery_time &gt;= #{paramVo.startDeliveryTime}
            </if>
            <if test="paramVo.endDeliveryTime !=null and paramVo.endDeliveryTime !='' ">
                and stock.delivery_time &lt;= #{paramVo.endDeliveryTime}
            </if>
        </where>
        ORDER BY stock.create_time DESC
    </select>

<!--    <select id="getPreOrderStockByPage" resultType="com.basiscotton.trade.hall.vo.PreOrderResVo">-->
<!--        SELECT-->
<!--            stock.id,-->
<!--            stock.stock_code,-->
<!--            stock.trader_code,-->
<!--            stock.trader_name,-->
<!--            stock.pre_stock_source,-->
<!--            CASE-->
<!--                WHEN stock.quote_type = 1 THEN 1-->
<!--                WHEN stock.quote_type != 1 AND stock.pricing_party = 1 THEN 2-->
<!--                WHEN stock.quote_type != 1 AND stock.pricing_party = 2 THEN 3-->
<!--            ELSE ''-->
<!--            END tradeQuoteType,-->
<!--            stock.resource_display_type,-->
<!--            stock.pre_stock_weight,-->
<!--            stock.future_code,-->
<!--            stock.min_basis_price,-->
<!--            stock.max_basis_price,-->
<!--            baseinfo.min_length,-->
<!--            baseinfo.max_length,-->
<!--            baseinfo.min_break,-->
<!--            baseinfo.max_break,-->
<!--            baseinfo.min_uniformity_average,-->
<!--            baseinfo.max_uniformity_average,-->
<!--            baseinfo.min_impurity,-->
<!--            baseinfo.max_impurity,-->
<!--            baseinfo.min_moisture,-->
<!--            baseinfo.max_moisture,-->
<!--            baseinfo.min_mkl,-->
<!--            baseinfo.max_mkl,-->
<!--            baseinfo.min_color_grade,-->
<!--            baseinfo.max_color_grade,-->
<!--            baseinfo.intact_place,-->
<!--            baseinfo.whs_pick_mode,-->
<!--            stock.supplement_content,-->
<!--            stock.pricing_valid_time,-->
<!--            stock.delivery_time,-->
<!--            stock.remark,-->
<!--            quote.trader_code AS quoteTraderCode-->
<!--        FROM-->
<!--            trd_basis_pre_stock stock-->
<!--            JOIN trd_basis_pre_stock_base_info baseinfo ON stock.stock_base_info_id = baseinfo.id-->
<!--            LEFT JOIN trd_basis_quote quote ON stock.ID = QUOTE.stock_id AND quote.quote_status = 'succ'-->
<!--        <where>-->
<!--            <if test="paramVo.traderCode !=null and paramVo.traderCode !='' ">-->
<!--                and stock.trader_code != #{paramVo.traderCode}-->
<!--            </if>-->
<!--            <if test="paramVo.resourceAuditStatus !=null and paramVo.resourceAuditStatus !='' ">-->
<!--                and stock.resource_audit_status = #{paramVo.resourceAuditStatus}-->
<!--            </if>-->
<!--            <if test="paramVo.tradeStatus!=null and paramVo.tradeStatus.size > 0">-->
<!--                AND stock.trade_status in-->
<!--                <foreach item="status" collection="paramVo.tradeStatus" open="(" separator="," close=")">-->
<!--                    #{status}-->
<!--                </foreach>-->
<!--            </if>-->
<!--            <if test="paramVo.intactPlaceList!=null and paramVo.intactPlaceList.size > 0">-->
<!--                AND-->
<!--                <foreach item="intactPlaceStr" collection="paramVo.intactPlaceList" open="(" separator=" OR " close=")">-->
<!--                    <bind name="intactPlace" value="'%'+ intactPlaceStr +'%'"/>-->
<!--                    baseinfo.intact_place LIKE #{intactPlace}-->
<!--                </foreach>-->
<!--            </if>-->
<!--            <if test="paramVo.preStockSource !=null and paramVo.preStockSource !='' ">-->
<!--                and stock.pre_stock_source = #{paramVo.preStockSource}-->
<!--            </if>-->
<!--            &lt;!&ndash; 交货时间 &ndash;&gt;-->
<!--            <if test="paramVo.startDeliveryTime !=null and paramVo.startDeliveryTime !='' ">-->
<!--                and stock.delivery_time &gt;= #{paramVo.startDeliveryTime}-->
<!--            </if>-->
<!--            <if test="paramVo.endDeliveryTime !=null and paramVo.endDeliveryTime !='' ">-->
<!--                and stock.delivery_time &lt;= #{paramVo.endDeliveryTime}-->
<!--            </if>-->
<!--        </where>-->
<!--        ORDER BY stock.create_time DESC-->
<!--    </select>-->

<!--    <select id="selectResourceList" resultType="com.basiscotton.trade.resources.manage.vo.MPreResourceResVo">-->
<!--        SELECT-->
<!--            stock.id,-->
<!--            stock.trader_name,-->
<!--            stock.pre_stock_source,-->
<!--            stock.pre_stock_weight,-->
<!--            stock.future_code,-->
<!--            stock.min_basis_price,-->
<!--            stock.max_basis_price,-->
<!--            baseinfo.min_length,-->
<!--            baseinfo.max_length,-->
<!--            baseinfo.min_break,-->
<!--            baseinfo.max_break,-->
<!--            baseinfo.min_uniformity_average,-->
<!--            baseinfo.max_uniformity_average,-->
<!--            baseinfo.min_impurity,-->
<!--            baseinfo.max_impurity,-->
<!--            baseinfo.min_moisture,-->
<!--            baseinfo.max_moisture,-->
<!--            baseinfo.min_mkl,-->
<!--            baseinfo.max_mkl,-->
<!--            baseinfo.min_color_grade,-->
<!--            baseinfo.max_color_grade,-->
<!--            baseinfo.intact_place,-->
<!--            baseinfo.whs_pick_mode,-->
<!--            stock.supplement_content,-->
<!--            stock.remark,-->
<!--            stock.pricing_valid_time,-->
<!--            stock.delivery_time-->
<!--        FROM-->
<!--            trd_basis_pre_stock stock-->
<!--            JOIN trd_basis_pre_stock_base_info baseinfo ON stock.stock_base_info_id = baseinfo.id-->
<!--        <where>-->
<!--            <if test="paramVo.resourceAuditStatus !=null and paramVo.resourceAuditStatus !='' ">-->
<!--                and stock.resource_audit_status = #{paramVo.resourceAuditStatus}-->
<!--            </if>-->
<!--            &lt;!&ndash; 资源类型 &ndash;&gt;-->
<!--            <if test="paramVo.preStockSource !=null and paramVo.preStockSource !='' ">-->
<!--                and stock.pre_stock_source = #{paramVo.preStockSource}-->
<!--            </if>-->
<!--        </where>-->
<!--        ORDER BY stock.create_time DESC-->
<!--    </select>-->

<!--    <select id="selectStockList" resultType="com.basiscotton.trade.resources.manage.vo.MPreStockResVo">-->
<!--        SELECT-->
<!--        stock.id,-->
<!--        stock.stock_code,-->
<!--        stock.resource_audit_status,-->
<!--        stock.trader_code,-->
<!--        stock.trader_name,-->
<!--        stock.pre_stock_source,-->
<!--        CASE-->
<!--        WHEN stock.quote_type = 1 THEN 1-->
<!--        WHEN stock.quote_type != 1 AND stock.pricing_party = 1 THEN 2-->
<!--        WHEN stock.quote_type != 1 AND stock.pricing_party = 2 THEN 3-->
<!--        ELSE ''-->
<!--        END tradeQuoteType,-->
<!--        stock.resource_display_type,-->
<!--        stock.pre_stock_weight,-->
<!--        stock.future_code,-->
<!--        stock.min_basis_price,-->
<!--        stock.max_basis_price,-->
<!--        baseinfo.min_length,-->
<!--        baseinfo.max_length,-->
<!--        baseinfo.min_break,-->
<!--        baseinfo.max_break,-->
<!--        baseinfo.min_uniformity_average,-->
<!--        baseinfo.max_uniformity_average,-->
<!--        baseinfo.min_impurity,-->
<!--        baseinfo.max_impurity,-->
<!--        baseinfo.min_moisture,-->
<!--        baseinfo.max_moisture,-->
<!--        baseinfo.min_mkl,-->
<!--        baseinfo.max_mkl,-->
<!--        baseinfo.min_color_grade,-->
<!--        baseinfo.max_color_grade,-->
<!--        baseinfo.place_type,-->
<!--        baseinfo.intact_place,-->
<!--        baseinfo.whs_pick_mode,-->
<!--        stock.supplement_content,-->
<!--        stock.remark,-->
<!--        stock.pricing_valid_time,-->
<!--        stock.delivery_time-->
<!--        FROM-->
<!--        trd_basis_pre_stock stock-->
<!--        JOIN trd_basis_pre_stock_base_info baseinfo ON stock.stock_base_info_id = baseinfo.id-->
<!--        <where>-->
<!--            <if test="paramVo.resourceAuditStatus !=null and paramVo.resourceAuditStatus !='' ">-->
<!--                and stock.resource_audit_status != #{paramVo.resourceAuditStatus}-->
<!--            </if>-->
<!--            &lt;!&ndash; 资源类型 &ndash;&gt;-->
<!--            <if test="paramVo.preStockSource !=null and paramVo.preStockSource !='' ">-->
<!--                and stock.pre_stock_source = #{paramVo.preStockSource}-->
<!--            </if>-->
<!--        </where>-->
<!--        ORDER BY stock.stock_code DESC-->
<!--    </select>-->

<!--    <select id="getAllStock" resultType="com.basiscotton.trade.base.entity.BasisPreStockEntity">-->
<!--        SELECT-->
<!--            stock.*-->
<!--        FROM-->
<!--            trd_basis_pre_stock stock-->
<!--            JOIN trd_basis_pre_stock_base_info baseinfo ON stock.stock_base_info_id = baseinfo.id-->
<!--        WHERE-->
<!--            stock.resource_audit_status = 3-->
<!--          AND stock.trade_status IN (1,2)-->
<!--          AND quote_type = 2-->
<!--          AND PRICING_PARTY = 1-->
<!--          AND pricing_valid_time &lt;= #{pricingValidTime}-->
<!--    </select>-->
<!--    <select id="getAllResource" resultType="com.basiscotton.trade.base.entity.BasisPreStockEntity">-->
<!--        SELECT-->
<!--            stock.*-->
<!--        FROM-->
<!--            trd_basis_pre_stock stock-->
<!--            JOIN trd_basis_pre_stock_base_info baseinfo ON stock.stock_base_info_id = baseinfo.id-->
<!--        WHERE-->
<!--            stock.resource_audit_status IN (1,2)-->
<!--          AND stock.trade_status IN (1,2)-->
<!--          AND quote_type = 2-->
<!--          AND PRICING_PARTY = 1-->
<!--          AND pricing_valid_time &lt;= #{pricingValidTime}-->
<!--    </select>-->
</mapper>