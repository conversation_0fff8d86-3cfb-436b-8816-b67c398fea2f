package com.applet.trade.base.mappers;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.applet.trade.basisStock.vo.BasisWhsReceiptParamVo;
import com.applet.trade.basisStock.vo.BasisWhsReceiptVo;
import com.applet.trade.hall.vo.BatchInspectVo;
import com.applet.trade.hall.vo.BatchWeightVo;
import com.basiscotton.base.entity.BasisStockBaseInfoEntity;
import com.applet.trade.basisStock.vo.BasisBatchInfoVo;
import org.apache.ibatis.annotations.Param;
import org.harry.dandelion.framework.rep.support.page.PipPagination;

import java.util.List;

/**
 * @Title: SmallBasisBaseStockMapper.java
 * @Description: 商品基础信息mapper
 * <AUTHOR>
 * @date 2025/5/23
 * @version V1.0
 */
public interface SmallBasisStockBaseInfoMappers extends BaseMapper<BasisStockBaseInfoEntity> {
    /**
     * 交易商端-查询交易商下仓单信息
     * @param pipPagination
     * @param paramVo
     * @return
     */
    PipPagination<BasisWhsReceiptVo> selectBasisWarehouseReceiptByTraderCodes(PipPagination<BasisWhsReceiptVo> pipPagination, @Param("paramVo") BasisWhsReceiptParamVo paramVo);

    List<BasisBatchInfoVo> getBatchInfoVo(@Param("warehouseReceiptNumberList")List<String> warehouseReceiptNumberList);

    BasisWhsReceiptVo getWhsReceiptVo(@Param("wareNo") String wareNo);

    BatchInspectVo getBatchInspectVo(@Param("batchNo") String batchNo);

    BatchWeightVo getBatchWeightVo(@Param("batchNo") String batchNo);
}