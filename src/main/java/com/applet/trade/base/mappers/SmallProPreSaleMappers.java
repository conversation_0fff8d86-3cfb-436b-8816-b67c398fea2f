package com.applet.trade.base.mappers;

import com.applet.trade.preSaleResources.preSale.vo.SmallProPreSaleReqVo;
import com.applet.trade.preSaleResources.preSale.vo.SmallProPreSaleResVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.basiscotton.base.entity.BasisPreStockEntity;
import org.apache.ibatis.annotations.Param;
import org.harry.dandelion.framework.rep.support.page.PipPagination;

/**
 * @Author: gyz
 * @Date: 2025-06-11
 */
public interface SmallProPreSaleMappers extends BaseMapper<BasisPreStockEntity> {

    PipPagination<SmallProPreSaleResVo> getSmallProPreSaleStockByPage(PipPagination<SmallProPreSaleResVo> pipPagination, @Param("paramVo") SmallProPreSaleReqVo reqVo);
}
