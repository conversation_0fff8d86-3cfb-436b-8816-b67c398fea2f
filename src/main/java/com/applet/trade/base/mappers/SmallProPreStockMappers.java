package com.applet.trade.base.mappers;

import com.applet.trade.preSaleResources.preSale.vo.SmallProPreStockReqVo;
import com.applet.trade.preSaleResources.preSale.vo.SmallProPreStockResVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.basiscotton.base.entity.BasisPreStockEntity;
import org.apache.ibatis.annotations.Param;
import org.harry.dandelion.framework.rep.support.page.PipPagination;

/**
 * @Author: gyz
 * @Date: 2025-06-12
 */
public interface SmallProPreStockMappers extends BaseMapper<BasisPreStockEntity> {
    PipPagination<SmallProPreStockResVo> selectStockListByTraderCode(PipPagination<SmallProPreStockResVo> pipPagination,@Param("paramVo") SmallProPreStockReqVo paramVo);
}
