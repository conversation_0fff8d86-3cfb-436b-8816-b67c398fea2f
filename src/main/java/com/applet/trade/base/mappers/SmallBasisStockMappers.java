package com.applet.trade.base.mappers;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.basiscotton.base.entity.BasisStockEntity;
import com.applet.trade.hall.vo.BuyerStockReqVo;
import com.applet.trade.hall.vo.BuyerStockResVo;
import com.applet.trade.resources.manage.vo.ManageStockReqVo;
import com.applet.trade.resources.manage.vo.ManageStockResVo;
import com.applet.trade.basisStock.vo.TraderAllStockReqVo;
import com.applet.trade.basisStock.vo.TraderAllStockResVo;
import org.apache.ibatis.annotations.Param;
import org.harry.dandelion.framework.rep.support.page.PipPagination;

import java.util.List;

/**
 * @Title: SmallBasisSpotStockMapper.java
 * @Description: 现货商品表mapper
 * <AUTHOR>
 * @date 2025/5/23
 * @version V1.0
 */
public interface SmallBasisStockMappers extends BaseMapper<BasisStockEntity> {

    PipPagination<BuyerStockResVo> getAllStockByPage(PipPagination<BuyerStockResVo> pipPagination, @Param("search") BuyerStockReqVo reqVo);

    PipPagination<BuyerStockResVo> getAllStockBuyerByPage(PipPagination<BuyerStockResVo> pipPagination, @Param("search") BuyerStockReqVo reqVo);

    PipPagination<TraderAllStockResVo> getTraderAllStockByPage(PipPagination<TraderAllStockResVo> pipPagination,@Param("search") TraderAllStockReqVo reqVo);

    PipPagination<ManageStockResVo> getManageStockByPage(PipPagination<ManageStockResVo> pipPagination,@Param("search") ManageStockReqVo reqVo);

    List<BasisStockEntity> getAllStock(@Param("pricingValidTime")String pricingValidTime);

    List<BasisStockEntity> getAllResource(@Param("pricingValidTime")String pricingValidTime);
}