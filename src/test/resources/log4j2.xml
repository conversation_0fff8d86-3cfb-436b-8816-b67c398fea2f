<?xml version="1.0" encoding="utf-8"?>
<!-- 开发环境日志配置 -->
<configuration status="OFF">
    <Properties>
		 <!--服务名-->
        <property name="SERVER_NAME">basiscotton-trade-service-system</property>
        <!-- 日志输出级别 root-log-level 控制所有引用框架的日志级别  project-log-level 控制平台及当前项目日志级别-->
        <property name="root-log-level">INFO</property>
        <property name="project-log-level">DEBUG</property>
		<!--日志输出格式-->
        <property name="PATTERN">${logid:-bootstrap%d{yyyyMMddHHmm}},${SERVER_NAME},[%-5level],%d{yyyy-MM-dd HH:mm:ss.SSS},[%c:%L], %msg%n</property>
    </Properties>
	<Appenders>
		<!--控制台的配置-->
		<Console name="Console" target="SYSTEM_OUT">
			<!--控制台只输出level及以上级别的信息（onMatch），其他的直接拒绝（onMismatch）-->
			<ThresholdFilter level="trace" onMatch="ACCEPT" onMismatch="DENY"/>
			<PatternLayout pattern="${PATTERN}" />
		</Console>
		<Async name="AsyncCONSOLE">
            <AppenderRef ref="Console" />
        </Async>
	</Appenders>
	<Loggers>
		<!-- 项目日志基本控制 -->
        <AsyncLogger name="org.harry" level="${project-log-level}"/>
        <AsyncLogger name="com.sinosoft" level="${project-log-level}"/>
        <AsyncLogger name="com.cottoneasy" level="${project-log-level}"/>
        <AsyncLogger name="com.basiscotton" level="${logLevel}" />
        <!-- 整体日志级别控制 -->
        <asyncRoot level="${root-log-level}">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="AsyncCONSOLE"/>
        </asyncRoot>
    </Loggers>
    
</configuration>