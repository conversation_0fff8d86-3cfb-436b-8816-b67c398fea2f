package com.basiscotton.trade.commom;

import com.alibaba.fastjson.JSONObject;
import com.basiscotton.trade.TestTradeServiceSystemApplication;
import com.basiscotton.trade.commom.entity.LoginFormParams;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.mgt.SecurityManager;
import org.harry.dandelion.framework.core.message.response.ResponseMessage;
import org.harry.dandelion.framework.security.pac4j.http.extractor.RSAFormExtractor;
import org.harry.dandelion.framework.security.pac4j.shiro.filter.CallbackFilter;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.pac4j.core.config.Config;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpSession;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = TestTradeServiceSystemApplication.class,webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class JwtTokenLoginTest {

	protected MockMvc mockMvc;

    @Autowired
    protected WebApplicationContext wac;
    
    protected MockHttpSession session;
    
    @Resource
    protected SecurityManager securityManager;

    @Resource
    protected RSAFormExtractor  extractor;
    
    @Resource
    protected Config pac4jConfig;
	
    @Before()  //这个方法在每个方法执行之前都会执行一遍
    public void setup() {
    	SecurityUtils.setSecurityManager(securityManager);
        mockMvc = MockMvcBuilders.webAppContextSetup(wac)
        						 .addFilters(callbackFilter(pac4jConfig))
        						 .build();  //初始化MockMvc对象
    }
    
    public CallbackFilter callbackFilter(Config pac4jConfig) {
		CallbackFilter filter = new CallbackFilter();
		filter.setConfig(pac4jConfig);
		filter.setMultiProfile(true);
		return filter;
	}
    
    public String login(String username,String password) {
    	try {
    		session = new MockHttpSession();
        	String loginParamsValue = extractor.buildLoginParams("admin", "111111");
        	LoginFormParams params = new LoginFormParams(loginParamsValue);
        	MvcResult result = this.mockMvc.perform(MockMvcRequestBuilders.post("/callback?client_name=form_client")
        											   .contentType(MediaType.APPLICATION_JSON)
        											   .header("is_ajax_request", "true")
    									               .content(JSONObject.toJSONString(params))
    									               .session(session)
    			              	)
    			                .andExpect(MockMvcResultMatchers.status().isOk())
    			                .andDo(MockMvcResultHandlers.print())
    			                .andReturn();
        	String message = result.getResponse().getContentAsString();
        	ResponseMessage responseMessage = JSONObject.parseObject(message, ResponseMessage.class);
        	return responseMessage.getBody().getDataSet().get("token").toString();
    	}catch(Exception e) {
    		log.error("登录失败",e);
    		return null;
    	}
    }
}
