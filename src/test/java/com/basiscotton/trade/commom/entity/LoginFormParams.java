package com.basiscotton.trade.commom.entity;

import java.io.Serializable;

import com.alibaba.fastjson.annotation.JSONField;

import lombok.Data;

@Data
public class LoginFormParams implements Serializable {

	private static final long serialVersionUID = 4539091211665437055L;
	
	@JSONField(name="Authorization")
	private String authorization;
	
	public LoginFormParams() {}
	
	public  LoginFormParams(String Authorization) {
		this.authorization = Authorization;
	}
}
