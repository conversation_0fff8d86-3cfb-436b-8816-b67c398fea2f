package com.basiscotton.trade.commom;

import java.util.Map;

import javax.annotation.Resource;

import com.alibaba.fastjson.TypeReference;
import org.apache.shiro.SecurityUtils;
import org.harry.dandelion.framework.core.common.Constants;
import org.harry.dandelion.framework.core.message.IMessageFactory;
import org.harry.dandelion.framework.core.service.IServiceDispatcher;
import org.harry.dandelion.framework.security.pac4j.shiro.filter.SecurityFilter;
import org.junit.Before;
import org.pac4j.core.config.Config;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

public class BasicTest extends JwtTokenLoginTest{

	protected MockMvc restMockMvc;

    @Resource
    protected Config pac4jConfig;
    
	@Resource
	private IMessageFactory messageFactory;
	
	@Resource
	private IServiceDispatcher dispatcher;
	
	@Before()  //这个方法在每个方法执行之前都会执行一遍
    public void setup() {
		super.setup();
    	SecurityUtils.setSecurityManager(securityManager);
    	restMockMvc = MockMvcBuilders.webAppContextSetup(wac)
        						 .addFilters(formFilter(pac4jConfig))
        						 .build();  //初始化MockMvc对象
    }
    
	public SecurityFilter formFilter(Config pac4jConfig) {
		SecurityFilter filter = new SecurityFilter();
		filter.setConfig(pac4jConfig);
		filter.setClients("form_client,header_client,jwt_client");
		filter.setAuthorizers("ShiroProfileAuthorizer");
		filter.setName("basicAuthc");
		filter.setMultiProfile(true);
		return filter;
	}
	
	public Object runTest(String url,Map<String,Object> param) throws Exception{
		String token = this.login("admin", "123456");
		
		ResultActions resultActions = this.restMockMvc.perform(MockMvcRequestBuilders.post(url)
					   .contentType(MediaType.APPLICATION_JSON)
					   .header("is_ajax_request", "true")
					   .header("authorization", token)
					   .content(param!=null?JSON.toJSONString(param):"{}")
		               .session(this.session)
		);
		
		resultActions.andReturn().getResponse().setCharacterEncoding("UTF-8");
		MvcResult result = resultActions.andExpect(MockMvcResultMatchers.status().isOk())
		.andDo(MockMvcResultHandlers.print())
		.andReturn();
		String response = result.getResponse().getContentAsString();
		return BizResponse(response);
	}
	
	public Object BizResponse(String data) {
		Map<String, Map<String, String>> stringMapMap = JSONObject.parseObject(data ,new TypeReference<Map<String, Map<String, String>>>() {
		});
		JSONObject jsonObject = JSONObject.parseObject(data);
		JSONObject body = jsonObject.getJSONObject("body");
		String rtnCode = body.getString("rtnCode");
//		ResponseMessage reponseMessage = JSON.parseObject(data, ResponseMessage.class);
		if(rtnCode.equals(Constants.succeedCode)) {
//			Map<String,Object> dataset = reponseMessage.getBody().getDataSet();
			String dataSet2 = stringMapMap.get("body").get("dataSet");

			return  dataSet2;
		}else {
			return rtnCode;
		}
	}
}
